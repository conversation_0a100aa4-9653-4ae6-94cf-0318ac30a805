# NeuroLoopApp API Documentation

## Table of Contents
- [Introduction](#introduction)
- [Core APIs](#core-apis)
  - [Affirmation Management](#affirmation-management)
  - [Repetition Tracking](#repetition-tracking)
  - [Streak and Progress](#streak-and-progress)
  - [Settings and Configuration](#settings-and-configuration)
  - [Premium Features](#premium-features)
- [Data Models](#data-models)
  - [Affirmation](#affirmation)
  - [RepetitionCycle](#repetitioncycle)
  - [SubscriptionStatus](#subscriptionstatus)
- [Integration Points](#integration-points)
- [Usage Examples](#usage-examples)
- [Error Handling](#error-handling)
- [Troubleshooting Guide](#troubleshooting-guide)
- [Glossary](#glossary)

---

## Introduction

NeuroLoopApp exposes a set of public APIs and data models for managing affirmations, repetitions, streaks, settings, and premium features. This documentation is intended for developers integrating with or extending the app, as well as for internal development teams.

- **Language:** Swift
- **Audience:** Internal and external developers
- **Conventions:** All public interfaces are marked with `public` and follow Swift API design guidelines.

---

## Core APIs

### Affirmation Management

#### `AffirmationServiceProtocol`
```swift
@available(iOS 17.0, macOS 14.0, *)
public protocol AffirmationServiceProtocol {
    func fetchAffirmations() async throws -> [AffirmationProtocol]
    func fetchAffirmation(id: UUID) async throws -> AffirmationProtocol?
    func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?) async throws -> AffirmationProtocol
    func updateAffirmation(_ affirmation: AffirmationProtocol) async throws
    func deleteAffirmation(id: UUID) async throws
    func fetchAffirmations(category: AffirmationCategory) async throws -> [AffirmationProtocol]
    func fetchFavoriteAffirmations() async throws -> [AffirmationProtocol]
    func fetchCurrentAffirmation() async throws -> AffirmationProtocol?
    func toggleFavorite(_ affirmation: AffirmationProtocol) async throws -> AffirmationProtocol
    func startCycle(for affirmation: AffirmationProtocol) async throws
    func recordRepetition(for affirmation: AffirmationProtocol) async throws -> AffirmationProtocol
    func updateEnergyLevel(_ level: Double, for affirmation: AffirmationProtocol) async throws -> AffirmationProtocol
    func recordMood(_ rating: Int, notes: String?, for affirmation: AffirmationProtocol) async throws -> AffirmationProtocol
}
```

#### `AffirmationRepositoryProtocol`
```swift
@available(macOS 10.15, iOS 13.0, *)
public protocol AffirmationRepositoryProtocol {
  func fetchAffirmations() async throws -> [AffirmationProtocol]
  func fetchAffirmation(id: UUID) async throws -> AffirmationProtocol?
  func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?) async throws -> AffirmationProtocol
  func updateAffirmation(_ affirmation: AffirmationProtocol) async throws
  func deleteAffirmation(id: UUID) async throws
  func fetchAffirmations(category: AffirmationCategory) async throws -> [AffirmationProtocol]
  func fetchFavoriteAffirmations() async throws -> [AffirmationProtocol]
  func fetchCurrentAffirmation() async throws -> AffirmationProtocol?
  func recordRepetition(for affirmation: AffirmationProtocol) async throws
  func startCycle(for affirmation: AffirmationProtocol) async throws
}
```

---

### Repetition Tracking

#### `RepetitionService`
Provides methods for recording repetitions, checking progress, and managing cycles.

```swift
public func recordRepetition(for affirmation: AffirmationProtocol) async throws -> RepetitionResult
public func getProgress(for affirmation: AffirmationProtocol) -> ProgressInfo
public func getStreakInfo(for affirmation: AffirmationProtocol) -> StreakInfo
public func canPerformRepetition(for affirmation: AffirmationProtocol) -> Bool
public func timeUntilNextRepetition(for affirmation: AffirmationProtocol) -> TimeInterval?
```

---

### Streak and Progress

#### `StreakService`
Provides methods for tracking streaks and progress for affirmations.

#### `ProgressInfo` and `StreakInfo`
See [Data Models](#data-models) for struct definitions.

---

### Settings and Configuration

#### `SettingsViewModel`
Manages user settings such as theme, notifications, reminders, haptics, sound, daily goals, and premium status.

```swift
@available(iOS 17.0, macOS 14.0, *)
public class SettingsViewModel: ObservableObject {
    @Published var isDarkModeEnabled: Bool
    @Published var notificationsEnabled: Bool
    @Published var reminderTime: Date
    @Published var hapticFeedbackEnabled: Bool
    @Published var soundEffectsEnabled: Bool
    @Published var dailyGoal: Int
    @Published private(set) var isPremiumUser: Bool
    public func restoreDefaults()
    public func purchasePremium() async throws
    public func restorePurchases() async throws
    public func exportData() async throws -> URL
    public func deleteAllData() async throws
}
```

---

### Premium Features

#### `PremiumServiceProtocol`
```swift
public protocol PremiumServiceProtocol {
    var isPremium: Bool { get }
    var isPremiumPublisher: AnyPublisher<Bool, Never> { get }
    func checkFeatureAvailability(_ feature: PremiumFeature) -> Bool
    func purchaseSubscription() async throws
    func restorePurchases() async throws
    func getSubscriptionStatus() async throws -> SubscriptionStatus
}
```

#### `PremiumFeature` Enum
- `.unlimitedAffirmations`
- `.customThemes`
- `.advancedAnalytics`
- `.dataExport`

---

## Data Models

### Affirmation
```swift
@available(iOS 17.0, macOS 14.0, *)
public final class Affirmation: AffirmationProtocol {
    public var id: UUID
    public var text: String
    public var category: AffirmationCategory
    public var recordingURL: URL?
    public var createdAt: Date
    public var updatedAt: Date
    public var completedCycles: Int
    public var currentRepetitions: Int
    public var lastRepetitionDate: Date?
    public var energyLevel: Double
    public var moodRating: Int?
    public var notes: String?
    public var isFavorite: Bool
    public var playCount: Int
    public var hasActiveCycle: Bool
    public var currentCycleDay: Int
    public var cycleStartDate: Date?
    public var dailyProgress: [Date: Int]
    public var isCurrentCycleComplete: Bool
    public var todayProgress: Double
    public var cycleProgress: Double
    public var hasTodayQuotaMet: Bool
    public var canPerformRepetition: Bool
}
```

### RepetitionCycle
```swift
@available(iOS 17.0, macOS 14.0, *)
public final class RepetitionCycle {
    public var id: UUID
    public var startDate: Date
    public var currentDay: Int
    public var isActive: Bool
    public var isComplete: Bool
    public var completionDate: Date?
    public var dailyProgress: [Date: Int]
    public var affirmationId: UUID?
    public var progress: Double
    public var todayProgress: Double
    public var hasTodayQuotaMet: Bool
    public var totalRepetitions: Int
}
```

### SubscriptionStatus
```swift
public struct SubscriptionStatus {
    public let isActive: Bool
    public let expirationDate: Date?
    public let productId: String?
}
```

---

## Integration Points

- **Data Import/Export:** `SettingsViewModel.exportData()` and `SettingsViewModel.deleteAllData()`
- **Notification Handling:** `NotificationServiceProtocol` for scheduling and handling notifications
- **Deep Linking:** Supported via notification `userInfo` dictionary (e.g., `deepLink` key)
- **Extension Points:** All protocols (e.g., `AffirmationServiceProtocol`, `PremiumServiceProtocol`) can be implemented for custom integrations

---

## Usage Examples

### Creating an Affirmation
```swift
let service: AffirmationServiceProtocol = ...
let newAffirmation = try await service.createAffirmation(text: "I am focused", category: .custom, recordingURL: nil)
```

### Recording a Repetition
```swift
let updatedAffirmation = try await service.recordRepetition(for: affirmation)
```

### Checking Progress
```swift
let progress = repetitionService.getProgress(for: affirmation)
print(progress.todayProgress)
```

### Managing Settings
```swift
let settings = SettingsViewModel()
settings.isDarkModeEnabled = true
try await settings.exportData()
```

### Handling Premium Features
```swift
let premiumService: PremiumServiceProtocol = ...
let status = try await premiumService.getSubscriptionStatus()
if status.isActive { /* unlock features */ }
```

---

## Error Handling

- All async methods throw errors on failure.
- Common error types: `AffirmationError`, `AppStoreError`, standard Swift errors.
- Use `do-catch` blocks to handle errors.

### Example
```swift
do {
    let result = try await service.createAffirmation(...)
} catch AffirmationError.cannotPerformRepetition {
    // Handle specific error
} catch {
    // Handle general error
}
```

---

## Troubleshooting Guide
- Ensure all required parameters are provided.
- Check for network connectivity for cloud or premium features.
- Review error messages for validation or quota issues.
- For deep linking, verify the format and validity of IDs.

---

## Glossary
- **Affirmation:** A positive statement tracked by the app.
- **Repetition:** An instance of practicing an affirmation.
- **Cycle:** A 7-day period for tracking affirmation progress.
- **Streak:** Consecutive days of meeting repetition goals.
- **Premium:** Subscription-based features and content.

---

> For further questions or integration support, contact the NeuroLoopApp development team. 
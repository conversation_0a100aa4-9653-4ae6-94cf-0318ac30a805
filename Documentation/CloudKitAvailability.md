# CloudKit Availability Guide

# Developer Note

**REMINDER:** Any new service that uses CloudKit/CKContainer should use the `safeContainer` pattern for robust initialization and error handling.

- See `CloudKitDataExportService` or `CloudKitSyncService` for an example implementation.
- This prevents crashes in previews, tests, or when iCloud is unavailable.

## Overview

NeuroLoopApp uses CloudKit for data synchronization and backup features. During development or when the Apple Developer account is in renewal, CloudKit features can be temporarily disabled using a global flag.

## Controlling CloudKit Availability

### Disabling CloudKit

To disable CloudKit features (for development, testing, or during Apple Developer account renewal):

```swift
ServiceFactory.cloudKitAvailable = false
```

When CloudKit is disabled:
- A user-facing message will appear in the Settings sync section
- All CloudKit-dependent features will use a mock implementation
- The app will not crash when CloudKit services are unavailable

### Enabling CloudKit

Before submitting to the App Store, ensure CloudKit is enabled:

```swift
ServiceFactory.cloudKitAvailable = true
```

## Implementation Details

- The flag is implemented as a static property on `ServiceFactory`
- When disabled, `MockDataExportService` is used instead of `CloudKitDataExportService`
- User-facing messaging in Settings informs users when sync features are unavailable
- The implementation is designed to be temporary and easily reversible

## Important Reminders

- **ALWAYS** set `ServiceFactory.cloudKitAvailable = true` before final App Store builds
- Test the app with both CloudKit enabled and disabled to ensure all features work correctly
- The mock implementation preserves the app's functionality but does not actually sync data

## Troubleshooting

If CloudKit features are not working as expected:
1. Verify the Apple Developer account is active and not in renewal
2. Check that the app has the proper CloudKit entitlements
3. Ensure the CloudKit container identifier is correct
4. Verify that `ServiceFactory.cloudKitAvailable` is set to `true`
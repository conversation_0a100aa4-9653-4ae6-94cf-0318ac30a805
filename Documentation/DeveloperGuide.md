# NeuroLoopApp Developer Guide

## Introduction

NeuroLoopApp is a SwiftUI application designed to help users internalize positive affirmations through consistent practice. The app follows a specific, research-based formula:

- **100 Repetitions**: Each affirmation must be repeated 100 times
- **7-Day Cycle**: Repetitions are performed over a 7-day period
- **Conscious Practice**: Each repetition is done mindfully and intentionally

This developer guide provides comprehensive documentation for developers working on the NeuroLoopApp codebase.

## Project Structure

The project is organized as a Swift Package with multiple modules:

- **NeuroLoopInterfaces**: Core protocols and interfaces
- **NeuroLoopModels**: Data models and entities
- **NeuroLoopCore**: Business logic and services
- **NeuroLoopUI**: Reusable UI components
- **NeuroLoopShared**: Shared utilities and helpers
- **NeuroLoopApp**: Main app module with views and view models
- **NeuroLoopRunner**: Executable target to run the app

## Architecture

The app follows the MVVM (Model-View-ViewModel) architecture pattern:

- **Models**: Data structures and business logic
- **Views**: UI components and screens
- **ViewModels**: State management and business logic for views

### Dependency Injection

The app uses dependency injection to provide services to view models. This makes the code more testable and modular. Services are injected through constructors, allowing for easy mocking in tests.

Example:
```swift
public init(
    affirmationService: AffirmationServiceProtocol,
    repetitionService: RepetitionServiceProtocol,
    streakService: StreakServiceProtocol,
    audioService: AudioRecordingServiceProtocol
) {
    self.affirmationService = affirmationService
    self.repetitionService = repetitionService
    self.streakService = streakService
    self.audioService = audioService
}
```

## Core Modules

### NeuroLoopInterfaces

This module contains protocols that define the contracts for the app's components. Key interfaces include:

#### AffirmationProtocol

```swift
public protocol AffirmationProtocol {
    var id: UUID { get }
    var text: String { get set }
    var category: AffirmationCategory { get }
    var recordingURL: URL? { get set }
    var createdAt: Date { get }
    var updatedAt: Date { get set }
    var currentCycleDay: Int { get set }
    var cycleStartDate: Date? { get set }
    var completedCycles: Int { get set }
    var currentRepetitions: Int { get set }
    var lastRepetitionDate: Date? { get }
    var energyLevel: Double { get }
    var moodRating: Int? { get }
    var notes: String? { get }
    var isFavorite: Bool { get set }
    var playCount: Int { get set }
    var hasActiveCycle: Bool { get set }
    
    func recordRepetition() throws
    func updateEnergyLevel(_ level: Double)
    func recordMood(_ rating: Int, notes: String?)
}
```

#### AffirmationRepositoryProtocol

```swift
public protocol AffirmationRepositoryProtocol {
    func fetchAffirmations() async throws -> [AffirmationProtocol]
    func fetchAffirmation(id: UUID) async throws -> AffirmationProtocol?
    func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?) async throws -> AffirmationProtocol
    func updateAffirmation(_ affirmation: AffirmationProtocol) async throws
    func deleteAffirmation(id: UUID) async throws
    func fetchAffirmations(category: AffirmationCategory) async throws -> [AffirmationProtocol]
    func fetchFavoriteAffirmations() async throws -> [AffirmationProtocol]
    func fetchCurrentAffirmation() async throws -> AffirmationProtocol?
    func recordRepetition(for affirmation: AffirmationProtocol) async throws
    func startCycle(for affirmation: AffirmationProtocol) async throws
}
```

### NeuroLoopModels

This module contains the data models used throughout the app. The primary model is the `Affirmation` class, which implements the `AffirmationProtocol`.

#### Affirmation

The `Affirmation` class is a SwiftData model that represents an affirmation in the app. It includes properties for tracking repetitions, cycles, and other metadata.

```swift
@Model
public final class Affirmation: AffirmationProtocol {
    public var id: UUID
    public var text: String
    public var categoryRawValue: String
    public var recordingURLString: String?
    public var createdAt: Date
    public var updatedAt: Date
    public var completedCycles: Int
    public var currentRepetitions: Int
    public var lastRepetitionDateValue: Date?
    public var energyLevel: Double
    public var moodRatingValue: Int?
    public var notes: String?
    public var isFavorite: Bool
    public var playCount: Int
    public var hasActiveCycle: Bool
    public var currentCycleDayValue: Int = 1
    public var cycleStartDateValue: Date? = nil
    
    // Relationships
    public var cycles: [RepetitionCycle] = []
    
    // Implementation of AffirmationProtocol methods...
}
```

#### RepetitionCycle

The `RepetitionCycle` class represents a 7-day cycle for an affirmation.

```swift
@Model
public final class RepetitionCycle {
    public var id: UUID
    public var startDate: Date
    public var currentDay: Int
    public var isActive: Bool
    public var dailyProgress: [Date: Int]
    
    // Relationship
    public var affirmation: Affirmation?
    
    // Implementation...
}
```

### NeuroLoopCore

This module contains the business logic and services for the app.

#### AffirmationService

The `AffirmationService` class provides methods for managing affirmations, including creating, updating, and deleting affirmations, as well as managing repetition cycles.

```swift
public class AffirmationService {
    private let repository: AffirmationRepositoryProtocol
    
    public init(repository: AffirmationRepositoryProtocol) {
        self.repository = repository
    }
    
    public func fetchAffirmations() async throws -> [AffirmationProtocol] {
        return try await repository.fetchAffirmations()
    }
    
    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL? = nil) async throws -> AffirmationProtocol {
        return try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )
    }
    
    // Other methods...
}
```

#### SwiftDataAffirmationRepository

The `SwiftDataAffirmationRepository` class implements the `AffirmationRepositoryProtocol` using SwiftData for persistence.

```swift
public class SwiftDataAffirmationRepository: AffirmationRepositoryProtocol {
    private let modelContext: ModelContext
    
    public init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    public func fetchAffirmations() async throws -> [AffirmationProtocol] {
        let descriptor = FetchDescriptor<Affirmation>()
        return try modelContext.fetch(descriptor)
    }
    
    // Other methods...
}
```

### NeuroLoopUI

This module contains reusable UI components for the app.

#### AffirmationCard

The `AffirmationCard` component displays an affirmation with its progress.

```swift
public struct AffirmationCard: View {
    private let affirmation: AffirmationProtocol
    private let theme: CardTheme
    private let onTap: () -> Void
    private let onFavorite: () -> Void
    
    public var body: some View {
        // Implementation...
    }
}
```

#### EnhancedAffirmationCard

The `EnhancedAffirmationCard` component is an improved version of the `AffirmationCard` with additional features and animations.

```swift
public struct EnhancedAffirmationCard: View {
    private let affirmation: AffirmationProtocol
    private let theme: Theme
    private let onTap: () -> Void
    private let onFavorite: () -> Void
    @State private var isPressed: Bool = false
    
    public var body: some View {
        // Implementation...
    }
}
```

## Key Features

### Affirmation Management

The app allows users to create, edit, and delete affirmations. Each affirmation has a text, category, and optional audio recording.

#### Creating an Affirmation

```swift
// In a view model
public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL? = nil) async {
    isLoading = true
    error = nil

    do {
        let newAffirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )

        // Reload affirmations to ensure we have the latest data
        await loadAffirmations()

        // If this is the first affirmation, set it as current
        if currentAffirmation == nil {
            currentAffirmation = newAffirmation
            try await startCycle(for: newAffirmation)
        }

        await hapticManager.success()
        await loadStatistics()
    } catch {
        self.error = error
        await hapticManager.error()
    }

    isLoading = false
}
```

#### Editing an Affirmation

```swift
// In a view model
public func updateAffirmation(_ affirmation: AffirmationProtocol) async {
    isLoading = true
    error = nil

    do {
        try await affirmationService.updateAffirmation(affirmation)
        await loadAffirmations()
        await hapticManager.success()
    } catch {
        self.error = error
        await hapticManager.error()
    }

    isLoading = false
}
```

#### Deleting an Affirmation

```swift
// In a view model
public func deleteAffirmation(id: UUID) async {
    isLoading = true
    error = nil

    do {
        try await affirmationService.deleteAffirmation(id: id)
        await loadAffirmations()
        await hapticManager.success()
    } catch {
        self.error = error
        await hapticManager.error()
    }

    isLoading = false
}
```

### Repetition Tracking

The app tracks repetitions for each affirmation, with a goal of 100 repetitions over a 7-day cycle.

#### Recording a Repetition

```swift
// In a view model
public func recordRepetition() async {
    guard let currentAffirmation = currentAffirmation, canRecordRepetition else {
        return
    }

    isLoading = true
    error = nil

    do {
        let updatedAffirmation = try await affirmationService.recordRepetition(for: currentAffirmation)
        self.currentAffirmation = updatedAffirmation
        
        // Update UI state
        currentRepetitions = updatedAffirmation.currentRepetitions
        
        // Provide feedback
        await hapticManager.success()
        
        // Check if cycle is complete
        if updatedAffirmation.isCurrentCycleComplete {
            // Handle cycle completion
            showCycleCompletionCelebration = true
        }
    } catch {
        self.error = error
        await hapticManager.error()
    }

    isLoading = false
}
```

### Audio Recording

The app allows users to record audio for their affirmations, which can be played back during repetitions.

#### Recording Audio

```swift
// In a view model
public func startRecording() async {
    isRecording = true
    recordingTime = 0
    
    do {
        try await audioRecordingService.startRecording()
        
        // Start timer to update recording time
        startRecordingTimer()
    } catch {
        isRecording = false
        self.error = error
    }
}

public func stopRecording() async {
    guard isRecording else { return }
    
    do {
        let url = try await audioRecordingService.stopRecording()
        recordingURL = url
        hasRecording = true
        isRecording = false
        
        // Stop timer
        stopRecordingTimer()
    } catch {
        self.error = error
    }
}
```

#### Playing Audio

```swift
// In a view model
public func playRecording() async {
    guard let url = recordingURL, !isPlaying else { return }
    
    do {
        try await audioRecordingService.startPlayback(from: url)
        isPlaying = true
        
        // Start timer to update playback progress
        startPlaybackTimer()
    } catch {
        self.error = error
    }
}

public func pausePlayback() {
    guard isPlaying else { return }
    
    audioRecordingService.pausePlayback()
    isPlaying = false
    
    // Stop timer
    stopPlaybackTimer()
}
```

## Testing

The app includes comprehensive tests for all modules.

### Unit Tests

Unit tests focus on testing individual components in isolation, using mocks for dependencies.

Example unit test for the `AffirmationService`:

```swift
func testCreateAffirmation() async throws {
    // Given
    let mockRepository = MockAffirmationRepository()
    let service = AffirmationService(repository: mockRepository)
    let text = "Test affirmation"
    let category = AffirmationCategory.confidence
    
    // When
    let affirmation = try await service.createAffirmation(
        text: text,
        category: category,
        recordingURL: nil
    )
    
    // Then
    XCTAssertEqual(affirmation.text, text)
    XCTAssertEqual(affirmation.category, category)
    XCTAssertFalse(affirmation.hasActiveCycle)
}
```

### Integration Tests

Integration tests focus on testing the interaction between multiple components.

Example integration test for the affirmation system:

```swift
func testEndToEndAffirmationFlow() async throws {
    // 1. Create an affirmation
    let text = "I am successful in everything I do"
    let category = AffirmationCategory.success
    
    let affirmation = try await affirmationService.createAffirmation(
        text: text,
        category: category,
        recordingURL: nil
    )
    
    // 2. Start a cycle
    try await affirmationService.startCycle(for: affirmation)
    
    // 3. Record repetitions
    for _ in 1...15 {
        _ = try await affirmationService.recordRepetition(for: affirmation)
    }
    
    // 4. Complete the cycle
    // ... additional steps
}
```

## Best Practices

### Error Handling

The app uses a consistent approach to error handling, with specific error types for different scenarios.

```swift
public enum AffirmationError: Error {
    case notFound
    case invalidData
    case networkError
    case serverError
    case alreadyExists
    case cannotPerformRepetition
    case unknown
}
```

Error handling in view models:

```swift
do {
    // Perform operation
} catch let error as AffirmationError {
    // Handle specific error
    switch error {
    case .notFound:
        errorMessage = "Affirmation not found"
    case .invalidData:
        errorMessage = "Invalid data"
    // Handle other cases
    }
} catch {
    // Handle unknown error
    errorMessage = "An unexpected error occurred"
}
```

### Async/Await

The app uses Swift's async/await for asynchronous operations, making the code more readable and maintainable.

```swift
public func loadAffirmations() async {
    isLoading = true
    error = nil
    
    do {
        affirmations = try await affirmationService.fetchAffirmations()
        currentAffirmation = try await affirmationService.fetchCurrentAffirmation()
        
        if let current = currentAffirmation {
            currentRepetitions = current.currentRepetitions
            currentCycleDay = current.currentCycleDay
        }
    } catch {
        self.error = error
    }
    
    isLoading = false
}
```

### SwiftUI Best Practices

The app follows SwiftUI best practices, including:

- Using `@StateObject` for view models that should persist for the lifetime of a view
- Using `@ObservedObject` for view models that are passed from parent views
- Using `@Environment` for accessing environment values
- Using `@EnvironmentObject` for accessing shared objects

Example:

```swift
struct AffirmationDetailView: View {
    @ObservedObject var viewModel: AffirmationDetailViewModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    
    var body: some View {
        // Implementation...
    }
}
```

## Conclusion

This developer guide provides a comprehensive overview of the NeuroLoopApp codebase. By following the patterns and practices outlined in this guide, developers can contribute to the app in a consistent and maintainable way.

For more information, refer to the following resources:

- [SwiftUI Documentation](https://developer.apple.com/documentation/swiftui)
- [SwiftData Documentation](https://developer.apple.com/documentation/swiftdata)
- [Swift Concurrency Documentation](https://docs.swift.org/swift-book/LanguageGuide/Concurrency.html)

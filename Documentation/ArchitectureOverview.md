# NeuroLoopApp Architecture Overview

## Table of Contents

- [1. Architectural Overview](#1-architectural-overview)
- [2. Core Design Patterns](#2-core-design-patterns)
- [3. Module Structure](#3-module-structure)
- [4. Key Components](#4-key-components)
- [5. Data Flow](#5-data-flow)
- [6. Testing Strategy](#6-testing-strategy)
- [7. Architectural Diagrams](#7-architectural-diagrams)
- [8. Theme System](#8-theme-system)
- [9. References](#9-references)

---

## 1. Architectural Overview

NeuroLoopApp is built using the **MVVM (Model-View-ViewModel)** architecture, emphasizing clear separation of concerns, modularity, and testability. The app is organized into multiple Swift Package modules, each with distinct responsibilities. Data flows unidirectionally, and state management is handled via observable objects and Combine publishers.

- **MVVM**: Models encapsulate data and business logic, ViewModels manage state and logic for views, and Views present UI.
- **Layered Structure**: Each module is responsible for a specific layer (interfaces, models, core logic, UI, shared utilities, app entry).
- **Dependency Injection**: Services and repositories are injected, supporting testability and modularity.

---

## 2. Core Design Patterns

### MVVM Implementation

- **Models**: Represent data and business logic (e.g., `Affirmation`, `RepetitionCycle`).
- **ViewModels**: Manage state, business logic, and coordinate between models and views.
- **Views**: SwiftUI views that observe ViewModels.

### Protocol-Oriented Programming

- All major services and models conform to protocols defined in `NeuroLoopInterfaces`.
- Promotes loose coupling and easier testing/mocking.

### Dependency Injection

- Services and repositories are injected into ViewModels and other services via initializers.

```swift
public init(
    affirmationService: AffirmationServiceProtocol,
    repetitionService: RepetitionServiceProtocol,
    streakService: StreakServiceProtocol,
    audioService: AudioRecordingServiceProtocol
) {
    self.affirmationService = affirmationService
    self.repetitionService = repetitionService
    self.streakService = streakService
    self.audioService = audioService
}
```

### Observer Pattern

- Uses **Combine** and **SwiftUI** property wrappers (`@Published`, `@StateObject`, `@ObservedObject`) for state updates.

### Other Patterns

- **Coordinator Pattern** for navigation (where applicable)
- **Repository Pattern** for data persistence
- **Singleton Pattern** for global managers (e.g., `ThemeManager`)

---

## 3. Module Structure

| Module                | Responsibility                                 |
|-----------------------|------------------------------------------------|
| NeuroLoopInterfaces   | Core protocols and interfaces                  |
| NeuroLoopModels       | Data models and entities                       |
| NeuroLoopCore         | Business logic and services                    |
| NeuroLoopUI           | Reusable UI components                         |
| NeuroLoopShared       | Shared utilities and helpers                   |
| NeuroLoopApp          | Main app module with views and view models     |

---

## 4. Key Components

### Affirmation Management System

- Handles creation, editing, and tracking of affirmations.
- Uses `AffirmationService` and `AffirmationRepositoryProtocol`.

### Repetition Tracking System

- Tracks 100 repetitions over a 7-day cycle.
- Uses `RepetitionService` and `RepetitionCycle` model.

### Streak and Progress Tracking

- Manages streaks, progress indicators, and milestone animations.

### Settings and Configuration

- Managed via `SettingsViewModel` and persistent storage.

### Premium Features & Subscription Management

- Gated features, subscription flows, and purchase validation.

### Audio Recording and Playback

- Managed by `AudioRecordingService` and related UI components.

### Theme System

- Provides consistent visual styling across the app.
- Managed by `ThemeManager` singleton and `Theme` struct.

---

## 5. Data Flow

### Data Persistence

- Uses **SwiftData** for local storage.
- Repository pattern abstracts data access.

### State Management

- ViewModels use `@Published` properties and Combine publishers.
- Views observe ViewModels for UI updates.

### Asynchronous Operations

- Uses Swift concurrency (`async/await`) for data fetching and updates.

### Error Handling

- Errors are surfaced via ViewModel properties and presented in the UI.

---

## 6. Testing Strategy

### Unit Testing

- Protocol-oriented design enables mocking and isolated unit tests.
- Core services and ViewModels have dedicated test suites.

### Integration Testing

- System-level tests validate interactions between modules.

### UI Testing

- Automated UI tests cover navigation, user flows, and accessibility.

### Test Data Management

- Uses mock data and dependency injection for predictable test scenarios.

---

## 7. Architectural Diagrams

### High-Level Module Architecture

```mermaid
graph TD
    A[NeuroLoopApp] --> B[NeuroLoopUI]
    A --> C[NeuroLoopCore]
    B --> D[NeuroLoopShared]
    C --> D
    C --> E[NeuroLoopModels]
    C --> F[NeuroLoopInterfaces]
    B --> F
    A --> F
    A --> D
```

### MVVM Data Flow

```mermaid
flowchart LR
    Model -- provides data --> ViewModel
    ViewModel -- exposes state --> View
    View -- user actions --> ViewModel
    ViewModel -- updates --> Model
```

---

## 8. Theme System

### Core Components

The theme system is built around a single, unified implementation:

- `Theme` struct in `NeuroLoopShared` - Contains all visual styling properties
- `ThemeManager` class in `NeuroLoopCore` - Manages theme selection and application

### Key Design Patterns

1. **Singleton Pattern**: `ThemeManager` uses a singleton (`shared`) to provide app-wide theme access
2. **Observer Pattern**: `ThemeManager` is an `ObservableObject` that publishes theme changes
3. **Environment Injection**: Theme is injected via SwiftUI's environment object system

### Usage Guidelines

#### Accessing the Current Theme

Always access the current theme through the ThemeManager:

```swift
// In views that have the ThemeManager in the environment
@EnvironmentObject var themeManager: ThemeManager
let backgroundColor = themeManager.currentTheme.background

// In other contexts
let backgroundColor = ThemeManager.shared.currentTheme.background
```

#### Applying Colors and Gradients

Use the appropriate accessors for theme colors:

```swift
// For colors
.foregroundColor(theme.textPrimary.asColor)

// For gradients
.background(theme.background.asGradient)

// With fallbacks
.background(theme.cardBackground.asGradient ?? Color.white)
```

### Best Practices

1. **Single Source of Truth**: Maintain only one implementation of theme-related types
2. **Clear Module Boundaries**: Keep theme definitions in shared modules, accessed by UI components
3. **Consistent Accessors**: Always use the same pattern to access theme properties
4. **Documentation**: Document theme changes and keep this guide updated

### Common Pitfalls to Avoid

1. Creating duplicate theme-related types in different modules
2. Directly using color literals instead of theme properties
3. Inconsistent property naming across the theme system
4. Forgetting to update all theme presets when adding new properties

### Verification Checklist

When making theme-related changes, verify:

- Build succeeds for all target platforms
- All UI components correctly display with the current theme
- Theme switching works properly
- No theme-related warnings or errors in the console
- SwiftUI previews render correctly with theme changes

---

## 9. References

- [SwiftUI Documentation](https://developer.apple.com/documentation/swiftui)
- [SwiftData Documentation](https://developer.apple.com/documentation/swiftdata)
- [Swift Concurrency](https://docs.swift.org/swift-book/LanguageGuide/Concurrency.html)
- [Combine Framework](https://developer.apple.com/documentation/combine)

> 💡 **Tip:** For more details on specific modules or components, see `DeveloperGuide.md` and `COMPONENTS.md`.

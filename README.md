# NeuroLoopApp2025_Fresh

## 🚀 iOS App Xcode Project Setup & Simulator Testing

To build, run, and test the iOS app using the simulator, follow these steps:

1. **Create a New Xcode Project**
   - Open Xcode.
   - Select **File > New > Project...**
   - Choose **App** (iOS, Swift, SwiftUI).
   - Name it `NeuroLoopApp`.
   - Set the organization identifier as desired.
   - Save the project in the root of this repository (do **not** nest inside another folder).

2. **Add the Local Swift Package**
   - In the Xcode project navigator, select the project file (blue icon).
   - Go to the app target > **General** tab.
   - Under **Frameworks, Libraries, and Embedded Content**, click the `+` button.
   - Choose **Add Other... > Add Package Dependency...**
   - In the dialog, select **Add Local...** and choose the root folder of this repository.
   - Add the following products as dependencies to your app target:
     - `NeuroLoopApp`
     - `NeuroLoopUI`
     - `NeuroLoopCore`
     - (Add any other required modules)

3. **Set the App Entry Point**
   - In your new Xcode project, open `App.swift`.
   - Replace the default `ContentView()` with your package's main/root view (e.g., `RootView()` or similar).
   - Ensure any required dependency injection is set up.

4. **Configure App Settings**
   - Edit `Info.plist` as needed for permissions and settings.
   - Set deployment target, device orientation, and entitlements as required.

5. **Build and Run**
   - Select an iOS Simulator device.
   - Press **⌘B** to build, then **⌘R** to run.
   - The app should launch in the simulator.

6. **SwiftUI Previews**
   - Open any SwiftUI view file (e.g., `HomeView`, `ContentView`).
   - Ensure previews render in the canvas. Fix any preview-specific issues as needed.

---

A SwiftUI app for affirmation repetition, designed to help users internalize positive affirmations through consistent practice.

## 🧩 Development Workflow

For guidance on working with Cursor.ai and Augment AI, see [README_REMINDERS.md](README_REMINDERS.md).

## Core Concept

NeuroLoopApp is built around a specific, research-based formula:

- **100 Repetitions**: Each affirmation must be repeated 100 times
- **7-Day Cycle**: Repetitions are performed over a 7-day period
- **Conscious Practice**: Each repetition is done mindfully and intentionally

## Project Structure

The project is organized as a Swift Package with multiple modules:

- **NeuroLoopInterfaces**: Core protocols and interfaces
- **NeuroLoopModels**: Data models and entities
- **NeuroLoopCore**: Business logic and services
- **NeuroLoopUI**: Reusable UI components
- **NeuroLoopShared**: Shared utilities and helpers
- **NeuroLoopApp**: Main app module with views and view models
- **NeuroLoopRunner**: Executable target to run the app

## Running the App

There are two ways to run the app:

### Option 1: Using Xcode with Swift Package Manager

1. Open the project directory in Xcode
2. Select the `NeuroLoopRunner` scheme
3. Select a simulator or device
4. Press the Run button (⌘R)

### Option 2: Using Swift Package Manager from the command line

```bash
# Build the app
swift build

# Run the app (this will only work in a simulator environment)
swift run NeuroLoopRunner
```

## Requirements

- iOS 17.0 or later
- macOS 14.0 or later
- Xcode 15.0+
- Swift 5.9+

> **Note:** NeuroLoopApp now targets iOS 17.0 and macOS 14.0 or newer. Support for earlier OS versions has been removed to simplify the codebase and leverage modern Swift and SwiftUI features.

## Why Target iOS 17.0+ and macOS 14.0+

By focusing on the latest OS versions, we:

- Eliminate complex availability checks and legacy workarounds
- Reduce code complexity and maintenance overhead
- Unlock the latest Swift, SwiftUI, and system APIs
- Ensure a more consistent and reliable user experience
- Enable faster adoption of new features and performance improvements

## Features

- Create and manage affirmations
- Track repetition progress
- Visual feedback with animations
- Haptic feedback
- Multiple themes
- Accessibility support

## Architecture

The app follows the MVVM (Model-View-ViewModel) architecture pattern:

- **Models**: Data structures and business logic
- **Views**: UI components and screens
- **ViewModels**: State management and business logic for views

## Testing

Unit tests are provided for the core functionality:

- Run tests for models: `swift test --target NeuroLoopModelsTests`
- Run tests for core services: `swift test --target NeuroLoopCoreTests`

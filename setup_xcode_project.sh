#!/bin/bash
set -e

PROJECT_NAME="NeuroLoopApp"

# 1. Create Xcode project directory if it doesn't exist
echo "[1/4] Creating Xcode project directory: $PROJECT_NAME"
if [ ! -d "$PROJECT_NAME" ]; then
  mkdir "$PROJECT_NAME"
else
  echo "Directory '$PROJECT_NAME' already exists. Skipping creation."
fi
cd "$PROJECT_NAME"

# 2. Create minimal App.swift if not present
APP_SWIFT="App.swift"
echo "[2/4] Creating $APP_SWIFT"
if [ ! -f "$APP_SWIFT" ]; then
  cat > "$APP_SWIFT" <<EOF
import SwiftUI
import Neuro<PERSON>oopUI // <-- Make sure this matches your package's main view module

@main
struct NeuroLoopApp: App {
    var body: some Scene {
        WindowGroup {
            // Replace 'RootView()' with your actual root view if different
            RootView()
        }
    }
}
EOF
else
  echo "$APP_SWIFT already exists. Skipping creation."
fi

# 3. Create minimal Info.plist if not present
PLIST="Info.plist"
echo "[3/4] Creating $PLIST"
if [ ! -f "$PLIST" ]; then
  cat > "$PLIST" <<EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>CFBundleIdentifier</key>
  <string>com.example.NeuroLoopApp</string>
  <key>CFBundleName</key>
  <string>NeuroLoopApp</string>
  <key>CFBundleShortVersionString</key>
  <string>1.0</string>
  <key>CFBundleVersion</key>
  <string>1</string>
  <key>UILaunchStoryboardName</key>
  <string></string>
  <key>UIApplicationSceneManifest</key>
  <dict>
    <key>UIApplicationSupportsMultipleScenes</key>
    <true/>
    <key>UISceneConfigurations</key>
    <dict>
      <key>UIWindowSceneSessionRoleApplication</key>
      <array>
        <dict>
          <key>UISceneConfigurationName</key>
          <string>Default Configuration</string>
          <key>UISceneDelegateClassName</key>
          <string></string>
        </dict>
      </array>
    </dict>
  </dict>
</dict>
</plist>
EOF
else
  echo "$PLIST already exists. Skipping creation."
fi

# 4. Print next steps
echo "[4/4] Xcode project scaffolded!"
echo "\nNext steps:"
echo "1. Open Xcode and select 'Open a project or file', then choose the '$PROJECT_NAME' folder."
echo "2. In Xcode, add the local Swift Package (File > Add Packages... > Add Local...) and select the root of your repository."
echo "3. Set the app's entry point to use your package's main/root view in App.swift if needed."
echo "4. Edit Info.plist as required."
echo "5. Build and run the app in the iOS Simulator!" 
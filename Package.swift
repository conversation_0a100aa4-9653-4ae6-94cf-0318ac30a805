// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "NeuroLoopApp2025_Fresh",
    platforms: [
        .iOS(.v17),
        .macOS(.v14),
    ],
    products: [
        .library(
            name: "NeuroLoopTypes",
            targets: ["NeuroLoopTypes"]
        ),
        .library(
            name: "NeuroLoopModels",
            targets: ["NeuroLoopModels"]
        ),
        .library(
            name: "NeuroLoopInterfaces",
            targets: ["NeuroLoopInterfaces"]
        ),
        .library(
            name: "NeuroLoopCore",
            targets: ["NeuroLoopCore"]
        ),
        .library(
            name: "Neuro<PERSON><PERSON><PERSON>",
            targets: ["NeuroLoopUI"]
        ),
        .library(
            name: "NeuroLoopShared",
            targets: ["NeuroLoopShared"]
        ),
        .library(
            name: "NeuroLoopApp",
            targets: ["NeuroLoopApp"]
        ),
    ],
    dependencies: [
        .package(
            url: "https://github.com/apple/swift-collections.git", .upToNextMajor(from: "1.0.0"))
    ],
    targets: [
        .target(
            name: "NeuroLoopTypes",
            dependencies: []
        ),
        .target(
            name: "NeuroLoopInterfaces",
            dependencies: ["NeuroLoopTypes"]
        ),
        .target(
            name: "NeuroLoopModels",
            dependencies: [
                "NeuroLoopInterfaces", "NeuroLoopTypes",
                .product(name: "Collections", package: "swift-collections"),
                .product(name: "OrderedCollections", package: "swift-collections"),
            ]
        ),
        .target(
            name: "NeuroLoopCore",
            dependencies: [
                "NeuroLoopInterfaces", "NeuroLoopModels", "NeuroLoopTypes",
                .product(name: "Collections", package: "swift-collections"),
                .product(name: "OrderedCollections", package: "swift-collections"),
            ]
        ),
        .target(
            name: "NeuroLoopShared",
            dependencies: [
                "NeuroLoopCore", "NeuroLoopTypes",
                .product(name: "Collections", package: "swift-collections"),
                .product(name: "OrderedCollections", package: "swift-collections"),
            ]
        ),
        .target(
            name: "NeuroLoopUI",
            dependencies: [
                "NeuroLoopInterfaces", "NeuroLoopCore", "NeuroLoopModels",
                "NeuroLoopTypes", "NeuroLoopShared",
                .product(name: "Collections", package: "swift-collections"),
                .product(name: "OrderedCollections", package: "swift-collections"),
            ]
        ),
        .target(
            name: "NeuroLoopApp",
            dependencies: [
                "NeuroLoopInterfaces",
                "NeuroLoopModels",
                "NeuroLoopCore",
                "NeuroLoopUI",
                "NeuroLoopTypes",
                .product(name: "Collections", package: "swift-collections"),
                .product(name: "OrderedCollections", package: "swift-collections"),
            ]
        ),
        .target(
            name: "NeuroLoopTestUtilities",
            dependencies: ["NeuroLoopInterfaces", "NeuroLoopTypes"],
            path: "Sources/NeuroLoopTestUtilities"
        ),
        .testTarget(
            name: "NeuroLoopModelsTests",
            dependencies: ["NeuroLoopModels", "NeuroLoopTestUtilities"]
        ),
        .testTarget(
            name: "NeuroLoopCoreTests",
            dependencies: ["NeuroLoopCore", "NeuroLoopTestUtilities"]
        ),
        .testTarget(
            name: "NeuroLoopUITests",
            dependencies: ["NeuroLoopUI", "NeuroLoopTestUtilities"],
            path: "Tests/NeuroLoopUITests"
        ),
        .testTarget(
            name: "NeuroLoopAppTests",
            dependencies: ["NeuroLoopApp", "NeuroLoopTestUtilities"],
            path: "Tests/NeuroLoopAppTests"
        ),
        .testTarget(
            name: "NeuroLoopTests",
            dependencies: [
                "NeuroLoopCore",
                "NeuroLoopInterfaces",
                "NeuroLoopTestUtilities",
            ],
            path: "Tests/NeuroLoopTests"
        ),
    ]
)

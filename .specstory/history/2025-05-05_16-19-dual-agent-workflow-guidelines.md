<!-- Generated by SpecStory -->

# Dual-Agent Workflow Guidelines (2025-05-05 17:19:20)

_**User**_

🧠 Reminder: We're using a dual-agent workflow. Please stick to your assigned role.

Refer to your respective rule files: `.augment-ai` for Augment and `.cursor-ai` for Cursor.

Augment:
- You are the architect and planner.
- Only update `TASKS.md` and `IMPLEMENTATION_PLAN.md`.
- Do NOT write or edit Swift code or `.swift` files.
- Do NOT create extra `.md` files — only use `TASKS.md` and `TASKS_ARCHIVE.md`.

Cursor:
- You are the implementation engineer.
- Only implement tasks defined by Augment in `TASKS.md`.
- You may read `IMPLEMENTATION_PLAN.md` for context, but NEVER modify it.
- Never plan features or restructure architecture.

📌 All coordination and task flow must happen through `TASKS.md`.  
This is your single source of truth.  
If you are unsure of your next action, re-read your role file and ask for clarification.


---

_**Assistant**_

<details>
            <summary>Searched codebase "TASKS.md" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: TASKS.md

---

As the implementation engineer (Cursor), my next action is determined by the tasks listed in `TASKS.md`. According to the current handoff and task list, my next assigned task is:

**Implement Recovery Flow for Broken Streaks**
- Create a "Streak Recovery" view that appears when a streak is broken
- Design encouraging messaging that emphasizes progress not perfection
- Implement a "Quick Restart" option to begin a new streak immediately
- Add optional notifications to help users get back on track
- Ensure the UI maintains a positive, non-judgmental tone

I will proceed to implement this feature, following the details and priorities outlined in `TASKS.md`. If you have any specific requirements or want to clarify the design, please let me know before I begin. Otherwise, I will start by identifying the relevant files and code structure for the streak feature and recovery flow.

---

_**User**_

# Task Brief: Implement Recovery Flow for Broken Streaks

## Objective
Create a supportive and encouraging flow for users who break their affirmation streaks, helping them restart their practice without feeling discouraged.

## Background
- The NeuroLoopApp is built around a 7-day streak system for affirmation repetitions
- Currently, when users break a streak, there's no specific guidance or encouragement to help them restart
- This can lead to user abandonment if they feel they've "failed"
- We need a recovery flow that maintains motivation and emphasizes progress over perfection

## Specific Tasks

1. Create a "Streak Recovery" View
   - Design a dedicated view that appears when a streak is broken
   - Use encouraging visuals and messaging
   - Show statistics on their previous progress to emphasize what they've accomplished
   - Include a clear "Restart" button to begin a new streak

2. Implement the Recovery Logic
   - Add methods to `StreakService` to detect broken streaks
   - Create logic to present the recovery flow at the appropriate time
   - Implement the restart functionality that resets the streak counter but preserves history
   - Add optional notification settings for streak recovery reminders

3. Design Supportive Messaging
   - Create copy that emphasizes that consistency matters more than perfection
   - Include research-backed information about habit formation
   - Use positive, non-judgmental language throughout
   - Provide tips for maintaining future streaks

4. Add Progress Preservation
   - Ensure historical data is preserved even when streaks are reset
   - Create a "Streak History" section that shows all previous attempts
   - Implement a "Personal Best" tracker to gamify improvement

5. Test the Recovery Flow
   - Create unit tests for the streak breaking and recovery logic
   - Add UI tests for the recovery view and interactions
   - Test edge cases (multiple broken streaks, immediate breaks after recovery)

## Deliverables
1. A complete "Streak Recovery" view with supportive messaging
2. Enhanced `StreakService` with recovery-related methods
3. Unit and UI tests for the new functionality
4. Documentation of the recovery flow in code comments

## Implementation Guidelines
- Follow the existing MVVM architecture pattern
- Use SwiftUI for the UI components
- Maintain consistency with the app's visual design
- Ensure all text is localized
- Follow accessibility best practices

## Notes
- This feature should feel supportive, not punitive
- The goal is to increase user retention by preventing abandonment after broken streaks
- Research shows that positive reinforcement is more effective than guilt for habit formation

---

_**Assistant**_

<details>
            <summary>Searched codebase "StreakService" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `Sources/NeuroLoopCore/Services/StreakService.swift` | L1-30 |
| `Sources/NeuroLoopCore/Services/StreakService.swift` | L71-98 |
| `Sources/NeuroLoopCore/Services/StreakService.swift` | L179-227 |
| `Sources/NeuroLoopCore/Services/StreakService.swift` | L98-131 |
| `Tests/NeuroLoopAppTests/TestMocks.swift` | L50-83 |
| `Sources/NeuroLoopCore/Services/StreakService.swift` | L131-179 |
| `Sources/NeuroLoopCore/Services/StreakService.swift` | L30-71 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L202-214 |
| `Tests/NeuroLoopAppTests/StreakSystemTests.swift` | L1-46 |
| `Sources/NeuroLoopCore/Services/RepetitionService.swift` | L111-148 |
| `Sources/NeuroLoopUI/ViewModels/StreakVisualizationViewModel.swift` | L1-54 |
| `Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L329-396 |
| `Tests/NeuroLoopTests/Integration/StreakSystemTests.swift` | L165-210 |
| `Sources/NeuroLoopCore/Services/NotificationService.swift` | L89-123 |
| `Tests/NeuroLoopAppTests/StreakSystemTests.swift` | L165-219 |
| `Sources/NeuroLoopUI/ViewModels/StreakVisualizationViewModel.swift` | L54-103 |
| `Sources/NeuroLoopCore/Services/RepetitionService.swift` | L203-253 |
| `Sources/NeuroLoopCore/Services/ServiceFactory.swift` | L63-95 |
| `Tests/NeuroLoopAppTests/StreakSystemTests.swift` | L122-165 |
| `Sources/NeuroLoopCore/Services/RepetitionService.swift` | L1-24 |
| `Tests/NeuroLoopAppTests/StreakSystemTests.swift` | L46-85 |
| `Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L396-468 |
| `Tests/NeuroLoopAppTests/StreakSystemTests.swift` | L85-122 |
| `Tests/NeuroLoopAppTests/RepetitionSystemTests.swift` | L47-85 |
| `Tests/NeuroLoopAppTests/RepetitionSystemTests.swift` | L85-126 |

</details>

---

<details>
            <summary>Searched codebase "Recovery" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Listed directory Sources/NeuroLoopUI/Views • **12** results</summary>
        
| Name |
|-------|
| 📄 `RootView.swift` |
| 📄 `AffirmationDetailView.swift` |
| 📁 `Home` |
| 📄 `.DS_Store` |
| 📁 `Affirmation` |
| 📁 `Library` |
| 📁 `Audio` |
| 📁 `Repetition` |
| 📁 `Premium` |
| 📁 `Streak` |
| 📁 `Navigation` |
| 📁 `Settings` |

</details>

---

<details>
            <summary>Listed directory Sources/NeuroLoopUI/Views/Streak • **3** results</summary>
        
| Name |
|-------|
| 📄 `StreakVisualizationView.swift` |
| 📄 `StreakTimelineView.swift` |
| 📄 `MonthlyCalendarView.swift` |

</details>

---

<details>
            <summary>Listed directory Sources/NeuroLoopUI/ViewModels • **9** results</summary>
        
| Name |
|-------|
| 📄 `SubscriptionViewModel.swift` |
| 📄 `StreakVisualizationViewModel.swift` |
| 📄 `AudioRecordingViewModel.swift` |
| 📄 `SettingsViewModel.swift` |
| 📄 `AddAffirmationViewModel.swift` |
| 📄 `HomeViewModel.swift` |
| 📄 `AffirmationDetailViewModel.swift` |
| 📄 `LibraryViewModel.swift` |
| 📄 `RepetitionTrackingViewModel.swift` |

</details>

---

<details>
            <summary>Listed directory Sources/NeuroLoopCore/Services • **14** results</summary>
        
| Name |
|-------|
| 📄 `PremiumService.swift` |
| 📄 `RepetitionService.swift` |
| 📄 `DataExportService.swift` |
| 📄 `PurchaseManager.swift` |
| 📄 `ServiceFactory.swift` |
| 📄 `AccessibilityService.swift` |
| 📄 `PremiumFeatureUnlocker.swift` |
| 📄 `StreakService.swift` |
| 📄 `PerformanceOptimizationService.swift` |
| 📄 `NotificationService.swift` |
| 📄 `AudioRecordingService.swift` |
| 📄 `AffirmationService.swift` |
| 📄 `FinalTestingService.swift` |
| 📄 `AppStoreConfigurationService.swift` |

</details>

---

<details>
            <summary>Listed directory Sources/NeuroLoopModels • **2** results</summary>
        
| Name |
|-------|
| 📄 `RepetitionCycle.swift` |
| 📄 `Affirmation.swift` |

</details>

---

<details>
            <summary>Listed directory Sources/NeuroLoopUI/Views/Repetition • **1** results</summary>
        
| Name |
|-------|
| 📄 `RepetitionTrackingView.swift` |

</details>

---

Read file: Sources/NeuroLoopUI/Views/Streak/StreakVisualizationView.swift

---

Read file: Sources/NeuroLoopCore/Services/StreakService.swift

---

<details>
            <summary>Listed directory Sources/NeuroLoopUI/Views/Streak • **3** results</summary>
        
| Name |
|-------|
| 📄 `StreakVisualizationView.swift` |
| 📄 `StreakTimelineView.swift` |
| 📄 `MonthlyCalendarView.swift` |

</details>

---

<details>
            <summary>Listed directory Sources/NeuroLoopUI/ViewModels • **9** results</summary>
        
| Name |
|-------|
| 📄 `SubscriptionViewModel.swift` |
| 📄 `StreakVisualizationViewModel.swift` |
| 📄 `AudioRecordingViewModel.swift` |
| 📄 `SettingsViewModel.swift` |
| 📄 `AddAffirmationViewModel.swift` |
| 📄 `HomeViewModel.swift` |
| 📄 `AffirmationDetailViewModel.swift` |
| 📄 `LibraryViewModel.swift` |
| 📄 `RepetitionTrackingViewModel.swift` |

</details>

---

<details>
            <summary>Listed directory Sources/NeuroLoopModels • **2** results</summary>
        
| Name |
|-------|
| 📄 `RepetitionCycle.swift` |
| 📄 `Affirmation.swift` |

</details>

---

Read file: Sources/NeuroLoopModels/Affirmation.swift

---

Read file: Sources/NeuroLoopModels/RepetitionCycle.swift

---

Read file: Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift

---

Read file: Sources/NeuroLoopUI/Views/Repetition/RepetitionTrackingView.swift

---

Could not find file 'Sources/NeuroLoopUI/Views/Streak/StreakVisualizationViewModel.swift' in the workspace.

---

Read file: Sources/NeuroLoopUI/ViewModels/StreakVisualizationViewModel.swift

---

Here’s a summary of the current state and next steps for implementing the "Streak Recovery" flow, based on your brief and the codebase:

---

## Key Findings

- **No existing Streak Recovery view or view model**: The `Sources/NeuroLoopUI/Views/Streak` and `ViewModels` folders do not contain a recovery-specific view or logic.
- **StreakService**: Handles streak validation, statistics, and risk detection, but does not yet expose recovery or restart logic, nor does it track streak break history.
- **Affirmation & RepetitionCycle models**: Track cycles and progress, but do not explicitly record streak breaks or recovery events.
- **RepetitionTrackingView/Model**: Handles daily repetition and progress, but does not present a recovery UI or logic for broken streaks.
- **StreakVisualizationView/Model**: Visualizes streaks, but does not handle recovery or history of broken streaks.

---

## Next Steps (Implementation Plan)

**1. Add Streak Recovery Logic to StreakService**
   - Add methods to detect broken streaks, restart streaks, and preserve streak break history.
   - Ensure history is not lost when a streak is reset.

**2. Create StreakRecoveryViewModel**
   - Handles the logic for presenting the recovery flow, statistics, and restart action.
   - Fetches supportive messaging and user’s streak history.

**3. Create StreakRecoveryView**
   - SwiftUI view that presents:
     - Encouraging messaging and visuals
     - Previous progress and statistics
     - "Restart" button
     - Option to enable recovery notifications
     - "Streak History" and "Personal Best" sections

**4. Integrate Recovery Flow**
   - Update repetition and streak tracking logic to present the recovery view when a streak is broken.
   - Ensure navigation to the recovery view is smooth and context-aware.

**5. Add/Update Models for Streak History**
   - Extend `Affirmation` or add a new model to track streak break and recovery events.

**6. Add Unit and UI Tests**
   - Test streak break detection, recovery, and UI presentation.
   - Test edge cases (multiple breaks, immediate breaks after recovery).

**7. Documentation**
   - Add code comments and documentation for the new flow.

---

**I will begin by implementing the new model(s) for streak break history, then add the recovery logic to `StreakService`, and proceed to create the new view model and SwiftUI view.** If you have any preferences for the UI or messaging, let me know before I start. Otherwise, I’ll proceed with the implementation.

---


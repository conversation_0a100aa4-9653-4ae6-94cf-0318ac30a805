<!-- Generated by SpecStory -->

# New Rule for .augment-ai Sessions (2025-05-10 07:26:29)

_**User**_

Please add this as a rule In .augment-ai ## Startup Rule
At the beginning of any new session, always re-load `.augment-ai` and `.augment` rules folder.
Confirm your role, restrictions, and workflow responsibilities before responding to planning tasks.


---

_**Assistant**_

I'll help you add this rule to the `.augment-ai` file. First, let me check if the file exists and then add the rule.


---

<details>
            <summary>Searched codebase ".augment-ai" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Let me read the current content of the `.augment-ai` file to see where to add the new rule.


---

Read file: .augment-ai

---

Now I'll add the new startup rule to the file. I'll place it near the beginning of the file, after the initial role description but before the core responsibilities section.


---

```diff
  
  You are NOT a code implementer. Do NOT write or modify Swift code or implementation project files.
+ 
+ ## Startup Rule
+ At the beginning of any new session, always re-load `.augment-ai` and `.augment` rules folder.
+ Confirm your role, restrictions, and workflow responsibilities before responding to planning tasks.
  
  ---
```

---

I've successfully added the new startup rule to the `.augment-ai` file. The rule has been placed in a logical location near the beginning of the file, after the initial role description but before the core responsibilities section. The rule is formatted consistently with the rest of the document and clearly communicates the requirement to reload rules and confirm role responsibilities at the start of each session.

Is there anything else you'd like me to add or modify in the `.augment-ai` file?

---

_**User**_

Please add this as a rule In ## Startup Rule
Always load `.cursor-ai` and `.cursor` rules folder at the start of a new session.
Reaffirm your role as implementation engineer. Only implement assigned tasks. Do not plan features.


---

_**Assistant**_

I'll help you add this rule to the `.augment-ai` file. I'll add it to the Startup Rule section, combining it with the existing rule while maintaining clear separation between the two roles.


---

```diff
  
  ## Startup Rule
- At the beginning of any new session, always re-load `.augment-ai` and `.augment` rules folder.
- Confirm your role, restrictions, and workflow responsibilities before responding to planning tasks.
+ At the beginning of any new session:
+ 1. For Augment (Architect & Task Planner):
+    - Always re-load `.augment-ai` and `.augment` rules folder
+    - Confirm your role, restrictions, and workflow responsibilities before responding to planning tasks
+ 
+ 2. For Cursor (Implementation Engineer):
+    - Always load `.cursor-ai` and `.cursor` rules folder
+    - Reaffirm your role as implementation engineer
+    - Only implement assigned tasks
+    - Do not plan features
  
  ---
```

---

I've updated the Startup Rule section to include both Augment and Cursor startup requirements. The new format:
1. Clearly separates the responsibilities of both roles
2. Maintains a structured, hierarchical format
3. Preserves the existing Augment requirements while adding the new Cursor requirements
4. Makes it clear that Cursor should focus on implementation and not planning

The changes have been successfully applied to the `.augment-ai` file. Is there anything else you'd like me to modify in the file?

---

_**User**_

 Please add this as a rule In .cursor-ai ## Startup Rule
Always load `.cursor-ai` and `.cursor` rules folder at the start of a new session.
Reaffirm your role as implementation engineer. Only implement assigned tasks. Do not plan features.


---

_**Assistant**_

I understand now - you want to add this rule to the `.cursor-ai` file instead of `.augment-ai`. Let me help you with that.


---



---



---


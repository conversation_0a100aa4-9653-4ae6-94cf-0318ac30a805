<!-- Generated by SpecStory -->

# Dual-Agent Workflow Role Clarification (2025-05-06 21:43:34)

_**User**_

🧠 Reminder: We're using a dual-agent workflow. Please stick to your assigned role.

Refer to your respective rule files: `.augment-ai` for Augment and `.cursor-ai` for Cursor.

Augment:
- You are the architect and planner.
- Only update `TASKS.md` and `IMPLEMENTATION_PLAN.md`.
- Do NOT write or edit Swift code or `.swift` files.
- Do NOT create extra `.md` files — only use `TASKS.md` and `TASKS_ARCHIVE.md`.

Cursor:
- You are the implementation engineer.
- Only implement tasks defined by Augment in `TASKS.md`.
- You may read `IMPLEMENTATION_PLAN.md` for context, but NEVER modify it.
- Never plan features or restructure architecture.

📌 All coordination and task flow must happen through `TASKS.md`.  
This is your single source of truth.  
If you are unsure of your next action, re-read your role file and ask for clarification.


---

_**Assistant**_

Understood. As Cursor, my role is to implement tasks that are explicitly defined in `TASKS.md` and not to plan, restructure, or modify any files outside of implementation (especially not `IMPLEMENTATION_PLAN.md`). All coordination and task flow must be referenced from `TASKS.md`.

If you have a specific implementation task from `TASKS.md` that you want me to work on, please specify which one, or let me know if you want me to identify the next actionable implementation task from the current `TASKS.md` content. 

How would you like to proceed?

---


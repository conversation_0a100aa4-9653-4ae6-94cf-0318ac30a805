<!-- Generated by SpecStory -->

# Dual-Agent Workflow Coordination (2025-05-06 08:08:48)

_**User**_

🧠 Reminder: We're using a dual-agent workflow. Please stick to your assigned role.

Refer to your respective rule files: `.augment-ai` for Augment and `.cursor-ai` for Cursor.

Augment:
- You are the architect and planner.
- Only update `TASKS.md` and `IMPLEMENTATION_PLAN.md`.
- Do NOT write or edit Swift code or `.swift` files.
- Do NOT create extra `.md` files — only use `TASKS.md` and `TASKS_ARCHIVE.md`.

Cursor:
- You are the implementation engineer.
- Only implement tasks defined by Augment in `TASKS.md`.
- You may read `IMPLEMENTATION_PLAN.md` for context, but NEVER modify it.
- Never plan features or restructure architecture.

📌 All coordination and task flow must happen through `TASKS.md`.  
This is your single source of truth.  
If you are unsure of your next action, re-read your role file and ask for clarification.

---

_**Assistant**_

Understood. As Cursor, my role is to implement tasks as defined in `TASKS.md` and not to plan or restructure features. All coordination and task flow must happen through `TASKS.md`.

Based on the current state of `TASKS.md`, my next assigned task is:

**Fix SettingsViewModelTests**
- Verify that `MockPurchaseManager` and `MockDataExportService` are properly implemented.
- Ensure the test is correctly initializing `SettingsViewModel` with these mock dependencies.
- Check for any missing properties or methods in the mocks.
- Update the test to match the current implementation of `SettingsViewModel`.

I will begin by locating the `SettingsViewModelTests`, `MockPurchaseManager`, and `MockDataExportService` files, and then proceed to review and implement the required fixes.

---

<details>
            <summary>Searched codebase "SettingsViewModelTests" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "MockPurchaseManager" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "MockPurchaseManager" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "MockDataExportService" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "MockPurchaseManager" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsView.swift` | L184 | `private class PreviewMockPurchaseManager: PurchaseManagerProtocol {` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsView.swift` | L197 | `purchaseManager: PreviewMockPurchaseManager(),` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/RootView.swift` | L91 | `SettingsView(purchaseManager: MockPurchaseManager(), dataExportService: MockDataExportService())` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/RootView.swift` | L145 | `private class MockPurchaseManager: PurchaseManagerProtocol {` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L8 | `private var mockPurchaseManager: MockPurchaseManager!` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L15 | `mockPurchaseManager = MockPurchaseManager()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L20 | `purchaseManager: mockPurchaseManager,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L27 | `mockPurchaseManager = nil` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L112 | `mockPurchaseManager.shouldSucceed = true` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L124 | `mockPurchaseManager.shouldSucceed = false` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L137 | `mockPurchaseManager.shouldSucceed = true` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L149 | `mockPurchaseManager.shouldSucceed = false` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L217 | `private class MockPurchaseManager: PurchaseManagerProtocol {` |

</details>

---

<details>
            <summary>Grep search for "MockDataExportService" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/RootView.swift` | L91 | `SettingsView(purchaseManager: MockPurchaseManager(), dataExportService: MockDataExportService())` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/RootView.swift` | L150 | `private class MockDataExportService: DataExportServiceProtocol {` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsView.swift` | L189 | `private class PreviewMockDataExportService: DataExportServiceProtocol {` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsView.swift` | L198 | `dataExportService: PreviewMockDataExportService()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L9 | `private var mockDataExportService: MockDataExportService!` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L16 | `mockDataExportService = MockDataExportService()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L21 | `dataExportService: mockDataExportService` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L28 | `mockDataExportService = nil` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L164 | `mockDataExportService.shouldSucceed = true` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L175 | `mockDataExportService.shouldSucceed = false` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L188 | `mockDataExportService.shouldSucceed = true` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L203 | `mockDataExportService.shouldSucceed = false` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift` | L233 | `private class MockDataExportService: DataExportServiceProtocol {` |

</details>

---

Read file: Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift

---

Read file: Tests/NeuroLoopUITests/ViewModels/SettingsViewModelTests.swift

---

<details>
            <summary>Searched codebase "SettingsViewModel" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L1-62 |
| `Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L62-115 |
| `Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L115-155 |
| `Sources/NeuroLoopUI/ViewModels/HomeViewModel.swift` | L1-30 |
| `Sources/NeuroLoopUI/ViewModels/LibraryViewModel.swift` | L1-63 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L1-39 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L28-73 |
| `Sources/NeuroLoopUI/ViewModels/SubscriptionViewModel.swift` | L1-78 |
| `Sources/NeuroLoopUI/ViewModels/AddAffirmationViewModel.swift` | L1-65 |
| `Sources/NeuroLoopUI/ViewModels/AffirmationDetailViewModel.swift` | L1-15 |
| `Sources/NeuroLoopUI/ViewModels/AudioRecordingViewModel.swift` | L1-36 |
| `Sources/NeuroLoopUI/ViewModels/LibraryViewModel.swift` | L130-156 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L1-28 |
| `Sources/NeuroLoopUI/ViewModels/AudioRecordingViewModel.swift` | L36-98 |
| `Sources/NeuroLoopUI/ViewModels/LibraryViewModel.swift` | L63-112 |
| `Sources/NeuroLoopUI/ViewModels/StreakVisualizationViewModel.swift` | L1-54 |
| `Sources/NeuroLoopUI/ViewModels/HomeViewModel.swift` | L30-81 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L73-113 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L39-84 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L147-187 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L140-154 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L255-292 |
| `Sources/NeuroLoopUI/ViewModels/SubscriptionViewModel.swift` | L78-103 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L84-147 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L202-219 |

</details>

---

<details>
            <summary>Searched codebase "ThemeManaging OR HapticGenerating" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L1-62 |
| `Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L62-115 |
| `Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L115-155 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L1-39 |
| `Sources/NeuroLoopUI/ViewModels/HomeViewModel.swift` | L1-30 |
| `Sources/NeuroLoopUI/ViewModels/SubscriptionViewModel.swift` | L1-78 |
| `Sources/NeuroLoopUI/ViewModels/StreakVisualizationViewModel.swift` | L1-54 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L154-185 |
| `Sources/NeuroLoopUI/ViewModels/HomeViewModel.swift` | L30-81 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L73-113 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L84-147 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L1-28 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L113-128 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L28-73 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L39-84 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L128-140 |
| `Sources/NeuroLoopUI/ViewModels/SubscriptionViewModel.swift` | L78-103 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L202-219 |
| `Sources/NeuroLoopUI/ViewModels/AudioRecordingViewModel.swift` | L36-98 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L147-187 |
| `Sources/NeuroLoopUI/ViewModels/NeuralPathwayRestorationViewModel.swift` | L140-154 |
| `Sources/NeuroLoopUI/ViewModels/AudioRecordingViewModel.swift` | L1-36 |
| `Sources/NeuroLoopUI/ViewModels/LibraryViewModel.swift` | L1-63 |
| `Sources/NeuroLoopUI/ViewModels/RepetitionTrackingViewModel.swift` | L231-255 |
| `Sources/NeuroLoopUI/ViewModels/AddAffirmationViewModel.swift` | L1-65 |

</details>

---

<details>
            <summary>Grep search for "ThemeManager" • **11** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L13 | `themeManager.setTheme(isDarkModeEnabled ? Theme.defaultDark : Theme.defaultLight)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L55 | `private let themeManager: ThemeManaging` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L65 | `themeManager: ThemeManaging,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L71 | `self.themeManager = themeManager` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L150 | `let viewModel = SettingsViewModel(themeManager: ThemeManager.shared, hapticManager: HapticManager.shared)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsView.swift` | L26 | `themeManager: ThemeManager.shared,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemeManager.swift` | L4 | `public class ThemeManager: ObservableObject, ThemeManaging {` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemeManager.swift` | L5 | `public static let shared = ThemeManager()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L5 | `@StateObject private var themeManager = ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L15 | `get: { themeManager.currentTheme },` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L16 | `set: { themeManager.setTheme($0) }` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L150 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L25 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L26 | `private let themeManager: ThemeManaging` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L38 | `themeManager: ThemeManaging` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L45 | `self.themeManager = themeManager` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L283 | `themeManager.currentTheme` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L27 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L43 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L59 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/MainTabView.swift` | L4 | `@StateObject private var themeManager = ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/MainTabView.swift` | L28 | `.accentColor(themeManager.currentTheme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L213 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L13 | `themeManager.setTheme(isDarkModeEnabled ? Theme.defaultDark : Theme.defaultLight)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L55 | `private let themeManager: ThemeManaging` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L65 | `themeManager: ThemeManaging,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L71 | `self.themeManager = themeManager` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L150 | `let viewModel = SettingsViewModel(themeManager: ThemeManager.shared, hapticManager: HapticManager.shared)` |

</details>

---

<details>
            <summary>Grep search for "HapticManager" • **11** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L149 | `hapticManager: HapticManager.shared,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L24 | `hapticManager: HapticManager.shared,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L26 | `hapticManager: HapticManager.shared,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L42 | `hapticManager: HapticManager.shared,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L58 | `hapticManager: HapticManager.shared,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L28 | `HapticManager.shared.setEnabled(newValue)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L32 | `await HapticManager.shared.mediumImpact()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L58 | `hapticsEnabled = HapticManager.shared.isHapticsEnabled()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L212 | `hapticManager: HapticManager.shared,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopShared/Haptics/HapticManager.swift` | L10 | `public final class HapticManager: HapticGenerating {` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopShared/Haptics/HapticManager.swift` | L12 | `public static let shared = HapticManager()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L25 | `private let hapticManager: HapticGenerating` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L37 | `hapticManager: HapticGenerating,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L44 | `self.hapticManager = hapticManager` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L103 | `await hapticManager.success()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L110 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L135 | `await hapticManager.success()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L138 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L153 | `await hapticManager.selection()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L157 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L172 | `await hapticManager.playCelebrationPattern()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L174 | `await hapticManager.playAffirmationCompletionPattern()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L176 | `await hapticManager.mediumImpact()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L181 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L185 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L197 | `await hapticManager.success()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L212 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L223 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L233 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L244 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L256 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L260 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L264 | `await hapticManager.error()` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Views/Settings/SettingsView.swift` | L27 | `hapticManager: HapticManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/EnhancedAffirmationCard.swift` | L32 | `///   - haptics: The haptic feedback generator to use (defaults to HapticManager.shared)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/EnhancedAffirmationCard.swift` | L225 | `haptics: HapticManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/EnhancedAffirmationCard.swift` | L245 | `haptics: HapticManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/EnhancedAffirmationCard.swift` | L265 | `haptics: HapticManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/EnhancedAffirmationCard.swift` | L286 | `haptics: HapticManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/Components/Cards/EnhancedAffirmationCard.swift` | L305 | `haptics: HapticManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L56 | `private let hapticManager: HapticGenerating` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L66 | `hapticManager: HapticGenerating` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L72 | `self.hapticManager = hapticManager` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L150 | `let viewModel = SettingsViewModel(themeManager: ThemeManager.shared, hapticManager: HapticManager.shared)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L56 | `private let hapticManager: HapticGenerating` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L66 | `hapticManager: HapticGenerating` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L72 | `self.hapticManager = hapticManager` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L150 | `let viewModel = SettingsViewModel(themeManager: ThemeManager.shared, hapticManager: HapticManager.shared)` |

</details>

---

<details>
            <summary>Grep search for "protocol ThemeManaging" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Theme/ThemeManaging.swift` | L4 | `public protocol ThemeManaging {` |

</details>

---

Read file: Sources/NeuroLoopCore/Theme/ThemeManaging.swift

---

<details>
            <summary>Grep search for "protocol HapticGenerating" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopShared/Haptics/HapticGenerating.swift` | L4 | `public protocol HapticGenerating {` |

</details>

---

Read file: Sources/NeuroLoopShared/Haptics/HapticGenerating.swift

---

<details>
            <summary>Grep search for "Theme" • **9** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L5 | `@StateObject private var themeManager = ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L14 | `Picker("Theme", selection: Binding(` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L15 | `get: { themeManager.currentTheme },` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L16 | `set: { themeManager.setTheme($0) }` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L18 | `Text("Default").tag(Theme.defaultLight)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/SettingsView.swift` | L19 | `Text("Dark").tag(Theme.defaultDark)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/MainTabView.swift` | L4 | `@StateObject private var themeManager = ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/MainTabView.swift` | L28 | `.accentColor(themeManager.currentTheme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L25 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L36 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L44 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L49 | `.foregroundColor(viewModel.theme.secondaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L56 | `.fill(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L89 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L96 | `.foregroundColor(viewModel.theme.primaryTextColor.opacity(0.7))` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationsView.swift` | L105 | `.background(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L34 | `viewModel.theme.backgroundColor` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L43 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L48 | `.background(viewModel.theme.cardBackgroundColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L52 | `.stroke(viewModel.theme.borderColor, lineWidth: 1)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L60 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L73 | `.background(viewModel.theme.cardBackgroundColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L77 | `.stroke(viewModel.theme.borderColor, lineWidth: 1)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L85 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L106 | `.background(isValid ? viewModel.theme.accentColor : viewModel.theme.disabledColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AddAffirmationView.swift` | L150 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L31 | `viewModel.theme.backgroundColor` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L39 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L43 | `.background(viewModel.theme.cardBackgroundColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L45 | `.shadow(color: viewModel.theme.shadowColor.opacity(0.1), radius: 5, x: 0, y: 2)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L54 | `.foregroundColor(viewModel.theme.secondaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L68 | `.foregroundColor(affirmation.isFavorite ? viewModel.theme.accentColor : viewModel.theme.secondaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L72 | `.background(viewModel.theme.cardBackgroundColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L74 | `.shadow(color: viewModel.theme.shadowColor.opacity(0.1), radius: 5, x: 0, y: 2)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L102 | `color: viewModel.theme.accentColor` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L111 | `.foregroundColor(viewModel.theme.secondaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L122 | `.background(viewModel.theme.cardBackgroundColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L124 | `.shadow(color: viewModel.theme.shadowColor.opacity(0.1), radius: 5, x: 0, y: 2)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L142 | `.foregroundColor(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L148 | `.stroke(viewModel.theme.accentColor, lineWidth: 1)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L159 | `.foregroundColor(viewModel.theme.errorColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L165 | `.stroke(viewModel.theme.errorColor, lineWidth: 1)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/AffirmationDetailView.swift` | L213 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L27 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L43 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L59 | `themeManager: ThemeManager.shared` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L73 | `viewModel.theme.backgroundColor` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L121 | `.accentColor(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L136 | `.foregroundColor(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L140 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L144 | `.foregroundColor(viewModel.theme.secondaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L153 | `.background(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L164 | `.foregroundColor(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L170 | `.foregroundColor(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L174 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L180 | `.foregroundColor(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L184 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L190 | `.foregroundColor(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L194 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L202 | `.foregroundColor(viewModel.theme.secondaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L206 | `.background(viewModel.theme.cardBackgroundColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L235 | `.background(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L237 | `.shadow(color: viewModel.theme.accentColor.opacity(0.2), radius: 8, x: 0, y: 4)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L252 | `theme: viewModel.theme,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L277 | `.foregroundColor(viewModel.theme.secondaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L281 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L289 | `.foregroundColor(viewModel.theme.secondaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L293 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L301 | `backgroundColor: viewModel.theme.backgroundColor,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L302 | `foregroundColor: viewModel.theme.accentColor,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L310 | `.foregroundColor(viewModel.theme.accentColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L316 | `.foregroundColor(viewModel.theme.secondaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L320 | `.foregroundColor(viewModel.theme.primaryTextColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L334 | `.background(viewModel.canRecordRepetition ? viewModel.theme.accentColor : Color.gray)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/Views/HomeView.swift` | L341 | `.background(viewModel.theme.cardBackgroundColor)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L26 | `private let themeManager: ThemeManaging` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L38 | `themeManager: ThemeManaging` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L45 | `self.themeManager = themeManager` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L282 | `public var theme: Theme {` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopApp/ViewModels/AffirmationViewModel.swift` | L283 | `themeManager.currentTheme` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopCore/Services/PremiumFeatureUnlocker.swift` | L105 | `case .customThemes:` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L13 | `themeManager.setTheme(isDarkModeEnabled ? Theme.defaultDark : Theme.defaultLight)` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L55 | `private let themeManager: ThemeManaging` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L65 | `themeManager: ThemeManaging,` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L71 | `self.themeManager = themeManager` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopUI/ViewModels/SettingsViewModel.swift` | L150 | `let viewModel = SettingsViewModel(themeManager: ThemeManager.shared, hapticManager: HapticManager.shared)` |

</details>

---

<details>
            <summary>Grep search for "struct Theme" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Sources/NeuroLoopShared/Theme.swift` | L3 | `public struct Theme: Hashable {` |
| `file:///Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/Tests/NeuroLoopTests/Integration/PremiumFeatureTests.swift` | L187 | `struct Theme {` |

</details>

---

Read file: Sources/NeuroLoopShared/Theme.swift

---

```diff
      private var mockDataExportService: MockDataExportService!
      private var userDefaults: UserDefaults!
+     private var mockThemeManager: MockThemeManager!
+     private var mockHapticManager: MockHapticManager!
      
      override func setUp() {
```

```diff
          mockPurchaseManager = MockPurchaseManager()
          mockDataExportService = MockDataExportService()
+         mockThemeManager = MockThemeManager()
+         mockHapticManager = MockHapticManager()
          
          viewModel = SettingsViewModel(
              userDefaults: userDefaults,
              purchaseManager: mockPurchaseManager,
-             dataExportService: mockDataExportService
+             dataExportService: mockDataExportService,
+             themeManager: mockThemeManager,
+             hapticManager: mockHapticManager
          )
      }
```

```diff
          }
      }
+ }
+ 
+ private class MockThemeManager: ThemeManaging {
+     var currentTheme: Theme = .defaultLight
+     func setTheme(_ theme: Theme) { currentTheme = theme }
+ }
+ 
+ private class MockHapticManager: HapticGenerating {
+     func lightImpact() async {}
+     func mediumImpact() async {}
+     func heavyImpact() async {}
+     func success() async {}
+     func error() async {}
+     func warning() async {}
+     func selection() async {}
+     func playAffirmationCompletionPattern() async {}
+     func playCelebrationPattern() async {}
+     func playStreakCompletionPattern() async {}
  } 
```

---

```diff
  import NeuroLoopInterfaces
  import NeuroLoopUI
+ import NeuroLoopShared
+ import SwiftUI
  
  @available(iOS 17.0, macOS 14.0, *)
```

---

```diff
      private var mockHapticManager: MockHapticManager!
      
-     override func setUp() {
+     @MainActor override func setUp() {
          super.setUp()
          userDefaults = UserDefaults(suiteName: #function)
```

---

The test file `SettingsViewModelTests.swift` is now updated to:

- Provide mocks for `ThemeManaging` and `HapticGenerating` and pass them to `SettingsViewModel`.
- Include minimal mock implementations for both protocols.
- Import the necessary modules for `Theme` and `HapticGenerating`.
- Mark `setUp()` with `@MainActor` to resolve actor isolation issues.

You can now run the test suite to verify that the `SettingsViewModelTests` are correctly set up and that the mocks are properly injected. If there are any further test failures, please provide the error output for additional debugging.

---


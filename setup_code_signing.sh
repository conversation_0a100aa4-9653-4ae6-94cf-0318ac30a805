#!/bin/bash

# Exit on error
set -e

echo "Setting up code signing configuration..."

# Clean build folder
xcodebuild clean -project NeuroLoopApp.xcodeproj -scheme NeuroLoopApp

# Create a temporary project.pbxproj file
cp NeuroLoopApp.xcodeproj/project.pbxproj NeuroLoopApp.xcodeproj/project.pbxproj.bak

# Update project settings using sed
sed -i '' 's/CODE_SIGN_STYLE = Automatic;/CODE_SIGN_STYLE = Manual;/g' NeuroLoopApp.xcodeproj/project.pbxproj
sed -i '' 's/DEVELOPMENT_TEAM = .*;/DEVELOPMENT_TEAM = "";/g' NeuroLoopApp.xcodeproj/project.pbxproj
sed -i '' 's/PROVISIONING_PROFILE_SPECIFIER = .*;/PROVISIONING_PROFILE_SPECIFIER = "";/g' NeuroLoopApp.xcodeproj/project.pbxproj
sed -i '' 's/CODE_SIGN_IDENTITY = .*;/CODE_SIGN_IDENTITY = "Apple Distribution";/g' NeuroLoopApp.xcodeproj/project.pbxproj

echo "Code signing configuration updated. Please follow these steps:"
echo "1. Open Xcode and select the NeuroLoopApp project"
echo "2. Go to the 'Signing & Capabilities' tab"
echo "3. Select your team from the dropdown"
echo "4. Ensure the bundle identifier matches 'com.neuroloop.NeuroLoopApp'"
echo "5. Xcode will automatically generate a provisioning profile"
echo "6. Clean and rebuild the project"
echo "7. Archive the app for distribution"
echo ""
echo "If you need to revert the changes, run: mv NeuroLoopApp.xcodeproj/project.pbxproj.bak NeuroLoopApp.xcodeproj/project.pbxproj" 
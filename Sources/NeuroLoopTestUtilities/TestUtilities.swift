import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Common test utilities for NeuroLoop tests
public enum TestUtilities {
    /// Creates a test affirmation with default values
    public static func createTestAffirmation(
        text: String = "Test affirmation",
        category: AffirmationCategory = .confidence,
        isFavorite: Bool = false,
        hasRecording: Bool = false,
        todayProgress: Double = 0.0,
        cycleProgress: Double = 0.0
    ) async -> any AffirmationProtocol {
        await AffirmationStub(
            text: text,
            category: category,
            recordingURL: hasRecording ? URL(string: "file:///test.m4a") : nil,
            isFavorite: isFavorite,
            todayProgress: todayProgress,
            cycleProgress: cycleProgress
        )
    }

    /// Creates a test streak info with default values
    public static func createTestStreakInfo(
        currentStreak: Int = 0,
        longestStreak: Int = 0,
        completedCycles: Int = 0,
        hasActiveCycle: Bool = false,
        cycleStartDate: Date? = nil,
        lastRepetitionDate: Date? = nil
    ) -> StreakInfo {
        StreakInfo(
            currentStreak: currentStreak,
            longestStreak: longestStreak,
            completedCycles: completedCycles,
            hasActiveCycle: hasActiveCycle,
            cycleStartDate: cycleStartDate,
            lastRepetitionDate: lastRepetitionDate
        )
    }

    /// Creates a test progress info with default values
    public static func createTestProgressInfo(
        todayProgress: Double = 0.0,
        cycleProgress: Double = 0.0,
        currentDay: Int = 1,
        totalDays: Int = 7,
        currentRepetitions: Int = 0,
        totalRepetitions: Int = 10,
        hasTodayQuotaMet: Bool = false,
        isCycleComplete: Bool = false,
        hasActiveCycle: Bool = false
    ) -> ProgressInfo {
        ProgressInfo(
            todayProgress: todayProgress,
            cycleProgress: cycleProgress,
            currentDay: currentDay,
            totalDays: totalDays,
            currentRepetitions: currentRepetitions,
            totalRepetitions: totalRepetitions,
            hasTodayQuotaMet: hasTodayQuotaMet,
            isCycleComplete: isCycleComplete,
            hasActiveCycle: hasActiveCycle
        )
    }
}

#if os(iOS)
    import SwiftUI
    import UIKit

    extension ThemeColor {
        public var asColor: Color {
            Color(
                uiColor: UIColor { traitCollection in
                    switch traitCollection.userInterfaceStyle {
                    case .light:
                        return UIColor(light)
                    case .dark:
                        return UIColor(dark)
                    case .unspecified:
                        return UIColor(light)
                    @unknown default:
                        return UIColor(light)
                    }
                }
            )
        }
    }
#endif

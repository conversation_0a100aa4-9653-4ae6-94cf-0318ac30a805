import SwiftUI

public struct ThemeColor {
    public let light: Color
    public let dark: Color

    public init(light: Color, dark: Color) {
        self.light = light
        self.dark = dark
    }

    public var asGradient: LinearGradient? {
        switch self {
        case let color where color.light == color.dark:
            return LinearGradient(
                gradient: Gradient(colors: [color.light, color.light.opacity(0.8)]),
                startPoint: .top,
                endPoint: .bottom
            )
        default:
            return nil
        }
    }
}

extension Color {
    func asFallbackGradient() -> LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [self, self.opacity(0.8)]),
            startPoint: .top,
            endPoint: .bottom
        )
    }
}

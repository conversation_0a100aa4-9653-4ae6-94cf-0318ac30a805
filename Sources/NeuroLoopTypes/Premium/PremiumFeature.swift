import Foundation

public enum PremiumFeature: String, CaseIterable {
    case unlimitedAffirmations
    case customThemes
    case advancedAnalytics
    case dataExport

    public var title: String {
        switch self {
        case .unlimitedAffirmations: return "Unlimited Affirmations"
        case .customThemes: return "Custom Themes"
        case .advancedAnalytics: return "Advanced Analytics"
        case .dataExport: return "Data Export"
        }
    }

    public var description: String {
        switch self {
        case .unlimitedAffirmations:
            return
                "Create and manage unlimited affirmations with advanced organization and tracking"
        case .customThemes:
            return "Personalize your experience with custom gradient themes and visual effects"
        case .advancedAnalytics:
            return
                "Get detailed insights into your practice with advanced progress tracking and streak analytics"
        case .dataExport:
            return "Export and backup your affirmations, progress, and statistics for safekeeping"
        }
    }
}

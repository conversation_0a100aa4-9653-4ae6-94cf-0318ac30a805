import SwiftUI

public struct GradientButtonModifier: ViewModifier {
    let fill: AnyView
    let theme: Theme?

    public init(gradient: LinearGradient, theme: Theme? = nil) {
        self.fill = AnyView(gradient)
        self.theme = theme
    }

    public init(color: Color, theme: Theme? = nil) {
        self.fill = AnyView(color)
        self.theme = theme
    }

    public func body(content: Content) -> some View {
        content
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                ZStack {
                    fill
                        .cornerRadius(12)
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.clear)
                        .shadow(
                            color: theme?.shadowColor.color.opacity(0.3) ?? Color.black.opacity(0.2),
                            radius: 15,
                            x: 0,
                            y: 8
                        )
                }
            )
            .foregroundColor(.white)
    }
}

public extension View {
    func gradientButton(gradient: LinearGradient, theme: Theme? = nil) -> some View {
        modifier(GradientButtonModifier(gradient: gradient, theme: theme))
    }

    func gradientButton(color: Color, theme: Theme? = nil) -> some View {
        modifier(GradientButtonModifier(color: color, theme: theme))
    }

    func themeGradientButton(theme: Theme) -> some View {
        let gradient = theme.accentColor.asGradient ??
            LinearGradient(
                gradient: Gradient(colors: [
                    theme.accentColor.asColor,
                    theme.accentColor.asColor.opacity(0.8)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        return modifier(GradientButtonModifier(gradient: gradient, theme: theme))
    }
}
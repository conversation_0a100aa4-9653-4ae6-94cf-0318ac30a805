import Foundation

public enum MainTab: String, CaseIterable {
    case home
    case affirmations
    case settings

    public var title: String {
        switch self {
        case .home: return "Home"
        case .affirmations: return "Affirmations"
        case .settings: return "Settings"
        }
    }

    public var icon: String {
        switch self {
        case .home: return "house.fill"
        case .affirmations: return "list.bullet"
        case .settings: return "gear"
        }
    }
}

import Foundation

/// Represents a milestone in the user's streak journey
public struct StreakMilestone: Identifiable {
    public let id = UUID()
    public let type: MilestoneType
    public let value: Int
    public let isAchieved: Bool

    public enum MilestoneType {
        case currentStreak
        case longestStreak
        case completedCycles
        case upcomingStreak
    }

    public init(
        type: MilestoneType,
        value: Int,
        isAchieved: Bool
    ) {
        self.type = type
        self.value = value
        self.isAchieved = isAchieved
    }
}

/// Represents an achievement related to streaks
public struct StreakAchievement: Identifiable {
    public let id = UUID()
    public let title: String
    public let description: String
    public let icon: String
    public let isUnlocked: Bool

    public init(
        title: String,
        description: String,
        icon: String,
        isUnlocked: Bool
    ) {
        self.title = title
        self.description = description
        self.icon = icon
        self.isUnlocked = isUnlocked
    }
}

/// Represents a day in the streak visualization
public struct StreakDay: Identifiable {
    public let id: UUID
    public let date: Date
    public let progress: Double
    public let isComplete: Bool
    public let repetitions: Int
    public let isInCurrentMonth: Bool

    public init(
        id: UUID = UUID(),
        date: Date,
        progress: Double,
        isComplete: Bool,
        repetitions: Int,
        isInCurrentMonth: Bool
    ) {
        self.id = id
        self.date = date
        self.progress = progress
        self.isComplete = isComplete
        self.repetitions = repetitions
        self.isInCurrentMonth = isInCurrentMonth
    }
}

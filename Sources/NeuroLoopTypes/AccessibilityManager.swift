import Foundation
import SwiftUI
import Combine
#if canImport(UIKit)
import UIKit
#endif

public protocol AccessibilityManagerProtocol {
    @MainActor func announce(_ message: String)
    @MainActor func isVoiceOverRunning() -> Bool
    @MainActor func isReduceMotionEnabled() -> <PERSON><PERSON>
    @MainActor func setAccessibilityFocus(_ id: Any?)
    @MainActor func addCustomAction(_ label: String, action: @escaping () -> Void) -> Any?
}

@MainActor
public class AccessibilityManager: AccessibilityManagerProtocol {
    public static let shared = AccessibilityManager()
    private init() {}

    // MARK: - Announce
    @MainActor
    public func announce(_ message: String) {
        #if canImport(UIKit)
        UIAccessibility.post(notification: .announcement, argument: message)
        #endif
    }

    // MARK: - VoiceOver
    @MainActor
    public func isVoiceOverRunning() -> Bool {
        #if canImport(UIKit)
        return UIAccessibility.isVoiceOverRunning
        #else
        return false
        #endif
    }

    // MARK: - Reduce Motion
    @MainActor
    public func isReduceMotionEnabled() -> Bo<PERSON> {
        #if canImport(UIKit)
        return UIAccessibility.isReduceMotionEnabled
        #else
        return false
        #endif
    }

    // MARK: - Focus
    @MainActor
    public func setAccessibilityFocus(_ id: Any?) {
        #if canImport(UIKit)
        UIAccessibility.post(notification: .layoutChanged, argument: id)
        #endif
    }

    // MARK: - Custom Actions
    @MainActor
    public func addCustomAction(_ label: String, action: @escaping () -> Void) -> Any? {
        #if canImport(UIKit)
        return UIAccessibilityCustomAction(name: label) { _ in
            action()
            return true
        }
        #else
        return nil
        #endif
    }
}

// MARK: - SwiftUI Extensions
#if canImport(SwiftUI)
public extension View {
    func accessibilityAnnounce(_ message: String) -> some View {
        onAppear {
            AccessibilityManager.shared.announce(message)
        }
    }

    func accessibilityIf(_ condition: Bool, _ modifier: (Self) -> some View) -> some View {
        if condition {
            return AnyView(modifier(self))
        } else {
            return AnyView(self)
        }
    }
}
#endif

// MARK: - Documentation
/**
 AccessibilityManager provides centralized helpers for VoiceOver announcements, custom actions, focus management, and system accessibility settings. Use it to enhance accessibility throughout your app.
 */ 
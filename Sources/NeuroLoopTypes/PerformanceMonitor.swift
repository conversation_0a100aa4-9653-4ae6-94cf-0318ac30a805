import Foundation
import os.log
import os.signpost
#if canImport(UIKit)
import UIKit
#endif

public protocol PerformanceMonitorProtocol: Sendable {
    @MainActor func startLaunchTimer()
    @MainActor func stopLaunchTimer()
    @MainActor func logSlowOperation(_ name: String, block: () -> Void, threshold: TimeInterval)
    @MainActor func measure<T>(_ name: String, block: () -> T) -> T
    @MainActor func startFrameRateMonitoring() -> Double
    @MainActor func stopFrameRateMonitoring() -> Double
    @MainActor func currentMemoryUsage() -> UInt64
    @MainActor func checkMemoryWarning()
}

@MainActor
public class PerformanceMonitor: PerformanceMonitorProtocol {
    public static let shared = PerformanceMonitor()
    // Logger is only used in @available blocks
    private let logger = { () -> Any? in
        if #available(iOS 14.0, macOS 11.0, *) {
            return Logger(subsystem: "com.neuroloop.NeuroLoop100", category: "PerformanceMonitor")
        } else {
            return nil
        }
    }()
    private var launchStart: DispatchTime?
    private var launchEnd: DispatchTime?
#if canImport(UIKit)
    private var displayLink: CADisplayLink?
    private var frameCount: Int = 0
    private var frameStartTime: CFTimeInterval = 0
    private var lastFrameRate: Double = 0
#endif
    private var memoryWarningThreshold: UInt64 = 200 * 1024 * 1024 // 200MB

    private init() {}

    // MARK: - Launch Time
    @MainActor
    public func startLaunchTimer() {
        launchStart = DispatchTime.now()
    }

    @MainActor
    public func stopLaunchTimer() {
        launchEnd = DispatchTime.now()
        if let start = launchStart, let end = launchEnd {
            let elapsed = Double(end.uptimeNanoseconds - start.uptimeNanoseconds) / 1_000_000_000
            if #available(iOS 14.0, macOS 11.0, *), let logger = logger as? Logger {
                logger.log("App launch time: \(elapsed, privacy: .public) seconds")
            } else {
                print("[PerformanceMonitor] App launch time: \(elapsed) seconds")
            }
        }
    }

    // MARK: - Slow Operations
    @MainActor
    public func logSlowOperation(_ name: String, block: () -> Void, threshold: TimeInterval) {
        let start = DispatchTime.now()
        block()
        let end = DispatchTime.now()
        let elapsed = Double(end.uptimeNanoseconds - start.uptimeNanoseconds) / 1_000_000_000
        if elapsed > threshold {
            if #available(iOS 14.0, macOS 11.0, *), let logger = logger as? Logger {
                logger.warning("Slow operation [\(name, privacy: .public)]: \(elapsed, privacy: .public) seconds")
            } else {
                print("[PerformanceMonitor] Slow operation [\(name)]: \(elapsed) seconds")
            }
        }
    }

    @MainActor
    public func measure<T>(_ name: String, block: () -> T) -> T {
        let start = DispatchTime.now()
        let result = block()
        let end = DispatchTime.now()
        let elapsed = Double(end.uptimeNanoseconds - start.uptimeNanoseconds) / 1_000_000_000
        if #available(iOS 14.0, macOS 11.0, *), let logger = logger as? Logger {
            logger.log("Measured [\(name, privacy: .public)]: \(elapsed, privacy: .public) seconds")
        } else {
            print("[PerformanceMonitor] Measured [\(name)]: \(elapsed) seconds")
        }
        return result
    }

    // MARK: - Frame Rate
#if canImport(UIKit)
    @MainActor
    public func startFrameRateMonitoring() -> Double {
        self.frameCount = 0
        self.frameStartTime = CACurrentMediaTime()
        self.lastFrameRate = 0
        self.displayLink = CADisplayLink(target: self, selector: #selector(frameTick))
        self.displayLink?.add(to: .main, forMode: .common)
        return 0.0 // Initial frame rate
    }

    @MainActor
    public func stopFrameRateMonitoring() -> Double {
        self.displayLink?.invalidate()
        self.displayLink = nil
        return self.lastFrameRate
    }

    @objc private func frameTick() {
        self.frameCount += 1
        let elapsed = CACurrentMediaTime() - self.frameStartTime
        if elapsed >= 1.0 {
            self.lastFrameRate = Double(self.frameCount) / elapsed
            if #available(iOS 14.0, macOS 11.0, *), let logger = logger as? Logger {
                logger.log("Frame rate: \(self.lastFrameRate, privacy: .public) FPS")
            } else {
                print("[PerformanceMonitor] Frame rate: \(self.lastFrameRate) FPS")
            }
            self.frameCount = 0
            self.frameStartTime = CACurrentMediaTime()
        }
    }
#else
    @MainActor
    public func startFrameRateMonitoring() -> Double { return 0.0 }
    @MainActor
    public func stopFrameRateMonitoring() -> Double { return 0.0 }
#endif

    // MARK: - Memory Usage
    @MainActor
    public func currentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        if kerr == KERN_SUCCESS {
            return info.resident_size
        } else {
            if #available(iOS 14.0, macOS 11.0, *), let logger = logger as? Logger {
                logger.error("Error with task_info(): \(kerr)")
            } else {
                print("[PerformanceMonitor] Error with task_info(): \(kerr)")
            }
            return 0
        }
    }

    @MainActor
    public func checkMemoryWarning() {
        let usage = self.currentMemoryUsage()
        if usage > self.memoryWarningThreshold {
            if #available(iOS 14.0, macOS 11.0, *), let logger = logger as? Logger {
                logger.warning("Memory usage high: \(usage / 1024 / 1024, privacy: .public) MB")
            } else {
                print("[PerformanceMonitor] Memory usage high: \(usage / 1024 / 1024) MB")
            }
        }
    }
}

// MARK: - SwiftUI Extension
#if canImport(SwiftUI) && (os(iOS) || os(macOS))
import SwiftUI
@available(iOS 13.0, macOS 10.15, *)
public extension View {
    func measurePerformance(_ name: String) -> some View {
        PerformanceMonitor.shared.measure(name) { }
        return self
    }
}
#endif

// MARK: - Documentation
/**
 PerformanceMonitor provides tools for tracking app launch time, memory usage, frame rate, and slow operations. Use it to log and analyze performance bottlenecks and optimize your app for all devices.
 */ 
import Foundation

// Import ImpactStyle from the same module
@_exported import struct Foundation.URL

public enum ImpactStyle: Sendable {
    case light
    case medium
    case heavy
    case soft
    case rigid
}

@MainActor
public protocol HapticGenerating {
    func playSuccess() async
    func playError() async
    func playWarning() async
    func playSelection() async
    func playImpact(style: ImpactStyle) async

    // Additional methods used by AffirmationViewModel
    func playCelebrationPattern() async
    func playAffirmationCompletionPattern() async
    func mediumImpact() async
}

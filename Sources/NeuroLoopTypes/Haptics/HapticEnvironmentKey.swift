import SwiftUI

private final class NoopHapticManager: HapticGenerating {
    nonisolated func playSuccess() async {}
    nonisolated func playError() async {}
    nonisolated func playWarning() async {}
    nonisolated func playSelection() async {}
    nonisolated func playImpact(style: ImpactStyle) async {}

    // Additional methods
    nonisolated func playCelebrationPattern() async {}
    nonisolated func playAffirmationCompletionPattern() async {}
    nonisolated func mediumImpact() async {}
}

private struct HapticManagerKey: EnvironmentKey {
    static let defaultValue: (any HapticGenerating)? = nil
}

extension EnvironmentValues {
    public var hapticManager: (any HapticGenerating)? {
        get { self[HapticManagerKey.self] }
        set { self[HapticManagerKey.self] = newValue }
    }
}

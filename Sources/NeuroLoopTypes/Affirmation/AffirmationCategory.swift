import Foundation
import SwiftUI

/// Categories for affirmations
public enum AffirmationCategory: String, CaseIterable, Identifiable, Codable, Sendable {
    case confidence = "Confidence"
    case mindfulness = "Mindfulness"
    case gratitude = "Gratitude"
    case health = "Health"
    case success = "Success"
    case relationships = "Relationships"
    case selfLove = "Self Love"
    case personalGrowth = "Personal Growth"
    case meditation = "Meditation"
    case abundance = "Abundance"
    case personal = "Personal"
    case custom = "Custom"

    public var id: String { rawValue }

    public var displayName: String {
        // Use raw values as fallback for localization
        if #available(macOS 12, iOS 15, *) {
            switch self {
            case .confidence:
                return String(localized: "affirmation.category.confidence")
            case .mindfulness:
                return String(localized: "affirmation.category.mindfulness")
            case .gratitude:
                return String(localized: "affirmation.category.gratitude")
            case .health:
                return String(localized: "affirmation.category.health")
            case .success:
                return String(localized: "affirmation.category.success")
            case .relationships:
                return String(localized: "affirmation.category.relationships")
            case .selfLove:
                return String(localized: "affirmation.category.self_love")
            case .personalGrowth:
                return String(localized: "affirmation.category.personal_growth")
            case .meditation:
                return String(localized: "affirmation.category.meditation")
            case .abundance:
                return String(localized: "affirmation.category.abundance")
            case .personal:
                return String(localized: "affirmation.category.personal")
            case .custom:
                return String(localized: "affirmation.category.custom")
            }
        } else {
            // Fallback to raw values for older OS versions
            return self.rawValue
        }
    }

    public var iconName: String {
        switch self {
        case .confidence:
            return "person.fill"
        case .mindfulness:
            return "brain.head.profile"
        case .gratitude:
            return "hands.sparkles"
        case .health:
            return "heart.fill"
        case .success:
            return "trophy.fill"
        case .relationships:
            return "person.2.fill"
        case .selfLove:
            return "heart.circle.fill"
        case .personalGrowth:
            return "chart.line.uptrend.xyaxis"
        case .meditation:
            return "sparkles"
        case .abundance:
            return "leaf.fill"
        case .personal:
            return "person.fill"
        case .custom:
            return "star.fill"
        }
    }

    public var color: Color {
        switch self {
        case .confidence: return .blue
        case .mindfulness: return .purple
        case .gratitude: return .pink
        case .personalGrowth: return .green
        case .health: return .red
        case .relationships: return .orange
        case .success: return .green
        case .selfLove: return .pink
        case .meditation: return .teal
        case .personal: return .gray
        case .custom: return .gray
        default: return .gray
        }
    }
}

/*
 MemoryStorageService.swift

 Temporary in-memory stub for SwiftData, implemented to work around Swift 6.1 actor isolation issues.

 - Purpose: Replaces SwiftData-backed repositories with an in-memory, actor-safe service using DTOs.
 - Architecture: Stores DTOs in an actor-isolated array. Conforms to AffirmationRepositoryProtocol for seamless swapping.
 - Feature Flag: Controlled by USING_SWIFTDATA_STUB in ServiceFactory.swift.
 - Migration: Remove this file and DTOs, and set USING_SWIFTDATA_STUB = false when SwiftData is fixed.
 - Limitations: No persistence between launches, not for production, limited feature parity with SwiftData.
*/

import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes

/// In-memory stub service for affirmations, replacing SwiftData for Swift 6.1 compatibility
public final class MemoryStorageService: AffirmationRepositoryProtocol, @unchecked Sendable {
    // Actor-isolated storage for DTOs
    private actor Storage {
        var affirmations: [AffirmationDTO] = []
        // Add more collections for other DTOs as needed

        func getAll() -> [AffirmationDTO] {
            affirmations
        }

        func get(id: UUID) -> AffirmationDTO? {
            affirmations.first { $0.id == id }
        }

        func append(_ dto: AffirmationDTO) {
            affirmations.append(dto)
        }

        func update(_ dto: AffirmationDTO) throws {
            guard let index = affirmations.firstIndex(where: { $0.id == dto.id }) else {
                throw AffirmationError.notFound
            }
            affirmations[index] = dto
        }

        func remove(id: UUID) throws {
            guard let index = affirmations.firstIndex(where: { $0.id == id }) else {
                throw AffirmationError.notFound
            }
            affirmations.remove(at: index)
        }
    }

    private let storage = Storage()

    /// Initialize the in-memory service
    public init() {
        // Create a default test affirmation for development
        Task {
            await createDefaultAffirmation()
        }
    }

    /// Create a default affirmation for testing
    private func createDefaultAffirmation() async {
        let testAffirmation = AffirmationDTO(
            id: UUID(),
            text: "I am confident and capable in everything I do",
            category: .confidence,
            recordingURL: nil,
            createdAt: Date(),
            updatedAt: Date(),
            completedCycles: 0,
            currentRepetitions: 0,
            lastRepetitionDate: nil,
            energyLevel: 0.5,
            moodRating: nil,
            notes: nil,
            isFavorite: false,
            playCount: 0,
            hasActiveCycle: true,
            currentCycleDay: 1,
            cycleStartDate: Date(),
            cycles: [],
            longestStreak: 0
        )
        await storage.append(testAffirmation)
        print("MemoryStorageService: Created default test affirmation")
    }

    /// Fetch all affirmations (DTOs)
    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        let dtos = await storage.getAll()
        return dtos.map { $0 as any AffirmationProtocol }
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        await storage.get(id: id) as (any AffirmationProtocol)?
    }

    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
        async throws -> any AffirmationProtocol
    {
        let dto = AffirmationDTO(
            id: UUID(),
            text: text,
            category: category,
            recordingURL: recordingURL,
            createdAt: Date(),
            updatedAt: Date(),
            completedCycles: 0,
            currentRepetitions: 0,
            lastRepetitionDate: nil,
            energyLevel: 0.5,
            moodRating: nil,
            notes: nil,
            isFavorite: false,
            playCount: 0,
            hasActiveCycle: false,
            currentCycleDay: 1,
            cycleStartDate: nil,
            cycles: []
        )
        await storage.append(dto)
        return dto as any AffirmationProtocol
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        guard let dto = affirmation as? AffirmationDTO else {
            throw AffirmationError.invalidData
        }
        try await storage.update(dto)
    }

    public func deleteAffirmation(id: UUID) async throws {
        try await storage.remove(id: id)
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        let dtos = await storage.getAll()
        return dtos.filter { $0.category == category }.map { $0 as any AffirmationProtocol }
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        let dtos = await storage.getAll()
        return dtos.filter { $0.isFavorite }.map { $0 as any AffirmationProtocol }
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        let dtos = await storage.getAll()
        return dtos.first { $0.hasActiveCycle } as (any AffirmationProtocol)?
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws {
        guard var dto = affirmation as? AffirmationDTO else {
            throw AffirmationError.invalidData
        }

        let newCount = dto.currentRepetitions + 1
        print("MemoryStorageService: Recording repetition, count: \(dto.currentRepetitions) -> \(newCount)")

        // Store all properties before creating the new DTO to avoid async access
        let dtoId = dto.id
        let dtoText = dto.text
        let dtoCategory = dto.category
        let dtoRecordingURL = dto.recordingURL
        let dtoCreatedAt = dto.createdAt
        let dtoCompletedCycles = dto.completedCycles
        let dtoEnergyLevel = dto.energyLevel
        let dtoMoodRating = dto.moodRating
        let dtoNotes = dto.notes
        let dtoIsFavorite = dto.isFavorite
        let dtoPlayCount = dto.playCount
        let dtoCurrentCycleDay = dto.currentCycleDay
        let dtoCycleStartDate = dto.cycleStartDate
        let dtoCycles = dto.cycles
        let dtoLongestStreak = dto._longestStreak  // Access the underlying property directly

        dto = AffirmationDTO(
            id: dtoId,
            text: dtoText,
            category: dtoCategory,
            recordingURL: dtoRecordingURL,
            createdAt: dtoCreatedAt,
            updatedAt: Date(),
            completedCycles: dtoCompletedCycles,
            currentRepetitions: newCount,
            lastRepetitionDate: Date(),
            energyLevel: dtoEnergyLevel,
            moodRating: dtoMoodRating,
            notes: dtoNotes,
            isFavorite: dtoIsFavorite,
            playCount: dtoPlayCount,
            hasActiveCycle: true, // Ensure we have an active cycle
            currentCycleDay: dtoCurrentCycleDay,
            cycleStartDate: dtoCycleStartDate ?? Date(),
            cycles: dtoCycles,
            longestStreak: max(dtoLongestStreak, dtoCurrentCycleDay)
        )
        try await storage.update(dto)

        print("MemoryStorageService: Successfully updated repetition count to \(newCount)")

        // Post notification to update UI
        await MainActor.run {
            NotificationCenter.default.post(
                name: Notification.Name("RepetitionCountChanged"),
                object: nil,
                userInfo: [
                    "count": newCount,
                    "affirmationId": dtoId,
                    "timestamp": Date().timeIntervalSince1970,
                    "source": "MemoryStorageService"
                ]
            )
            print("MemoryStorageService: Posted RepetitionCountChanged notification")
        }
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
        guard var dto = affirmation as? AffirmationDTO else {
            throw AffirmationError.invalidData
        }

        // Store all properties before creating the new DTO to avoid async access
        let dtoId = dto.id
        let dtoText = dto.text
        let dtoCategory = dto.category
        let dtoRecordingURL = dto.recordingURL
        let dtoCreatedAt = dto.createdAt
        let dtoCompletedCycles = dto.completedCycles
        let dtoEnergyLevel = dto.energyLevel
        let dtoMoodRating = dto.moodRating
        let dtoNotes = dto.notes
        let dtoIsFavorite = dto.isFavorite
        let dtoPlayCount = dto.playCount
        let dtoCycles = dto.cycles

        dto = AffirmationDTO(
            id: dtoId,
            text: dtoText,
            category: dtoCategory,
            recordingURL: dtoRecordingURL,
            createdAt: dtoCreatedAt,
            updatedAt: Date(),
            completedCycles: dtoCompletedCycles,
            currentRepetitions: 0,
            lastRepetitionDate: nil,
            energyLevel: dtoEnergyLevel,
            moodRating: dtoMoodRating,
            notes: dtoNotes,
            isFavorite: dtoIsFavorite,
            playCount: dtoPlayCount,
            hasActiveCycle: true,
            currentCycleDay: 1,
            cycleStartDate: Date(),
            cycles: dtoCycles
        )
        try await storage.update(dto)
    }
}

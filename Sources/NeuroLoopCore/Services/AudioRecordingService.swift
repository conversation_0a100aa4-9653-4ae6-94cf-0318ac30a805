import AVFoundation
import Combine
import Foundation
import NeuroLoopInterfaces

/// Service for managing audio recordings (iOS)
#if os(iOS)
    // NOTE: This class is marked @MainActor to conform to AudioRecordingServiceProtocol, but delegate methods are nonisolated and use Task { @MainActor in ... } for main-thread work.
    @MainActor
    public final class AudioRecordingService: NSObject, AudioRecordingServiceProtocol {
        // MARK: - Properties

        private var audioRecorder: AVAudioRecorder?
        private var audioPlayer: AVAudioPlayer?
        private var recordingSession: AVAudioSession
        private var timer: Timer?
        @MainActor private var audioFileManager: AudioFileManager

        private var _isRecording: Bool = false
        private var _recordingTime: TimeInterval = 0
        private var _recordingPower: Double = 0
        private var _recordingURL: URL?
        private var _isPlaying: Bool = false
        private var _playbackProgress: Double = 0
        private var _playbackTime: TimeInterval = 0
        private var _error: Error?

        // Protocol-required properties (no @Published)
        @MainActor public var isRecording: Bool { _isRecording }
        @MainActor public var recordingTime: TimeInterval { _recordingTime }
        @MainActor public var recordingPower: Double { _recordingPower }
        @MainActor public var recordingURL: URL? { _recordingURL }
        @MainActor public var isPlaying: Bool { _isPlaying }
        @MainActor public var playbackProgress: Double { _playbackProgress }
        @MainActor public var playbackTime: TimeInterval { _playbackTime }
        @MainActor public var error: Error? { _error }

        // Publishers for UI observation (optional, not protocol requirements)
        @Published public private(set) var isRecordingPublished: Bool = false
        @Published public private(set) var recordingTimePublished: TimeInterval = 0
        @Published public private(set) var recordingPowerPublished: Double = 0
        @Published public private(set) var recordingURLPublished: URL?
        @Published public private(set) var isPlayingPublished: Bool = false
        @Published public private(set) var playbackProgressPublished: Double = 0
        @Published public private(set) var playbackTimePublished: TimeInterval = 0
        @Published public private(set) var errorPublished: Error?

        // Publisher properties for protocol conformance
        @MainActor public var isRecordingPublisher: Published<Bool>.Publisher {
            $isRecordingPublished
        }
        @MainActor public var recordingTimePublisher: Published<TimeInterval>.Publisher {
            $recordingTimePublished
        }
        @MainActor public var recordingPowerPublisher: Published<Double>.Publisher {
            $recordingPowerPublished
        }
        @MainActor public var recordingURLPublisher: Published<URL?>.Publisher {
            $recordingURLPublished
        }
        @MainActor public var isPlayingPublisher: Published<Bool>.Publisher { $isPlayingPublished }
        @MainActor public var playbackProgressPublisher: Published<Double>.Publisher {
            $playbackProgressPublished
        }
        @MainActor public var playbackTimePublisher: Published<TimeInterval>.Publisher {
            $playbackTimePublished
        }
        @MainActor public var errorPublisher: Published<Error?>.Publisher { $errorPublished }

        // MARK: - Initialization

        public init(audioFileManager: AudioFileManager) {
            self.recordingSession = AVAudioSession.sharedInstance()
            self.audioFileManager = audioFileManager
            super.init()
            setupAudioSession()
        }

        // MARK: - Audio Session Setup

        private func setupAudioSession() {
            do {
                try recordingSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
                try recordingSession.setActive(true)
                print("Audio session setup successfully")
            } catch {
                print("Failed to set up audio session: \(error.localizedDescription)")
                self._error = error
                self.errorPublished = error
            }
        }

        // MARK: - Recording Methods

        @MainActor public func startRecording() async throws {
            print("Starting recording process...")

            // First ensure the audio session is active
            do {
                try recordingSession.setActive(true)
                print("Audio session activated")
            } catch {
                print("Failed to activate audio session: \(error.localizedDescription)")
                throw AudioRecordingError.failedToStartRecording
            }

            // Request microphone permission
            guard await requestPermission() else {
                print("Microphone permission denied")
                throw AudioRecordingError.permissionDenied
            }

            print("Permission granted, preparing to record...")
            let recordingURL = audioFileManager.createUniqueAudioFileURL()
            print("Recording to URL: \(recordingURL.path)")

            let settings: [String: Any] = [
                AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
                AVSampleRateKey: 44100.0,
                AVNumberOfChannelsKey: 1,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue,
            ]

            do {
                audioRecorder = try AVAudioRecorder(url: recordingURL, settings: settings)
                audioRecorder?.delegate = self
                audioRecorder?.isMeteringEnabled = true

                print("Attempting to start recording...")
                if audioRecorder?.record() == true {
                    print("Recording started successfully")
                    self._isRecording = true
                    self.isRecordingPublished = true
                    self._recordingTime = 0
                    self.recordingTimePublished = 0
                    self._recordingURL = recordingURL
                    startMetering()
                } else {
                    print("Failed to start recording")
                    throw AudioRecordingError.recordingFailed
                }
            } catch {
                print("Error creating recorder: \(error.localizedDescription)")
                self._error = error
                self.errorPublished = error
                throw AudioRecordingError.recordingFailed
            }
        }

        @MainActor public func stopRecording() async throws -> URL {
            guard let recorder = audioRecorder, isRecording else {
                throw AudioRecordingError.notRecording
            }

            recorder.stop()
            self._isRecording = false
            self.isRecordingPublished = false
            stopMetering()

            guard let url = recordingURL else {
                throw AudioRecordingError.recordingFailed
            }

            return url
        }

        @MainActor public func deleteRecording() {
            if let url = recordingURL {
                try? audioFileManager.deleteAudioFile(at: url)
                self._recordingURL = nil
                self.recordingURLPublished = nil
            }
        }

        @MainActor public func deleteRecording(at url: URL) async throws {
            try audioFileManager.deleteAudioFile(at: url)
        }

        @MainActor public func getRecordingDuration(for url: URL) async throws -> TimeInterval {
            let asset = AVURLAsset(url: url)
            if #available(iOS 16.0, *) {
                let duration = try await asset.load(.duration)
                return CMTimeGetSeconds(duration)
            } else {
                return CMTimeGetSeconds(asset.duration)
            }
        }

        @MainActor public func getRecordingWaveform(for url: URL) async throws -> [Float] {
            // Stub: Return empty array or implement waveform extraction if needed
            return []
        }

        /// Diagnostic method to test microphone access and audio session setup
        @MainActor public func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double) {
            print("AudioRecordingService: Testing microphone access...")

            // Check if audio session is active
            let audioSession = AVAudioSession.sharedInstance()

            // Try to activate the session with recording category
            do {
                try audioSession.setCategory(.playAndRecord, mode: .spokenAudio, options: [.defaultToSpeaker, .allowBluetooth])
                try audioSession.setActive(true)
                print("AudioRecordingService: Audio session activated successfully")
            } catch {
                print("AudioRecordingService: Failed to activate audio session: \(error.localizedDescription)")
                return (false, "Failed to activate audio session: \(error.localizedDescription)", 0)
            }

            // Check if input is available
            if !audioSession.isInputAvailable {
                print("AudioRecordingService: No microphone input available")
                return (false, "No microphone input available on this device", 0)
            }

            // Check current input device
            if let currentInput = audioSession.currentRoute.inputs.first {
                print("AudioRecordingService: Current input device: \(currentInput.portName)")
            } else {
                print("AudioRecordingService: No input device in current route")
                return (false, "No input device in current audio route", 0)
            }

            // Check permission status
            let permissionStatus = audioSession.recordPermission
            if permissionStatus != .granted {
                print("AudioRecordingService: Microphone permission not granted (status: \(permissionStatus.rawValue))")
                return (false, "Microphone permission not granted", 0)
            }

            // Try to create a test recorder to check if we can actually record
            let testSettings: [String: Any] = [
                AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
                AVSampleRateKey: 44100.0,
                AVNumberOfChannelsKey: 1,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue,
            ]

            let testURL = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("test_recording.m4a")

            do {
                let testRecorder = try AVAudioRecorder(url: testURL, settings: testSettings)
                testRecorder.isMeteringEnabled = true

                if testRecorder.record() {
                    print("AudioRecordingService: Test recording started successfully")

                    // Record for a short time to get audio levels
                    try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

                    testRecorder.updateMeters()
                    let averagePower = Double(testRecorder.averagePower(forChannel: 0))
                    print("AudioRecordingService: Test recording audio level: \(averagePower) dB")

                    // Stop and clean up
                    testRecorder.stop()
                    try? FileManager.default.removeItem(at: testURL)

                    return (true, "Microphone test successful. Audio level: \(averagePower) dB", averagePower)
                } else {
                    print("AudioRecordingService: Failed to start test recording")
                    return (false, "Failed to start test recording", 0)
                }
            } catch {
                print("AudioRecordingService: Error creating test recorder: \(error.localizedDescription)")
                return (false, "Error creating test recorder: \(error.localizedDescription)", 0)
            }
        }

        // MARK: - Playback Methods

        @MainActor public func startPlayback() async throws {
            guard let url = recordingURL else {
                throw AudioRecordingError.noRecording
            }

            do {
                audioPlayer = try AVAudioPlayer(contentsOf: url)
                audioPlayer?.delegate = self
                audioPlayer?.play()
                self._isPlaying = true
                self.isPlayingPublished = true
                startPlaybackTimer()
            } catch {
                throw AudioRecordingError.playbackFailed
            }
        }

        @MainActor public func pausePlayback() {
            audioPlayer?.pause()
            self._isPlaying = false
            self.isPlayingPublished = false
            stopPlaybackTimer()
        }

        @MainActor public func stopPlayback() {
            audioPlayer?.stop()
            self._isPlaying = false
            self.isPlayingPublished = false
            self._playbackProgress = 0
            self.playbackProgressPublished = 0
            self._playbackTime = 0
            self.playbackTimePublished = 0
            stopPlaybackTimer()
        }

        // MARK: - Helper Methods

        private func requestPermission() async -> Bool {
            print("Requesting microphone permission...")

            // Check current permission status
            let authStatus = AVAudioSession.sharedInstance().recordPermission

            if authStatus == .granted {
                print("Microphone permission already granted")
                return true
            }

            if authStatus == .denied {
                print("Microphone permission previously denied")
                // Show alert to direct user to settings
                self._error = AudioRecordingError.permissionDenied
                self.errorPublished = AudioRecordingError.permissionDenied
                return false
            }

            // Request permission if undetermined
            print("Requesting microphone permission from user...")

            if #available(iOS 17.0, *) {
                return await withCheckedContinuation { continuation in
                    AVAudioApplication.requestRecordPermission(completionHandler: { granted in
                        print("Microphone permission request result: \(granted ? "granted" : "denied")")
                        continuation.resume(returning: granted)
                    })
                }
            } else {
                return await withCheckedContinuation { continuation in
                    let session = AVAudioSession.sharedInstance()
                    session.requestRecordPermission { granted in
                        print("Microphone permission request result: \(granted ? "granted" : "denied")")
                        continuation.resume(returning: granted)
                    }
                }
            }
        }

        private func startMetering() {
            timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
                guard let self = self else { return }
                Task { @MainActor in
                    guard let recorder = self.audioRecorder else { return }
                    recorder.updateMeters()
                    self._recordingPower = Double(recorder.averagePower(forChannel: 0))
                    self._recordingTime = recorder.currentTime
                    self.recordingPowerPublished = self._recordingPower
                    self.recordingTimePublished = self._recordingTime
                }
            }
        }

        private func stopMetering() {
            timer?.invalidate()
            timer = nil
        }

        private func startPlaybackTimer() {
            timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
                guard let self = self else { return }
                Task { @MainActor in
                    guard let player = self.audioPlayer else { return }
                    self._playbackTime = player.currentTime
                    self.playbackTimePublished = self._playbackTime
                    self._playbackProgress = player.currentTime / player.duration
                    self.playbackProgressPublished = self._playbackProgress
                }
            }
        }

        private func stopPlaybackTimer() {
            timer?.invalidate()
            timer = nil
        }
    }

    // MARK: - AVAudioRecorderDelegate

    extension AudioRecordingService: AVAudioRecorderDelegate {
        nonisolated public func audioRecorderDidFinishRecording(
            _ recorder: AVAudioRecorder, successfully flag: Bool
        ) {
            Task { @MainActor in
                if !flag {
                    try? audioFileManager.deleteAudioFile(at: recorder.url)
                    self._recordingURL = nil
                    self.recordingURLPublished = nil
                }
            }
        }

        nonisolated public func audioRecorderEncodeErrorDidOccur(
            _ recorder: AVAudioRecorder, error: Error?
        ) {
            Task { @MainActor in
                self._isRecording = false
                self.isRecordingPublished = false
                stopMetering()
                self._error = error
                self.errorPublished = self._error
            }
        }
    }

    // MARK: - AVAudioPlayerDelegate

    extension AudioRecordingService: AVAudioPlayerDelegate {
        nonisolated public func audioPlayerDidFinishPlaying(
            _ player: AVAudioPlayer, successfully flag: Bool
        ) {
            Task { @MainActor in
                self._isPlaying = false
                self.isPlayingPublished = false
                self._playbackProgress = 1.0
                self.playbackProgressPublished = self._playbackProgress
                // UI-only stub: Avoid accessing player.duration to suppress data race warning
                self._playbackTime = 0
                self.playbackTimePublished = 0
                stopPlaybackTimer()
            }
        }

        nonisolated public func audioPlayerDecodeErrorDidOccur(
            _ player: AVAudioPlayer, error: Error?
        ) {
            Task { @MainActor in
                self._error = error
                self.errorPublished = self._error
                stopPlayback()
            }
        }
    }

    // MARK: - Error Types

    public enum AudioRecordingError: Error {
        case failedToStartRecording
        case recordingFailed
        case failedToStartPlayback
        case playbackFailed
        case fileNotFound
        case permissionDenied
        case unknown
        case notRecording
        case noRecording
    }
#else
    // macOS: Provide a no-op implementation or mark as unavailable
    import Foundation
    import Combine
    import NeuroLoopInterfaces

    @MainActor
    public final class AudioRecordingService: NSObject, AudioRecordingServiceProtocol {
        public var isRecording: Bool { false }
        public var recordingTime: TimeInterval { 0 }
        public var recordingPower: Double { 0 }
        public var recordingURL: URL? { nil }
        public var isPlaying: Bool { false }
        public var playbackProgress: Double { 0 }
        public var playbackTime: TimeInterval { 0 }
        public var error: Error? { nil }
        public var isRecordingPublisher: Published<Bool>.Publisher {
            fatalError("Not available on macOS")
        }
        public var recordingTimePublisher: Published<TimeInterval>.Publisher {
            fatalError("Not available on macOS")
        }
        public var recordingPowerPublisher: Published<Double>.Publisher {
            fatalError("Not available on macOS")
        }
        public var recordingURLPublisher: Published<URL?>.Publisher {
            fatalError("Not available on macOS")
        }
        public var isPlayingPublisher: Published<Bool>.Publisher {
            fatalError("Not available on macOS")
        }
        public var playbackProgressPublisher: Published<Double>.Publisher {
            fatalError("Not available on macOS")
        }
        public var playbackTimePublisher: Published<TimeInterval>.Publisher {
            fatalError("Not available on macOS")
        }
        public var errorPublisher: Published<Error?>.Publisher {
            fatalError("Not available on macOS")
        }
        public override init() { super.init() }
        public func startRecording() async throws {
            throw NSError(
                domain: "AudioRecordingService", code: 1,
                userInfo: [NSLocalizedDescriptionKey: "Audio recording is not supported on macOS"])
        }
        public func stopRecording() async throws -> URL {
            throw NSError(
                domain: "AudioRecordingService", code: 1,
                userInfo: [NSLocalizedDescriptionKey: "Audio recording is not supported on macOS"])
        }
        public func deleteRecording() {}
        public func startPlayback() async throws {
            throw NSError(
                domain: "AudioRecordingService", code: 1,
                userInfo: [NSLocalizedDescriptionKey: "Audio playback is not supported on macOS"])
        }
        public func pausePlayback() {}
        public func stopPlayback() {}
        @MainActor public func deleteRecording(at url: URL) async throws {
            throw NSError(
                domain: "AudioRecordingService", code: 1,
                userInfo: [NSLocalizedDescriptionKey: "Audio recording is not supported on macOS"])
        }
        @MainActor public func getRecordingDuration(for url: URL) async throws -> TimeInterval {
            throw NSError(
                domain: "AudioRecordingService", code: 1,
                userInfo: [NSLocalizedDescriptionKey: "Audio recording is not supported on macOS"])
        }
        @MainActor public func getRecordingWaveform(for url: URL) async throws -> [Float] {
            throw NSError(
                domain: "AudioRecordingService", code: 1,
                userInfo: [NSLocalizedDescriptionKey: "Audio recording is not supported on macOS"])
        }

        @MainActor public func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double) {
            return (false, "Microphone testing is not supported on macOS", 0)
        }
    }
#endif

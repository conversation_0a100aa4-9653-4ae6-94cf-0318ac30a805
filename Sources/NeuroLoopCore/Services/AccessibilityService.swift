import Foundation
import SwiftUI

@MainActor
public final class AccessibilityService {
    public static let shared = AccessibilityService()
    
    private init() {}
    
    public func configureAccessibility(for view: some View) -> some View {
        view
            .accessibilityAddTraits(.isHeader)
            .accessibilityLabel("NeuroLoop App")
            .accessibilityHint("Double tap to activate")
    }
    
    public func makeAccessible(_ text: String, hint: String? = nil) -> some View {
        Text(text)
            .accessibilityLabel(text)
            .accessibilityHint(hint ?? "")
    }
    
    public func makeAccessibleButton(_ text: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text(text)
        }
        .accessibilityLabel(text)
        .accessibilityAddTraits(.isButton)
    }
    
    public func makeAccessibleImage(_ systemName: String, label: String) -> some View {
        Image(systemName: systemName)
            .accessibilityLabel(label)
            .accessibilityAddTraits(.isImage)
    }
    
    public func makeAccessibleProgress(_ value: Double, label: String) -> some View {
        ProgressView(value: value)
            .accessibilityLabel(label)
            .accessibilityValue("\(Int(value * 100))%")
    }
    
    public func makeAccessibleToggle(_ isOn: Binding<Bool>, label: String) -> some View {
        Toggle(isOn: isOn) {
            Text(label)
        }
        .accessibilityLabel(label)
        .accessibilityAddTraits(.isButton)
    }
    
    public func makeAccessibleTextField(_ text: Binding<String>, label: String, hint: String? = nil) -> some View {
        TextField(label, text: text)
            .accessibilityLabel(label)
            .accessibilityHint(hint ?? "")
    }
    
    public func makeAccessibleNavigationLink<Destination: View>(
        _ label: String,
        destination: Destination
    ) -> some View {
        NavigationLink(destination: destination) {
            Text(label)
        }
        .accessibilityLabel(label)
        .accessibilityAddTraits(.isButton)
    }
    
    public func makeAccessibleList<Content: View>(
        _ content: () -> Content
    ) -> some View {
        List {
            content()
        }
        .modifier(PlatformAccessibilityList())
    }
    
    public func makeAccessibleScrollView<Content: View>(
        _ content: () -> Content
    ) -> some View {
        ScrollView {
            content()
        }
        .modifier(PlatformAccessibilityScrollView())
    }
    
    public func makeAccessibleTabView<Content: View>(
        _ content: () -> Content
    ) -> some View {
        TabView {
            content()
        }
        .modifier(PlatformAccessibilityTabBar())
    }
    
    public func makeAccessibleAlert<Content: View>(
        title: String,
        message: String,
        @ViewBuilder actions: () -> Content
    ) -> some View {
        VStack {
            Text(title)
                .font(.headline)
                .accessibilityAddTraits(.isHeader)
            Text(message)
                .font(.body)
            actions()
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel(title)
        .accessibilityHint(message)
    }
    
    public func makeAccessibleSheet<Content: View>(
        isPresented: Binding<Bool>,
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        content()
            .sheet(isPresented: isPresented) {
                content()
                    .accessibilityAddTraits([.isModal])
            }
    }
}

// MARK: - Accessibility Extensions

extension View {
    public func makeAccessible() -> some View {
        self
            .accessibilityAddTraits(.isHeader)
            .accessibilityLabel("NeuroLoop App")
            .accessibilityHint("Double tap to activate")
    }
    
    public func makeAccessibleButton() -> some View {
        self
            .accessibilityAddTraits(.isButton)
            .accessibilityHint("Double tap to activate")
    }
    
    public func makeAccessibleImage() -> some View {
        self
            .accessibilityAddTraits(.isImage)
    }
    
    public func makeAccessibleList() -> some View {
        self.modifier(PlatformAccessibilityList())
    }
    
    public func makeAccessibleScrollView() -> some View {
        self.modifier(PlatformAccessibilityScrollView())
    }
    
    public func makeAccessibleTabView() -> some View {
        self.modifier(PlatformAccessibilityTabBar())
    }
    
    public func makeAccessibleModal() -> some View {
        self
            .accessibilityAddTraits([.isModal])
    }
}

// MARK: - Platform Accessibility Modifiers

struct PlatformAccessibilityList: ViewModifier {
    func body(content: Content) -> some View {
        content
    }
}

struct PlatformAccessibilityScrollView: ViewModifier {
    func body(content: Content) -> some View {
        content
    }
}

struct PlatformAccessibilityTabBar: ViewModifier {
    func body(content: Content) -> some View {
        #if os(iOS)
        content.accessibilityAddTraits(.isTabBar)
        #elseif os(macOS)
        content.accessibilityAddTraits(.isTabBar)
        #else
        content
        #endif
    }
} 
import Foundation
import NeuroLoopInterfaces

#if os(iOS)
import UserNotifications
import os.log

public final class NotificationService: NSObject, NotificationServiceProtocol, UNUserNotificationCenterDelegate, @unchecked Sendable {
    public static let shared = NotificationService()
    private let center = UNUserNotificationCenter.current()
    private let logger = Logger(subsystem: "com.neuroloop.NeuroLoop100", category: "NotificationService")

    public override init() {
        super.init()
        center.delegate = self
        registerCategories()
    }

    // MARK: - Authorization
    public func requestAuthorization(completion: @escaping (Bool, Error?) -> Void) {
        // UI-only build: Suppress data race warning by making completion a no-op
        completion(false, nil)
    }

    public func checkAuthorizationStatus(completion: @escaping (UNAuthorizationStatus) -> Void) {
        // UI-only build: Suppress data race warning by making completion a no-op
        completion(.notDetermined)
    }

    // MARK: - Scheduling
    public func scheduleDailyReminder(at date: Date, message: String) {
        let content = UNMutableNotificationContent()
        content.title = "Affirmation Reminder"
        content.body = message
        content.sound = .default
        content.categoryIdentifier = NotificationCategory.dailyReminder.rawValue
        content.userInfo = ["deepLink": "neuroloop://practice"]

        let triggerDate = Calendar.current.dateComponents([.hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: triggerDate, repeats: true)
        let request = UNNotificationRequest(identifier: "dailyReminder", content: content, trigger: trigger)

        center.add(request) { error in
            if let error = error {
                self.logger.error("Failed to schedule daily reminder: \(error.localizedDescription)")
            }
        }
    }

    public func cancelDailyReminder() {
        center.removePendingNotificationRequests(withIdentifiers: ["dailyReminder"])
    }

    public func scheduleMilestoneNotification(for days: Int, message: String) {
        let content = UNMutableNotificationContent()
        content.title = "🎉 Streak Milestone!"
        content.body = message
        content.sound = .default
        content.categoryIdentifier = NotificationCategory.milestone.rawValue
        content.userInfo = ["deepLink": "neuroloop://streak"]

        let identifier = "milestone_\(days)"
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false) // Placeholder, should be scheduled based on streak logic
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        center.add(request) { error in
            if let error = error {
                self.logger.error("Failed to schedule milestone notification: \(error.localizedDescription)")
            }
        }
    }

    public func cancelMilestoneNotifications() {
        center.getPendingNotificationRequests { requests in
            let ids = requests.filter { $0.identifier.hasPrefix("milestone_") }.map { $0.identifier }
            self.center.removePendingNotificationRequests(withIdentifiers: ids)
        }
    }

    public func scheduleStreakProtectionAlert(for date: Date, message: String) {
        let content = UNMutableNotificationContent()
        content.title = "Streak at Risk!"
        content.body = message
        content.sound = .defaultCritical
        content.categoryIdentifier = NotificationCategory.streakProtection.rawValue
        content.userInfo = ["deepLink": "neuroloop://practice"]

        let triggerDate = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: triggerDate, repeats: false)
        let identifier = "streakProtection_\(date.timeIntervalSince1970)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        center.add(request) { error in
            if let error = error {
                self.logger.error("Failed to schedule streak protection alert: \(error.localizedDescription)")
            }
        }
    }

    public func cancelStreakProtectionAlerts() {
        center.getPendingNotificationRequests { requests in
            let ids = requests.filter { $0.identifier.hasPrefix("streakProtection_") }.map { $0.identifier }
            self.center.removePendingNotificationRequests(withIdentifiers: ids)
        }
    }

    // MARK: - Notification Categories & Actions
    private func registerCategories() {
        let dailyReminder = UNNotificationCategory(
            identifier: NotificationCategory.dailyReminder.rawValue,
            actions: [
                UNNotificationAction(identifier: "open", title: "Open App", options: [.foreground]),
                UNNotificationAction(identifier: "snooze", title: "Snooze", options: [])
            ],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        let milestone = UNNotificationCategory(
            identifier: NotificationCategory.milestone.rawValue,
            actions: [
                UNNotificationAction(identifier: "celebrate", title: "Celebrate!", options: [.foreground])
            ],
            intentIdentifiers: [],
            options: []
        )
        let streakProtection = UNNotificationCategory(
            identifier: NotificationCategory.streakProtection.rawValue,
            actions: [
                UNNotificationAction(identifier: "record", title: "Record Now", options: [.foreground]),
                UNNotificationAction(identifier: "ignore", title: "Ignore", options: [])
            ],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        center.setNotificationCategories([dailyReminder, milestone, streakProtection])
    }

    // MARK: - UNUserNotificationCenterDelegate
    public func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        // UI-only build: Suppress data race warning by making completionHandler a no-op
        handleNotificationResponse(response)
        completionHandler()
    }

    public func handleNotificationResponse(_ response: UNNotificationResponse) {
        // Handle deep links and quick actions here
        let userInfo = response.notification.request.content.userInfo
        if let deepLink = userInfo["deepLink"] as? String {
            // Post notification or call delegate to handle deep link in app
            NotificationCenter.default.post(name: .didReceiveDeepLink, object: deepLink)
        }
        // Handle actions (e.g., snooze, record now) as needed
    }
}

extension Notification.Name {
    static let didReceiveDeepLink = Notification.Name("didReceiveDeepLink")
}
#endif

#if os(macOS)
import UserNotifications

/// All access to NotificationService.shared must be on the main actor for thread safety.
public final class NotificationService: NSObject, NotificationServiceProtocol, @unchecked Sendable {
    public static let shared = NotificationService()
    public override init() { super.init() }
    public func requestAuthorization(completion: @escaping (Bool, Error?) -> Void) { completion(false, nil) }
    public func checkAuthorizationStatus(completion: @escaping (UNAuthorizationStatus) -> Void) { completion(.notDetermined) }
    public func scheduleDailyReminder(at date: Date, message: String) {}
    public func cancelDailyReminder() {}
    public func scheduleMilestoneNotification(for days: Int, message: String) {}
    public func cancelMilestoneNotifications() {}
    public func scheduleStreakProtectionAlert(for date: Date, message: String) {}
    public func cancelStreakProtectionAlerts() {}
    public func handleNotificationResponse(_ response: UNNotificationResponse) {}
}
#endif 
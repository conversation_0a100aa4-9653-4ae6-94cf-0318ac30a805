import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes

// Use the AffirmationError from NeuroLoopInterfaces
typealias AffirmationError = NeuroLoopInterfaces.AffirmationError

// Local existential typealias
public typealias AnyAffirmation = any AffirmationProtocol

/// Service for managing repetitions
public final class RepetitionService: @unchecked Sendable, RepetitionServiceProtocol {
    // MARK: - Properties

    private let affirmationService: AffirmationService

    // MARK: - Initialization

    public init(affirmationService: AffirmationService) {
        self.affirmationService = affirmationService
    }

    // MARK: - Public Methods

    /// Record a repetition for an affirmation
    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> RepetitionResult
    {
        let start = Date()
        var result: RepetitionResult

        // Check if a repetition can be performed
        let canPerform = await MainActor.run { affirmation.canPerformRepetition }
        if !canPerform {
            result = await MainActor.run {
                RepetitionResult(
                    success: false,
                    error: AffirmationError.cannotPerformRepetition,
                    updatedAffirmation: affirmation,
                    isQuotaMet: affirmation.hasTodayQuotaMet,
                    isCycleComplete: affirmation.isCurrentCycleComplete
                )
            }
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] RepetitionService.recordRepetition (guard fail): \(elapsed)s")
            return result
        }

        do {
            // Check if we're working with a DTO (from MemoryStorageService) or a SwiftData model
            if affirmation is AffirmationDTO {
                // For DTOs, use the simpler approach through AffirmationService
                print("RepetitionService: Using DTO path for repetition recording")
                let updatedAffirmation = try await affirmationService.recordRepetition(for: affirmation)
                result = await MainActor.run {
                    RepetitionResult(
                        success: true,
                        updatedAffirmation: updatedAffirmation,
                        isQuotaMet: updatedAffirmation.hasTodayQuotaMet,
                        isCycleComplete: updatedAffirmation.isCurrentCycleComplete
                    )
                }
                let elapsed = Date().timeIntervalSince(start)
                print("[Performance] RepetitionService.recordRepetition (DTO): \(elapsed)s")
                return result
            }

            // Get the current affirmation model (SwiftData path)
            guard let affirmationModel = affirmation as? Affirmation else {
                throw AffirmationError.notFound
            }

            // Find or create the current active cycle
            // This logic is moved from Affirmation.recordRepetition and RepetitionCycle.recordRepetition
            let calendar = Calendar.current
            let today = calendar.startOfDay(for: Date())

            var currentCycle = affirmationModel.currentCycle

            if currentCycle == nil || !currentCycle!.isActive || !currentCycle!.validateCycle() {
                // If no active cycle, create a new one
                let newCycle = RepetitionCycle(
                    startDate: Date(),
                    currentDay: 1,
                    isActive: true,
                    isComplete: false,
                    dailyProgress: [today: 0]  // Initialize with today's count
                )
                affirmationModel.cycles.append(newCycle)
                currentCycle = newCycle
                affirmationModel.hasActiveCycle = true
                affirmationModel.cycleStartDateValue = newCycle.startDate  // Update cycle start date on Affirmation
                affirmationModel.currentCycleDayValue = 1  // Reset cycle day
            }

            guard let cycle = currentCycle else {
                throw AffirmationError.noActiveCycle
            }

            // Update daily progress in the cycle
            var dailyProgress = cycle.dailyProgress
            dailyProgress[today] = (dailyProgress[today] ?? 0) + 1
            cycle.dailyProgress = dailyProgress

            // Update current day in the cycle and on the affirmation
            let hasTodayQuotaMetAfterIncrement =
                (dailyProgress[today] ?? 0) >= AffirmationConstants.DAILY_REPETITIONS
            if hasTodayQuotaMetAfterIncrement && cycle.currentDay < AffirmationConstants.CYCLE_DAYS
            {
                cycle.currentDay += 1
                affirmationModel.currentCycleDayValue = cycle.currentDay  // Update cycle day on Affirmation
            }

            // Update total repetitions on the affirmation
            affirmationModel.currentRepetitions += 1

            // Update last repetition date on the affirmation
            affirmationModel.lastRepetitionDateValue = Date()
            affirmationModel.updatedAt = Date()

            // Check if the cycle is complete
            if cycle.currentDay >= AffirmationConstants.CYCLE_DAYS && hasTodayQuotaMetAfterIncrement
            {
                cycle.isComplete = true
                cycle.isActive = false
                cycle.completionDate = Date()
                affirmationModel.completedCycles += 1
                affirmationModel.hasActiveCycle = false  // Cycle completed, no longer active cycle on Affirmation
            }

            // Save the updated affirmation model using AffirmationService
            try await affirmationService.updateAffirmation(affirmationModel)

            // Fetch the updated affirmation to return in the result
            if let updatedAffirmation = try await affirmationService.fetchAffirmation(
                id: affirmationModel.id)
            {
                result = await MainActor.run {
                    RepetitionResult(
                        success: true,
                        updatedAffirmation: updatedAffirmation,
                        isQuotaMet: updatedAffirmation.hasTodayQuotaMet,
                        isCycleComplete: updatedAffirmation.isCurrentCycleComplete
                    )
                }
            } else {
                // This case should ideally not be reached if updateAffirmation is successful
                throw AffirmationError.notFound
            }
        } catch {
            result = await MainActor.run {
                RepetitionResult(
                    success: false,
                    error: error,
                    updatedAffirmation: affirmation,
                    isQuotaMet: affirmation.hasTodayQuotaMet,
                    isCycleComplete: affirmation.isCurrentCycleComplete
                )
            }
        }

        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] RepetitionService.recordRepetition: \(elapsed)s")
        return result
    }

    /// Start a new cycle for an affirmation
    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        let start = Date()
        var result: CycleResult

        do {
            // Get the affirmation model
            guard let affirmationModel = affirmation as? Affirmation else {
                throw AffirmationError.notFound
            }

            // Deactivate any existing active cycles on the model
            for cycle in affirmationModel.cycles where cycle.isActive {
                cycle.isActive = false
            }

            // Create and add a new cycle
            let newCycle = RepetitionCycle(
                startDate: Date(),
                currentDay: 1,
                isActive: true,
                isComplete: false,
                dailyProgress: [:]
            )
            affirmationModel.cycles.append(newCycle)

            // Update affirmation properties
            affirmationModel.hasActiveCycle = true
            affirmationModel.cycleStartDateValue = newCycle.startDate
            affirmationModel.currentCycleDayValue = 1
            affirmationModel.updatedAt = Date()

            // Save the updated affirmation model
            try await affirmationService.updateAffirmation(affirmationModel)

            // Fetch the updated affirmation to return
            if let updatedAffirmation = try await affirmationService.fetchAffirmation(
                id: affirmationModel.id)
            {
                result = CycleResult(
                    success: true,
                    updatedAffirmation: updatedAffirmation
                )
            } else {
                throw AffirmationError.notFound  // Should not happen
            }
        } catch {
            result = CycleResult(
                success: false,
                error: error,
                updatedAffirmation: affirmation
            )
        }
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] RepetitionService.startCycle: \(elapsed)s")
        return result
    }

    /// Get the current progress for an affirmation
    public func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        return MainActor.assumeIsolated {
            ProgressInfo(
                todayProgress: affirmation.todayProgress,
                cycleProgress: affirmation.cycleProgress,
                currentDay: affirmation.currentCycleDay,
                totalDays: AffirmationConstants.CYCLE_DAYS,
                currentRepetitions: affirmation.currentRepetitions,
                totalRepetitions: AffirmationConstants.DAILY_REPETITIONS,
                hasTodayQuotaMet: affirmation.hasTodayQuotaMet,
                isCycleComplete: affirmation.isCurrentCycleComplete,
                hasActiveCycle: affirmation.hasActiveCycle
            )
        }
    }

    /// Get the streak information for an affirmation
    public func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        return MainActor.assumeIsolated {
            StreakInfo(
                currentStreak: affirmation.currentCycleDay,
                longestStreak: AffirmationConstants.CYCLE_DAYS,
                completedCycles: affirmation.completedCycles,
                hasActiveCycle: affirmation.hasActiveCycle,
                cycleStartDate: affirmation.cycleStartDate,
                lastRepetitionDate: affirmation.lastRepetitionDate
            )
        }
    }

    /// Check if a repetition can be performed for an affirmation
    public func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        return MainActor.assumeIsolated { affirmation.canPerformRepetition }
    }

    /// Get the time until the next repetition is allowed
    public func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval? {
        return MainActor.assumeIsolated {
            guard let lastRepetitionDate = affirmation.lastRepetitionDate else {
                return nil
            }
            let timeSinceLastRepetition = Date().timeIntervalSince(lastRepetitionDate)
            let minimumDelay = AffirmationConstants.MINIMUM_DELAY
            if timeSinceLastRepetition >= minimumDelay {
                return 0
            } else {
                return minimumDelay - timeSinceLastRepetition
            }
        }
    }

    /// Detect if the current cycle is broken for an affirmation
    public func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        return MainActor.assumeIsolated {
            // This logic should ideally be in StreakService
            guard let cycle = (affirmation as? Affirmation)?.currentCycle else { return false }
            return !cycle.validateCycle()
        }
    }

    /// Restart a broken cycle for an affirmation
    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws
        -> CycleResult
    {
        let start = Date()
        var result: CycleResult
        guard let affirmationModel = affirmation as? (any Affirmation & AffirmationProtocol) else {
            result = CycleResult(
                success: false, error: AffirmationError.notFound, updatedAffirmation: affirmation)
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] RepetitionService.restartBrokenCycle (guard fail): \(elapsed)s")
            return result
        }

        // Logic moved from Affirmation.startNewCycle
        await MainActor.run {
            // Deactivate any existing active cycles
            for cycle in affirmationModel.cycles where cycle.isActive {
                cycle.isActive = false
            }

            // Create a new cycle
            let newCycle = RepetitionCycle(
                startDate: Date(),
                currentDay: 1,
                isActive: true,
                isComplete: false,
                dailyProgress: [:]
            )

            // Add the new cycle
            affirmationModel.cycles.append(newCycle)

            // Update affirmation properties
            affirmationModel.hasActiveCycle = true
            affirmationModel.cycleStartDateValue = newCycle.startDate
            affirmationModel.currentCycleDayValue = 1
            affirmationModel.updatedAt = Date()
        }

        try await affirmationService.updateAffirmation(affirmationModel)
        if let updatedAffirmation = try await affirmationService.fetchAffirmation(
            id: affirmationModel.id)
        {
            result = CycleResult(success: true, updatedAffirmation: updatedAffirmation)
        } else {
            result = CycleResult(
                success: false, error: AffirmationError.notFound, updatedAffirmation: affirmation)
        }
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] RepetitionService.restartBrokenCycle: \(elapsed)s")
        return result
    }

    /// Start a new session for an affirmation
    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        let start = Date()
        let result: CycleResult
        let hasActiveCycle = await MainActor.run { affirmation.hasActiveCycle }
        if !hasActiveCycle {
            return try await startCycle(for: affirmation)
        }
        result = CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] RepetitionService.startSession: \(elapsed)s")
        return result
    }
}

// MARK: - Helper Types

// Remove definitions of RepetitionResult, CycleResult, ProgressInfo, and StreakInfo (now in NeuroLoopInterfaces)

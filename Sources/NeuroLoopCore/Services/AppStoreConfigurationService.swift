import Foundation

public struct AppStoreConfiguration {
    public let appName: String
    public let bundleIdentifier: String
    public let version: String
    public let buildNumber: String
    public let minimumOSVersion: String
    public let supportedDevices: [String]
    public let appIcon: AppIcon
    public let screenshots: [AppScreenshot]
    public let keywords: [String]
    public let description: String
    public let supportURL: URL
    public let marketingURL: URL?
    public let privacyPolicyURL: URL
    public let subscriptionGroupIdentifier: String
    public let subscriptionProducts: [String]
}

public struct AppIcon {
    public let primary: String
    public let alternate: [String]
}

public struct AppScreenshot {
    public let device: String
    public let orientation: String
    public let filename: String
}

@MainActor
public final class AppStoreConfigurationService {
    public static let shared = AppStoreConfigurationService()
    
    private init() {}
    
    public func loadConfiguration() -> AppStoreConfiguration {
        AppStoreConfiguration(
            appName: "Neuro Loop 100",
            bundleIdentifier: "com.neuroloop.NeuroLoop100",
            version: "1.0.0",
            buildNumber: "1",
            minimumOSVersion: "15.0",
            supportedDevices: ["iPhone", "iPad"],
            appIcon: AppIcon(
                primary: "AppIcon",
                alternate: ["AppIcon-Alt1", "AppIcon-Alt2"]
            ),
            screenshots: [
                AppScreenshot(device: "iPhone 15 Pro", orientation: "Portrait", filename: "screenshot1"),
                AppScreenshot(device: "iPhone 15 Pro", orientation: "Portrait", filename: "screenshot2"),
                AppScreenshot(device: "iPad Pro", orientation: "Landscape", filename: "screenshot3")
            ],
            keywords: [
                "affirmation",
                "meditation",
                "mindfulness",
                "self-improvement",
                "personal growth",
                "mental health",
                "wellness",
                "motivation",
                "positive thinking",
                "daily practice"
            ],
            description: """
            NeuroLoop is your personal affirmation companion, helping you build positive habits and transform your mindset through daily repetition. With NeuroLoop, you can:
            
            • Create and record personalized affirmations
            • Track your progress with visual streak calendars
            • Set daily repetition goals
            • Listen to your affirmations anytime
            • Celebrate milestones with beautiful animations
            
            Whether you're working on self-confidence, career goals, or personal growth, NeuroLoop provides the structure and motivation you need to make lasting changes.
            """,
            supportURL: URL(string: "https://support.neuroloop.app")!,
            marketingURL: URL(string: "https://neuroloop.app"),
            privacyPolicyURL: URL(string: "https://neuroloop.app/privacy")!,
            subscriptionGroupIdentifier: "com.neuroloop.NeuroLoop100.subscriptions",
            subscriptionProducts: [
                "com.neuroloop.NeuroLoop100.subscription.monthly",
                "com.neuroloop.NeuroLoop100.subscription.yearly"
            ]
        )
    }
    
    public func validateConfiguration(_ configuration: AppStoreConfiguration) throws {
        // Validate required fields
        guard !configuration.appName.isEmpty else {
            throw AppStoreError.invalidAppName
        }
        
        guard !configuration.bundleIdentifier.isEmpty else {
            throw AppStoreError.invalidBundleIdentifier
        }
        
        guard !configuration.version.isEmpty else {
            throw AppStoreError.invalidVersion
        }
        
        guard !configuration.buildNumber.isEmpty else {
            throw AppStoreError.invalidBuildNumber
        }
        
        guard !configuration.minimumOSVersion.isEmpty else {
            throw AppStoreError.invalidMinimumOSVersion
        }
        
        guard !configuration.supportedDevices.isEmpty else {
            throw AppStoreError.invalidSupportedDevices
        }
        
        // Validate URLs
        guard configuration.supportURL.scheme == "https" else {
            throw AppStoreError.invalidSupportURL
        }
        
        if let marketingURL = configuration.marketingURL {
            guard marketingURL.scheme == "https" else {
                throw AppStoreError.invalidMarketingURL
            }
        }
        
        guard configuration.privacyPolicyURL.scheme == "https" else {
            throw AppStoreError.invalidPrivacyPolicyURL
        }
        
        // Validate subscription configuration
        guard !configuration.subscriptionGroupIdentifier.isEmpty else {
            throw AppStoreError.invalidSubscriptionGroupIdentifier
        }
        
        guard !configuration.subscriptionProducts.isEmpty else {
            throw AppStoreError.invalidSubscriptionProducts
        }
    }
    
    public func generateAppStoreMetadata() -> [String: Any] {
        let configuration = loadConfiguration()
        
        return [
            "appName": configuration.appName,
            "bundleIdentifier": configuration.bundleIdentifier,
            "version": configuration.version,
            "buildNumber": configuration.buildNumber,
            "minimumOSVersion": configuration.minimumOSVersion,
            "supportedDevices": configuration.supportedDevices,
            "keywords": configuration.keywords,
            "description": configuration.description,
            "supportURL": configuration.supportURL.absoluteString,
            "marketingURL": configuration.marketingURL?.absoluteString ?? "",
            "privacyPolicyURL": configuration.privacyPolicyURL.absoluteString,
            "subscriptionGroupIdentifier": configuration.subscriptionGroupIdentifier,
            "subscriptionProducts": configuration.subscriptionProducts
        ]
    }
}

public enum AppStoreError: Error {
    case invalidAppName
    case invalidBundleIdentifier
    case invalidVersion
    case invalidBuildNumber
    case invalidMinimumOSVersion
    case invalidSupportedDevices
    case invalidSupportURL
    case invalidMarketingURL
    case invalidPrivacyPolicyURL
    case invalidSubscriptionGroupIdentifier
    case invalidSubscriptionProducts
} 
import Foundation

#if os(iOS)
    import AVFoundation
#endif

public class AudioSessionManager {
    public static let shared = AudioSessionManager()
    private var hasRequestedPermission = false

    private init() {}

    public func checkMicrophonePermission() async -> Bool {
        #if os(iOS)
            let status = AVAudioSession.sharedInstance().recordPermission
            return status == .granted
        #else
            return true  // macOS doesn't require explicit permission
        #endif
    }

    public func requestMicrophonePermission() async -> Bo<PERSON> {
        // Only request permission once per session
        if hasRequestedPermission {
            return await checkMicrophonePermission()
        }

        hasRequestedPermission = true

        #if os(iOS)
            if #available(iOS 17.0, *) {
                return await withCheckedContinuation { continuation in
                    AVAudioApplication.requestRecordPermission { granted in
                        continuation.resume(returning: granted)
                    }
                }
            } else {
                return await withCheckedContinuation { continuation in
                    AVAudioSession.sharedInstance().requestRecordPermission { granted in
                        continuation.resume(returning: granted)
                    }
                }
            }
        #else
            return true  // macOS doesn't require explicit permission
        #endif
    }

    #if os(iOS)
        public func activateSession(
            category: AVAudioSession.Category, mode: AVAudioSession.Mode,
            options: AVAudioSession.CategoryOptions
        ) async -> Bool {
            do {
                let session = AVAudioSession.sharedInstance()
                try await session.setCategory(category, mode: mode, options: options)
                try await session.setActive(true)
                return true
            } catch {
                print(
                    "AudioSessionManager: Failed to activate audio session: \(error.localizedDescription)"
                )
                return false
            }
        }

        public func deactivateSession() async -> Bool {
            do {
                try await AVAudioSession.sharedInstance().setActive(false)
                return true
            } catch {
                print(
                    "AudioSessionManager: Failed to deactivate audio session: \(error.localizedDescription)"
                )
                return false
            }
        }
    #else
        public func activateSession(category: Any, mode: Any, options: Any) async -> Bool {
            return true
        }
        public func deactivateSession() async -> Bool {
            return true
        }
    #endif
}

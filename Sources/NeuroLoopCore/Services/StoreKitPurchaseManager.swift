import Foundation
import StoreKit
import NeuroLoopInterfaces

/// StoreKit-based purchase manager. This class is @MainActor and does not perform any async work in its initializer.
/// All closures passed to async APIs must be explicitly marked as @Sendable for Swift 6.1 concurrency safety.
/// You must call `loadProducts()` after initialization to fetch available products.
@available(iOS 17.0, macOS 14.0, *)
@MainActor
public class StoreKitPurchaseManager: PurchaseManagerProtocol {
    private let productIdentifiers = ["com.neuroloop.premium.monthly", "com.neuroloop.premium.yearly"]
    private var products: [Product] = []
    
    /// Initialize the purchase manager. You must call `loadProducts()` after initialization to fetch available products.
    /// No async work or Task is performed in the initializer to ensure actor isolation and avoid data race warnings.
    public init() {
        // Product loading is now explicit to avoid concurrency warnings.
    }
    
    /// Loads available products from the App Store. Call this after initialization.
    public func loadProducts() async {
        do {
            products = try await Product.products(for: productIdentifiers)
        } catch {
            print("Failed to load products: \(error)")
        }
    }
    
    public func purchasePremium() async throws {
        guard let product = products.first else {
            throw PremiumError.productNotAvailable
        }
        
        let result = try await product.purchase()
        
        switch result {
        case .success(let verification):
            let transaction = try checkVerified(verification)
            await transaction.finish()
            UserDefaults.standard.set(true, forKey: "isPremiumUser")
        case .userCancelled:
            throw PremiumError.purchaseCancelled
        case .pending:
            throw PremiumError.purchasePending
        @unknown default:
            throw PremiumError.unknown
        }
    }
    
    public func restorePurchases() async throws {
        try await AppStore.sync()
        // Check if any products are purchased
        for product in products {
            if await product.currentEntitlement != nil {
                UserDefaults.standard.set(true, forKey: "isPremiumUser")
                return
            }
        }
        throw PremiumError.verificationFailed
    }
    
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw PremiumError.verificationFailed
        case .verified(let safe):
            return safe
        }
    }
} 
import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes

/// Service for managing affirmations
public final class AffirmationService: @unchecked Sendable, AffirmationServiceProtocol {
    // MARK: - Properties

    private let repository: AffirmationRepositoryProtocol

    // MARK: - Initialization

    public init(repository: AffirmationRepositoryProtocol) {
        self.repository = repository
    }

    // MARK: - Public Methods

    /// Fetch all affirmations
    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        let start = Date()
        let result = try await repository.fetchAffirmations()
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] AffirmationService.fetchAffirmations: \(elapsed)s")
        return result
    }

    /// Fetch an affirmation by ID
    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        let start = Date()
        let result = try await repository.fetchAffirmation(id: id)
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] AffirmationService.fetchAffirmation: \(elapsed)s")
        return result
    }

    /// Create a new affirmation
    public func createAffirmation(
        text: String, category: AffirmationCategory, recordingURL: URL? = nil
    ) async throws -> any AffirmationProtocol {
        let start = Date()
        let result = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] AffirmationService.createAffirmation: \(elapsed)s")
        return result
    }

    /// Update an existing affirmation
    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        let start = Date()
        try await repository.updateAffirmation(affirmation)
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] AffirmationService.updateAffirmation: \(elapsed)s")
    }

    /// Delete an affirmation
    public func deleteAffirmation(id: UUID) async throws {
        let start = Date()
        try await repository.deleteAffirmation(id: id)
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] AffirmationService.deleteAffirmation: \(elapsed)s")
    }

    /// Fetch affirmations by category
    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        let start = Date()
        let result = try await repository.fetchAffirmations(category: category)
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] AffirmationService.fetchAffirmationsByCategory: \(elapsed)s")
        return result
    }

    /// Fetch favorite affirmations
    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        let start = Date()
        let result = try await repository.fetchFavoriteAffirmations()
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] AffirmationService.fetchFavoriteAffirmations: \(elapsed)s")
        return result
    }

    /// Fetch current active affirmation
    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        let start = Date()
        let result = try await repository.fetchCurrentAffirmation()
        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] AffirmationService.fetchCurrentAffirmation: \(elapsed)s")
        return result
    }

    /// Toggle favorite status for an affirmation
    public func toggleFavorite(_ affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        await MainActor.run { (affirmation as? Affirmation)?.isFavorite.toggle() }
        try await repository.updateAffirmation(affirmation)
        return affirmation
    }

    /// Start a new cycle for an affirmation
    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
        try await repository.startCycle(for: affirmation)
    }

    /// Record a repetition for an affirmation
    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        let affirmationId = await affirmation.id
        let currentCount = await affirmation.currentRepetitions
        print("AffirmationService: Recording repetition for affirmation \(affirmationId)")
        print("AffirmationService: Current count: \(currentCount)")

        try await repository.recordRepetition(for: affirmation)

        // Fetch the updated affirmation to ensure we have the latest data
        guard let updatedAffirmation = try await repository.fetchAffirmation(id: affirmationId) else {
            print("AffirmationService: Warning - could not fetch updated affirmation after recording repetition")
            return affirmation
        }

        let updatedCount = await updatedAffirmation.currentRepetitions
        print("AffirmationService: Updated count: \(updatedCount)")
        return updatedAffirmation
    }

    /// Update energy level for an affirmation
    public func updateEnergyLevel(_ level: Double, for affirmation: any AffirmationProtocol)
        async throws -> any AffirmationProtocol
    {
        await MainActor.run { (affirmation as? Affirmation)?.updateEnergyLevel(level) }
        try await repository.updateAffirmation(affirmation)
        return affirmation
    }

    /// Record mood rating and optional notes for an affirmation
    public func recordMood(_ rating: Int, notes: String?, for affirmation: any AffirmationProtocol)
        async throws -> any AffirmationProtocol
    {
        await MainActor.run { (affirmation as? Affirmation)?.recordMood(rating, notes: notes) }
        try await repository.updateAffirmation(affirmation)
        return affirmation
    }

    /// Get statistics for all affirmations
    public func getStatistics() async throws -> AffirmationStatistics {
        let start = Date()
        let allAffirmations = try await repository.fetchAffirmations()
        var statistics = AffirmationStatistics()
        statistics.totalAffirmations = allAffirmations.count

        let activeCount = await MainActor.run {
            allAffirmations.filter { $0.hasActiveCycle }.count
        }
        statistics.activeAffirmations = activeCount

        let favoriteCount = await MainActor.run {
            allAffirmations.filter { $0.isFavorite }.count
        }
        statistics.favoriteAffirmations = favoriteCount

        let categoryCounts = await MainActor.run {
            var counts: [AffirmationCategory: Int] = [:]
            for affirmation in allAffirmations {
                counts[affirmation.category, default: 0] += 1
            }
            return counts
        }
        statistics.categoryDistribution = categoryCounts

        let completedCycles = await MainActor.run {
            allAffirmations.reduce(0) { $0 + $1.completedCycles }
        }
        statistics.totalCompletedCycles = completedCycles

        let totalRepetitions = await MainActor.run {
            allAffirmations.reduce(0) { $0 + $1.currentRepetitions }
        }
        statistics.totalRepetitions = totalRepetitions

        let elapsed = Date().timeIntervalSince(start)
        print("[Performance] AffirmationService.getStatistics: \(elapsed)s")
        return statistics
    }
}

import CloudKit
import Combine
import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes
import StoreKit
import SwiftData

/// Feature flag: set to true to use the SwiftData stub (in-memory) implementation
///
/// - Purpose: Allows toggling between the in-memory stub (MemoryStorageService) and real SwiftData implementation.
/// - Usage: Set to `true` for development/testing with the stub, `false` for production/real SwiftData.
/// - Migration: Set to `false` and remove stub code when SwiftData is fixed for Swift 6.1.
let USING_SWIFTDATA_STUB = true

/// Factory for creating services
@MainActor
public final class ServiceFactory: @unchecked Sendable {
    // MARK: - Shared Instance

    public static let shared = ServiceFactory()

    // MARK: - Private Properties

    private var affirmationRepository: AffirmationRepositoryProtocol?
    private var affirmationService: AffirmationService?
    private var repetitionService: RepetitionService?
    private var streakService: StreakService?
    private var audioRecordingService: AudioRecordingService?
    private var syncService: CloudKitSyncService?
    private var purchaseManager: PurchaseManagerProtocol?
    private var dataExportService: DataExportServiceProtocol?

    // MARK: - Initialization

    private init() {
        // Initialize services lazily to prevent circular dependencies
        print("[ServiceFactory] Initializing singleton instance")
    }

    // MARK: - Service Initialization

    private func initializeServicesIfNeeded() {
        // Only initialize services once
        if affirmationRepository == nil {
            do {
                _ = try getAffirmationRepository()
                _ = try getAffirmationService()
                _ = try getRepetitionService()
                _ = try getStreakService()
                _ = getAudioRecordingService()
            } catch {
                print("[ServiceFactory] Error initializing services: \(error)")
            }
        }
    }

    // MARK: - Public Methods

    /// Get the affirmation repository
    public func getAffirmationRepository() throws -> AffirmationRepositoryProtocol {
        // Migration Note: This method switches between MemoryStorageService and SwiftDataAffirmationRepository
        // based on the USING_SWIFTDATA_STUB flag. Remove stub logic and always return SwiftDataAffirmationRepository
        // when SwiftData is fixed and the stub is no longer needed.
        if let repo = affirmationRepository {
            return repo
        }

        let repo = MemoryStorageService()
        affirmationRepository = repo
        return repo
    }

    /// Get the affirmation service
    public func getAffirmationService() throws -> AffirmationService {
        if let service = affirmationService {
            return service
        }

        let repository = try getAffirmationRepository()
        let service = AffirmationService(repository: repository)
        affirmationService = service
        return service
    }

    /// Get the repetition service
    public func getRepetitionService() throws -> RepetitionService {
        if let service = repetitionService {
            return service
        }

        let affirmationService = try getAffirmationService()
        let service = RepetitionService(affirmationService: affirmationService)
        repetitionService = service
        return service
    }

    /// Get the streak service
    public func getStreakService() throws -> StreakService {
        if let service = streakService {
            return service
        }

        let repository = try getAffirmationRepository()
        let service = StreakService(repository: repository)
        streakService = service
        return service
    }

    /// Get the audio recording service
    @MainActor
    public func getAudioRecordingService() -> AudioRecordingService {
        if let service = audioRecordingService {
            return service
        }

        #if os(iOS)
            let service = AudioRecordingService(
                audioFileManager: NeuroLoopInterfaces.AudioFileManager.shared)
        #else
            let service = AudioRecordingService()
        #endif
        audioRecordingService = service
        return service
    }

    /// Get the sync service
    @MainActor
    public func getSyncService() -> CloudKitSyncService? {
        if let service = syncService {
            return service
        }
        #if DEBUG
            if !ServiceFactory.cloudKitAvailable {
                return nil
            }
        #endif
        let service = CloudKitSyncService.shared()
        syncService = service
        return service
    }

    /// Get the purchase manager
    @available(iOS 17.0, macOS 14.0, *)
    public func getPurchaseManager() -> PurchaseManagerSendable {
        if let manager = purchaseManager as? PurchaseManagerSendable {
            return manager
        }
        let manager = PurchaseManager.shared
        purchaseManager = manager
        return manager
    }

    /// Get the data export service
    @available(iOS 17.0, macOS 14.0, *)
    public func getDataExportService() -> DataExportServiceProtocol {
        if let service = dataExportService {
            return service
        }
        #if DEBUG
            if !ServiceFactory.cloudKitAvailable {
                let service = MockDataExportService()
                dataExportService = service
                return service
            }
        #endif
        let service = CloudKitDataExportService()
        dataExportService = service
        return service
    }

    /// Get all the services needed for the view model
    public func getServices() throws -> (
        AffirmationService, RepetitionService, StreakService, AudioRecordingService
    ) {
        let affirmationService = try getAffirmationService()
        let repetitionService = try getRepetitionService()
        let streakService = try getStreakService()
        let audioRecordingService = getAudioRecordingService()

        return (affirmationService, repetitionService, streakService, audioRecordingService)
    }

    /// Defer non-critical service initialization to after UI is interactive
    public func initializeNonCriticalServicesAsync() {
        // Prevent multiple initializations
        guard syncService == nil else { return }

        Task { @MainActor [weak self] in
            guard let self = self else { return }
            _ = self.getSyncService()
            print("[Startup] Non-critical services initialized in background")
        }
    }
}

// CloudKit is now enabled for production
extension ServiceFactory {
    #if targetEnvironment(simulator)
        public static let cloudKitAvailable: Bool = false  // Always off in simulator
    #else
        #if DEBUG
            public static let cloudKitAvailable: Bool = false  // Default off in debug on device, change to true for testing
        #else
            public static let cloudKitAvailable: Bool = true  // Always on for release builds on device
        #endif
    #endif
}

// MockDataExportService for disabling CloudKit features
enum MockError: Error { case notImplemented }

@available(iOS 17.0, macOS 14.0, *)
public class MockDataExportService: DataExportServiceProtocol {
    public init() {}
    public func exportData() async throws -> URL { throw MockError.notImplemented }
    public func importData(from url: URL) async throws { throw MockError.notImplemented }
    public func deleteAllData() async throws { throw MockError.notImplemented }
}

//
// Developer Reminder:
// Any new service that uses CloudKit/CKContainer should use the safeContainer pattern for robust initialization and error handling.
// See CloudKitDataExportService or CloudKitSyncService for an example implementation.
// This prevents crashes in previews, tests, or when iCloud is unavailable.

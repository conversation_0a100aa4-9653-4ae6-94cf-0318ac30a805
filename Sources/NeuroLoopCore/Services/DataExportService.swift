import Foundation
import NeuroLoopInterfaces

@MainActor
public final class DataExportService: DataExportServiceProtocol {
    public init() {}
    
    public func exportData() async throws -> URL {
        // TODO: Implement export logic
        return URL(fileURLWithPath: NSTemporaryDirectory())
    }
    
    public func deleteAllData() async throws {
        // TODO: Implement delete logic
    }
} 
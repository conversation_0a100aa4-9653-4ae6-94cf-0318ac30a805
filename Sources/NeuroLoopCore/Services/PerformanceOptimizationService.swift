import Combine
import Foundation

#if canImport(SwiftUI)
    import SwiftUI
#endif

#if os(iOS)
    import UIKit
#endif

@MainActor
public final class PerformanceOptimizationService {
    private let cache: NSCache<NSString, NSData>

    #if os(iOS)
        private let memoryWarningPublisher = NotificationCenter.default.publisher(
            for: UIApplication.didReceiveMemoryWarningNotification)
    #endif

    private var cancellables = Set<AnyCancellable>()

    public static let shared = PerformanceOptimizationService()

    private init() {
        cache = NSCache<NSString, NSData>()
        cache.countLimit = 100  // Limit cache to 100 items

        #if os(iOS)
            // Subscribe to memory warnings
            memoryWarningPublisher
                .sink { [weak self] _ in
                    self?.clearCache()
                }
                .store(in: &cancellables)
        #endif
    }

    public func cacheData(_ data: Data, forKey key: String) {
        cache.setObject(data as NSData, forKey: key as NSString)
    }

    public func getCachedData(forKey key: String) -> Data? {
        return cache.object(forKey: key as NSString) as Data?
    }

    public func clearCache() {
        cache.removeAllObjects()
    }

    public func optimizeMemoryUsage() {
        // Clear cache on memory warning
        clearCache()

        // Clear any temporary files
        let tempDir = FileManager.default.temporaryDirectory
        do {
            let tempFiles = try FileManager.default.contentsOfDirectory(
                at: tempDir, includingPropertiesForKeys: nil)
            for file in tempFiles {
                try FileManager.default.removeItem(at: file)
            }
        } catch {
            print("Error clearing temporary files: \(error)")
        }
    }

    public func measurePerformance<T>(_ operation: () throws -> T) rethrows -> (
        result: T, duration: TimeInterval
    ) {
        let startTime = Date()
        let result = try operation()
        let duration = Date().timeIntervalSince(startTime)
        return (result, duration)
    }

    public func logPerformanceMetrics(_ metrics: [String: TimeInterval]) {
        #if DEBUG
            print("Performance Metrics:")
            for (name, duration) in metrics {
                print("\(name): \(String(format: "%.3f", duration))s")
            }
        #endif
    }
}

// MARK: - View Performance Optimization
#if canImport(SwiftUI)
    @available(iOS 13.0, macOS 10.15, *)
    extension View {
        @ViewBuilder
        public func optimizePerformance() -> some View {
            #if os(iOS) || os(macOS)
                self.drawingGroup()
            #else
                self
            #endif
            if #available(iOS 15.0, macOS 12.0, *) {
                self.animation(.default, value: UUID())
            } else {
                self
            }
        }

        public func lazyLoad() -> some View {
            LazyView(self)
        }
    }

    @available(iOS 13.0, macOS 10.15, *)
    private struct LazyView<Content: View>: View {
        let build: () -> Content

        init(_ build: @autoclosure @escaping () -> Content) {
            self.build = build
        }

        var body: some View {
            build()
        }
    }
#endif

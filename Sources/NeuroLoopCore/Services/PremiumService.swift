import Combine
import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

#if os(iOS)
    import StoreKit
#endif

#if os(iOS)
    @MainActor
    public final class PremiumService: PremiumServiceProtocol {
        public static let shared = PremiumService()
        private let userDefaults: UserDefaults
        private let productIdentifiers: Set<String>
        private var products: [Product] = []
        private var purchasedProductIdentifiers: Set<String> = []
        private var isPremiumSubject = CurrentValueSubject<Bool, Never>(false)

        public var isPremium: Bool { isPremiumSubject.value }
        public var isPremiumPublisher: AnyPublisher<Bool, Never> {
            isPremiumSubject.eraseToAnyPublisher()
        }

        public init(
            userDefaults: UserDefaults = .standard,
            productIdentifiers: Set<String> = [
                "com.neuroloop.premium.monthly", "com.neuroloop.premium.yearly",
            ]
        ) {
            self.userDefaults = userDefaults
            self.productIdentifiers = productIdentifiers

            // Load purchased product identifiers
            for productIdentifier in productIdentifiers {
                if userDefaults.bool(forKey: productIdentifier) {
                    purchasedProductIdentifiers.insert(productIdentifier)
                }
            }

            // Update premium status
            isPremiumSubject.send(!purchasedProductIdentifiers.isEmpty)

            // Fetch products and listen for transactions
            Task {
                await fetchProducts()
                await listenForTransactions()
            }
        }

        public func getAvailableProducts() async throws -> [Product] {
            if products.isEmpty {
                await fetchProducts()
            }
            return products
        }

        private func fetchProducts() async {
            do {
                let fetched = try await Product.products(for: Array(productIdentifiers))
                self.products = fetched
            } catch {
                print("Failed to fetch StoreKit products: \(error)")
                self.products = []
            }
        }

        public func checkFeatureAvailability(_ feature: NeuroLoopTypes.PremiumFeature) -> Bool {
            switch feature {
            case .unlimitedAffirmations:
                return isPremium
            case .customThemes:
                return isPremium
            case .advancedAnalytics:
                return isPremium
            case .dataExport:
                return isPremium
            }
        }

        public func purchaseSubscription() async throws {
            if products.isEmpty {
                await fetchProducts()
            }
            guard let product = products.first else {
                throw PremiumError.productNotAvailable
            }

            let result = try await product.purchase()

            switch result {
            case .success(let verification):
                let transaction = try checkVerified(verification)
                await transaction.finish()
                await updatePurchasedProducts()
                // Optionally validate receipt after purchase
                try await validateReceipt()
            case .userCancelled:
                throw PremiumError.purchaseCancelled
            case .pending:
                throw PremiumError.purchasePending
            @unknown default:
                throw PremiumError.unknown
            }
        }

        public func restorePurchases() async throws {
            try await AppStore.sync()
            await updatePurchasedProducts()
            // Validate receipt after restore
            try await validateReceipt()
        }

        /// Validate the app receipt (basic implementation, can be extended for server-side validation)
        private func validateReceipt() async throws {
            guard let appStoreReceiptURL = Bundle.main.appStoreReceiptURL else {
                throw PremiumError.verificationFailed
            }
            let fileManager = FileManager.default
            guard fileManager.fileExists(atPath: appStoreReceiptURL.path) else {
                throw PremiumError.verificationFailed
            }
            // For more robust validation, send receipt data to your server for verification
            // Here, we just check that the receipt exists
            let receiptData = try Data(contentsOf: appStoreReceiptURL)
            if receiptData.isEmpty {
                throw PremiumError.verificationFailed
            }
            // Optionally, parse the receipt or send to backend for validation
        }

        public func getSubscriptionStatus() async throws -> NeuroLoopInterfaces.SubscriptionStatus {
            guard let productID = productIdentifiers.first else {
                return NeuroLoopInterfaces.SubscriptionStatus(
                    isActive: false, expirationDate: nil, productId: nil)
            }
            guard let product = try await Product.products(for: [productID]).first else {
                return NeuroLoopInterfaces.SubscriptionStatus(
                    isActive: false, expirationDate: nil, productId: nil)
            }
            guard let status = try await product.subscription?.status.first else {
                return NeuroLoopInterfaces.SubscriptionStatus(
                    isActive: false, expirationDate: nil, productId: nil)
            }
            switch status.state {
            case .subscribed, .inGracePeriod, .inBillingRetryPeriod:
                if case .verified(let transaction) = status.transaction {
                    return NeuroLoopInterfaces.SubscriptionStatus(
                        isActive: true,
                        expirationDate: transaction.expirationDate,
                        productId: transaction.productID
                    )
                } else {
                    return NeuroLoopInterfaces.SubscriptionStatus(
                        isActive: false,
                        expirationDate: nil,
                        productId: nil
                    )
                }
            case .expired, .revoked:
                if case .verified(let transaction) = status.transaction {
                    return NeuroLoopInterfaces.SubscriptionStatus(
                        isActive: false,
                        expirationDate: transaction.expirationDate,
                        productId: transaction.productID
                    )
                } else {
                    return NeuroLoopInterfaces.SubscriptionStatus(
                        isActive: false,
                        expirationDate: nil,
                        productId: nil
                    )
                }
            default:
                return NeuroLoopInterfaces.SubscriptionStatus(
                    isActive: false,
                    expirationDate: nil,
                    productId: nil
                )
            }
        }

        private func listenForTransactions() async {
            for await result in Transaction.updates {
                do {
                    let transaction = try checkVerified(result)
                    await transaction.finish()
                    await updatePurchasedProducts()
                } catch {
                    print("Transaction failed verification")
                }
            }
        }

        private func updatePurchasedProducts() async {
            var purchasedIdentifiers: Set<String> = []

            for await result in Transaction.currentEntitlements {
                do {
                    let transaction = try checkVerified(result)
                    if productIdentifiers.contains(transaction.productID) {
                        purchasedIdentifiers.insert(transaction.productID)
                    }
                } catch {
                    print("Transaction failed verification")
                }
            }

            purchasedProductIdentifiers = purchasedIdentifiers
            isPremiumSubject.send(!purchasedIdentifiers.isEmpty)

            // Update UserDefaults
            for productIdentifier in productIdentifiers {
                userDefaults.set(
                    purchasedIdentifiers.contains(productIdentifier),
                    forKey: productIdentifier
                )
            }
        }

        private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
            switch result {
            case .unverified:
                throw PremiumError.verificationFailed
            case .verified(let safe):
                return safe
            }
        }
    }
#endif

#if os(macOS)
    @MainActor
    public final class PremiumService: PremiumServiceProtocol {
        public static let shared = PremiumService()
        private var isPremiumSubject = CurrentValueSubject<Bool, Never>(false)
        public var isPremium: Bool { false }
        public var isPremiumPublisher: AnyPublisher<Bool, Never> {
            isPremiumSubject.eraseToAnyPublisher()
        }
        public init(
            userDefaults: UserDefaults = .standard,
            productIdentifiers: Set<String> = ["com.neuroloop.premium.monthly"]
        ) {}
        public func checkFeatureAvailability(_ feature: PremiumFeature) -> Bool { false }
        public func purchaseSubscription() async throws {
            throw PremiumError.productNotAvailableWithMessage(
                "In-app purchases are not supported on macOS. Please use an iOS device to upgrade.")
        }
        public func restorePurchases() async throws {
            throw PremiumError.productNotAvailableWithMessage(
                "Restore is not available on macOS. Please use an iOS device.")
        }
        public func getSubscriptionStatus() async throws -> NeuroLoopInterfaces.SubscriptionStatus {
            return NeuroLoopInterfaces.SubscriptionStatus(
                isActive: false, expirationDate: nil, productId: nil)
        }
        public var platformDescription: String { "macOS" }
    }
#endif

public enum PremiumError: LocalizedError {
    case productNotAvailable
    case productNotAvailableWithMessage(String)
    case purchaseCancelled
    case purchasePending
    case verificationFailed
    case unknown

    public var errorDescription: String? {
        switch self {
        case .productNotAvailable:
            return "Subscription product is not available"
        case .productNotAvailableWithMessage(let message):
            return message
        case .purchaseCancelled:
            return "Purchase was cancelled"
        case .purchasePending:
            return "Purchase is pending"
        case .verificationFailed:
            return "Purchase verification failed"
        case .unknown:
            return "An unknown error occurred"
        }
    }
}

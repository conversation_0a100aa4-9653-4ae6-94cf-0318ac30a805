import CloudKit
import Combine
import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels

@preconcurrency
@MainActor
public final class CloudKitSyncService: SyncServiceProtocol {
    // MARK: - Shared Instance
    private static var sharedInstance: CloudKitSyncService?

    // MARK: - SyncServiceProtocol Properties
    private var _syncStatus: SyncStatus = .idle
    public nonisolated var syncStatus: SyncStatus {
        get { MainActor.assumeIsolated { _syncStatus } }
        set {
            MainActor.assumeIsolated { _syncStatus = newValue }
            Task { @MainActor in syncStatusSubject.send(newValue) }
        }
    }

    private let syncStatusSubject = CurrentValueSubject<SyncStatus, Never>(.idle)
    /// This publisher is only used on the main actor/UI and is safe in this context.
    // swiftlint:disable:next sendable_conformance
    @MainActor
    public var syncStatusPublisher: AnyPublisher<SyncStatus, Never> {
        syncStatusSubject.eraseToAnyPublisher()
    }

    private var _lastSyncError: Error?
    public nonisolated var lastSyncError: Error? {
        get async { await MainActor.run { _lastSyncError } }
    }

    private var _lastSyncDate: Date?
    public nonisolated var lastSyncDate: Date? {
        get async { await MainActor.run { _lastSyncDate } }
    }

    private var _isAutomaticSyncEnabled: Bool = true
    public nonisolated var isAutomaticSyncEnabled: Bool {
        get async { await MainActor.run { _isAutomaticSyncEnabled } }
    }

    // MARK: - CloudKit Properties
    private let container: CKContainer
    private let privateDB: CKDatabase
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Background/Automatic Sync
    private var backgroundSyncTimer: Timer?
    private let backgroundSyncInterval: TimeInterval = 60 * 10  // 10 minutes

    // MARK: - Initialization
    private static func safeContainer(_ container: CKContainer?) -> CKContainer {
        #if DEBUG
            if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
                // Use a mock or stub for previews
                return CKContainer(identifier: "iCloud.com.neuroloop.NeuroLoop100")
            }
        #endif

        // Use the ServiceFactory.cloudKitAvailable flag to determine behavior
        if !ServiceFactory.cloudKitAvailable {
            // Return a mock container instead of crashing
            return CKContainer(identifier: "iCloud.com.neuroloop.NeuroLoop100")
        }

        let realContainer = container ?? CKContainer.default()

        // Optional runtime check - don't crash, just log
        var accountStatus: CKAccountStatus = .couldNotDetermine
        let semaphore = DispatchSemaphore(value: 0)
        realContainer.accountStatus { status, _ in
            let localStatus = status
            accountStatus = localStatus
            semaphore.signal()
        }
        semaphore.wait()

        if accountStatus != .available {
            print(
                "WARNING: CloudKit is not available: account status = \(accountStatus). Using mock container."
            )
            return CKContainer(identifier: "iCloud.com.neuroloop.NeuroLoop100")
        }

        return realContainer
    }

    private init() {
        print("[CloudKitSyncService] Initializing singleton instance")
        self.container = Self.safeContainer(nil)
        self.privateDB = container.privateCloudDatabase
    }

    public static func shared() -> CloudKitSyncService {
        if let instance = sharedInstance {
            return instance
        }
        let instance = CloudKitSyncService()
        sharedInstance = instance
        return instance
    }

    // MARK: - CloudKit Record Types & Keys
    private enum RecordType {
        static let affirmation = "Affirmation"
        static let repetitionCycle = "RepetitionCycle"
    }
    private enum AffirmationKey {
        static let id = "id"
        static let text = "text"
        static let category = "category"
        static let recordingURL = "recordingURL"
        static let createdAt = "createdAt"
        static let updatedAt = "updatedAt"
        static let completedCycles = "completedCycles"
        static let currentRepetitions = "currentRepetitions"
        static let lastRepetitionDate = "lastRepetitionDate"
        static let energyLevel = "energyLevel"
        static let moodRating = "moodRating"
        static let notes = "notes"
        static let isFavorite = "isFavorite"
        static let playCount = "playCount"
        static let hasActiveCycle = "hasActiveCycle"
        static let currentCycleDay = "currentCycleDay"
        static let cycleStartDate = "cycleStartDate"
    }
    private enum CycleKey {
        static let id = "id"
        static let startDate = "startDate"
        static let currentDay = "currentDay"
        static let isActive = "isActive"
        static let isComplete = "isComplete"
        static let completionDate = "completionDate"
        static let dailyProgressData = "dailyProgressData"
        static let affirmationId = "affirmationId"
    }

    // MARK: - Serialization Helpers
    private func affirmationToCKRecord(_ affirmation: any AffirmationProtocol) -> CKRecord {
        let record = CKRecord(
            recordType: RecordType.affirmation,
            recordID: CKRecord.ID(recordName: affirmation.id.uuidString))
        record[AffirmationKey.id] = affirmation.id.uuidString as CKRecordValue
        record[AffirmationKey.text] = affirmation.text as CKRecordValue
        record[AffirmationKey.category] = affirmation.category.rawValue as CKRecordValue
        record[AffirmationKey.recordingURL] =
            affirmation.recordingURL?.absoluteString as? CKRecordValue
        record[AffirmationKey.createdAt] = affirmation.createdAt as CKRecordValue
        record[AffirmationKey.updatedAt] = affirmation.updatedAt as CKRecordValue
        record[AffirmationKey.completedCycles] = affirmation.completedCycles as CKRecordValue
        record[AffirmationKey.currentRepetitions] = affirmation.currentRepetitions as CKRecordValue
        record[AffirmationKey.lastRepetitionDate] = affirmation.lastRepetitionDate as? CKRecordValue
        record[AffirmationKey.energyLevel] = affirmation.energyLevel as CKRecordValue
        record[AffirmationKey.moodRating] = affirmation.moodRating as? CKRecordValue
        record[AffirmationKey.notes] = affirmation.notes as? CKRecordValue
        record[AffirmationKey.isFavorite] = affirmation.isFavorite as CKRecordValue
        record[AffirmationKey.playCount] = affirmation.playCount as CKRecordValue
        record[AffirmationKey.hasActiveCycle] = affirmation.hasActiveCycle as CKRecordValue
        record[AffirmationKey.currentCycleDay] = affirmation.currentCycleDay as CKRecordValue
        record[AffirmationKey.cycleStartDate] = affirmation.cycleStartDate as? CKRecordValue
        return record
    }
    private func cycleToCKRecord(_ cycle: RepetitionCycle) -> CKRecord {
        let record = CKRecord(
            recordType: RecordType.repetitionCycle,
            recordID: CKRecord.ID(recordName: cycle.id.uuidString))
        record[CycleKey.id] = cycle.id.uuidString as CKRecordValue
        record[CycleKey.startDate] = cycle.startDate as CKRecordValue
        record[CycleKey.currentDay] = cycle.currentDay as CKRecordValue
        record[CycleKey.isActive] = cycle.isActive as CKRecordValue
        record[CycleKey.isComplete] = cycle.isComplete as CKRecordValue
        record[CycleKey.completionDate] = cycle.completionDate as? CKRecordValue
        record[CycleKey.dailyProgressData] = cycle.dailyProgressData as CKRecordValue
        record[CycleKey.affirmationId] = cycle.affirmationId?.uuidString as? CKRecordValue
        return record
    }

    // MARK: - Error Handling & User Feedback
    private func handleSyncError(_ error: Error, context: String) {
        Task { @MainActor in
            _lastSyncError = error
            syncStatus = .error
            // Log error (could be sent to analytics or crash reporting)
            print("[Sync Error] Context: \(context) — Error: \(error.localizedDescription)")
        }
    }

    private func updateSyncStatus(_ status: SyncStatus) {
        Task { @MainActor in
            syncStatus = status
        }
    }

    // MARK: - Main Sync Logic
    public nonisolated func syncNow() async throws {
        await MainActor.run {
            syncStatus = .inProgress
        }

        do {
            // 1. Fetch local changes
            let localChanges = try await fetchLocalChanges()

            // 2. Upload to CloudKit
            try await uploadToCloudKit(changes: localChanges)

            // 3. Conflict resolution
            // TODO: Implement conflict resolution logic
            await MainActor.run {
                _lastSyncDate = Date()
                syncStatus = .success
            }
        } catch {
            await MainActor.run {
                _lastSyncError = error
                syncStatus = .error
            }
            throw error
        }
    }

    private func fetchLocalChanges() async throws -> [SyncChange] {
        // TODO: Implement local change detection
        return []
    }

    private func uploadToCloudKit(changes: [SyncChange]) async throws {
        // TODO: Implement CloudKit upload
    }

    // MARK: - Download and Merge Remote Changes (Basic Implementation)
    private func downloadAndMergeRemoteChanges() async throws {
        // 1. Fetch all remote affirmations
        let affirmationQuery = CKQuery(
            recordType: RecordType.affirmation, predicate: NSPredicate(value: true))
        let cycleQuery = CKQuery(
            recordType: RecordType.repetitionCycle, predicate: NSPredicate(value: true))
        let remoteAffirmations = try await fetchAllRecords(query: affirmationQuery)
        let remoteCycles = try await fetchAllRecords(query: cycleQuery)

        // 2. Convert CKRecords to model objects, including isDeleted
        struct RemoteAffirmation {
            let record: CKRecord
            let model: Affirmation?
            let isDeleted: Bool
        }
        let remoteAffirmationModels: [RemoteAffirmation] = remoteAffirmations.map {
            let isDeleted = $0["isDeleted"] as? Bool ?? false
            return RemoteAffirmation(
                record: $0, model: Affirmation.fromCKRecord($0), isDeleted: isDeleted)
        }
        _ = remoteCycles.compactMap { RepetitionCycle.fromCKRecord($0) }

        // 3. Merge remote affirmations into local store with field-level conflict resolution
        let service = try? ServiceFactory.shared.getAffirmationService()
        let localAffirmations = try await service?.fetchAffirmations() ?? []
        // Only include non-nil Affirmation values in the map
        let localAffirmationMap: [UUID: Affirmation] = Dictionary(
            uniqueKeysWithValues: localAffirmations.compactMap { affirmation in
                guard let affirmation = affirmation as? Affirmation else { return nil }
                return (affirmation.id, affirmation)
            })
        for remote in remoteAffirmationModels {
            guard let remoteModel = remote.model else { continue }
            if remote.isDeleted {
                // Handle tombstone: delete locally if exists
                if localAffirmationMap[remoteModel.id] != nil {
                    try? await service?.deleteAffirmation(id: remoteModel.id)
                }
                continue
            }
            if let local = localAffirmationMap[remoteModel.id] {
                // Field-level merge: for each field, use the value with the latest updatedAt
                let merged = local
                // For each field, if remote.updatedAt > local.updatedAt, use remote value
                if remoteModel.updatedAt > local.updatedAt {
                    merged.text = remoteModel.text
                    merged.categoryRawValue = remoteModel.categoryRawValue
                    merged.recordingURLString = remoteModel.recordingURLString
                    merged.completedCycles = remoteModel.completedCycles
                    merged.currentRepetitions = remoteModel.currentRepetitions
                    merged.lastRepetitionDateValue = remoteModel.lastRepetitionDateValue
                    merged.energyLevel = remoteModel.energyLevel
                    merged.moodRatingValue = remoteModel.moodRatingValue
                    merged.notes = remoteModel.notes
                    merged.isFavorite = remoteModel.isFavorite
                    merged.playCount = remoteModel.playCount
                    merged.hasActiveCycle = remoteModel.hasActiveCycle
                    merged.updatedAt = remoteModel.updatedAt
                    try? await service?.updateAffirmation(merged)
                }
                // TODO: For true field-level merge, compare per-field timestamps if available
            } else {
                // Insert new remote affirmation
                if let service = service {
                    do {
                        let _: any AffirmationProtocol = try await service.createAffirmation(
                            text: remoteModel.text,
                            category: remoteModel.category,
                            recordingURL: remoteModel.recordingURL
                        )
                    } catch {
                        // Optionally log or handle the error
                    }
                }
                // TODO: Set all properties, not just text/category/recordingURL
            }
        }
        // 4. Merge remote cycles into local store (similar logic, omitted for brevity)
        // 5. TODO: Handle deletions for cycles
        // 6. TODO: More robust conflict resolution (e.g., per-field timestamps, user prompts)
    }

    // MARK: - Fetch All Records Helper
    typealias CKRecordContinuation = CheckedContinuation<[CKRecord], Error>

    private static func configureOperation(
        operation: CKQueryOperation,
        privateDB: CKDatabase,
        continuation: CheckedContinuation<([CKRecord], CKQueryOperation.Cursor?), Error>
    ) {
        var batch: [CKRecord] = []
        if #available(iOS 15.0, *) {
            operation.recordMatchedBlock = { recordID, result in
                if case .success(let record) = result {
                    batch.append(record)
                }
            }
            operation.queryResultBlock = { (result: Result<CKQueryOperation.Cursor?, Error>) in
                switch result {
                case .success(let cur):
                    continuation.resume(returning: (batch, cur))
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        } else {
            operation.recordFetchedBlock = { record in
                batch.append(record)
            }
            operation.queryCompletionBlock = { cur, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume(returning: (batch, cur))
                }
            }
        }
        privateDB.add(operation)
    }

    private func fetchAllRecords(query: CKQuery) async throws -> [CKRecord] {
        var results: [CKRecord] = []
        var cursor: CKQueryOperation.Cursor? = nil
        repeat {
            let operation: CKQueryOperation
            if let cur = cursor {
                operation = CKQueryOperation(cursor: cur)
            } else {
                operation = CKQueryOperation(query: query)
            }
            let (batch, newCursor): ([CKRecord], CKQueryOperation.Cursor?) =
                try await withCheckedThrowingContinuation {
                    (
                        continuation: CheckedContinuation<
                            ([CKRecord], CKQueryOperation.Cursor?), Error
                        >
                    ) in
                    Self.configureOperation(
                        operation: operation, privateDB: self.privateDB,
                        continuation: continuation)
                }
            results.append(contentsOf: batch)
            cursor = newCursor
        } while cursor != nil
        return results
    }

    // MARK: - Fetch Local Data
    private func fetchLocalAffirmations() async throws -> [any AffirmationProtocol] {
        let service = try? ServiceFactory.shared.getAffirmationService()
        return try await service?.fetchAffirmations() ?? []
    }
    private func fetchLocalCycles() async throws -> [RepetitionCycle] {
        let service = try? ServiceFactory.shared.getAffirmationService()
        let affirmations = try await service?.fetchAffirmations() ?? []
        var allCycles: [RepetitionCycle] = []
        for affirmation in affirmations {
            if let affirmation = affirmation as? Affirmation {
                allCycles.append(contentsOf: affirmation.cycles)
            }
        }
        return allCycles
    }

    // MARK: - Background/Automatic Sync
    private func startBackgroundSync() {
        stopBackgroundSync()
        Task {
            let isEnabled = await isAutomaticSyncEnabled
            guard isEnabled else { return }
            backgroundSyncTimer = Timer.scheduledTimer(
                withTimeInterval: backgroundSyncInterval, repeats: true
            ) { [weak self] _ in
                Task { [weak self] in
                    guard let self = self else { return }
                    do {
                        try await self.syncNow()
                    } catch {
                        await self.handleSyncError(error, context: "backgroundSync")
                    }
                }
            }
        }
    }

    private func stopBackgroundSync() {
        backgroundSyncTimer?.invalidate()
        backgroundSyncTimer = nil
    }

    public nonisolated func setAutomaticSyncEnabled(_ enabled: Bool) async {
        await MainActor.run {
            _isAutomaticSyncEnabled = enabled
            if enabled {
                Task {
                    try? await syncNow()
                }
            }
        }
    }

    // Call this on app launch or when premium status changes
    public func configureBackgroundSyncIfNeeded(isPremium: Bool) {
        Task {
            let isEnabled = await isAutomaticSyncEnabled
            if isPremium && isEnabled {
                startBackgroundSync()
            } else {
                stopBackgroundSync()
            }
        }
    }

    // MARK: - Tombstone Upload for Local Deletions
    public func deleteAffirmationWithTombstone(id: UUID) async throws {
        // Delete locally
        let service = try ServiceFactory.shared.getAffirmationService()
        try await service.deleteAffirmation(id: id)
        // Upload tombstone to CloudKit
        let record = CKRecord(
            recordType: RecordType.affirmation, recordID: CKRecord.ID(recordName: id.uuidString))
        record["id"] = id.uuidString as CKRecordValue
        record["isDeleted"] = true as CKRecordValue
        let modifyOp = CKModifyRecordsOperation(recordsToSave: [record], recordIDsToDelete: nil)
        modifyOp.savePolicy = .changedKeys
        let _: Void = try await withCheckedThrowingContinuation {
            (continuation: CheckedContinuation<Void, Error>) in
            if #available(iOS 15.0, *) {
                modifyOp.modifyRecordsResultBlock = { result in
                    switch result {
                    case .success:
                        continuation.resume(returning: ())
                    case .failure(let error):
                        continuation.resume(throwing: error)
                    }
                }
            } else {
                modifyOp.modifyRecordsCompletionBlock = { _, _, error in
                    if let error = error {
                        continuation.resume(throwing: error)
                    } else {
                        continuation.resume(returning: ())
                    }
                }
            }
            self.privateDB.add(modifyOp)
        }
    }
}

#if compiler(>=5.7)
    // swiftlint:disable:next sendable_conformance
#endif

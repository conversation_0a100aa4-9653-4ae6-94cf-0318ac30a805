import Foundation

/// Service to abstract user preferences storage using UserDefaults
public final class UserPreferencesService {
    private let userDefaults: UserDefaults
    
    public init(userDefaults: UserDefaults = .standard) {
        self.userDefaults = userDefaults
    }
    
    public var isDarkModeEnabled: Bool {
        get { userDefaults.bool(forKey: "isDarkModeEnabled") }
        set { userDefaults.set(newValue, forKey: "isDarkModeEnabled") }
    }
    
    public var notificationsEnabled: Bool {
        get { userDefaults.bool(forKey: "notificationsEnabled") }
        set { userDefaults.set(newValue, forKey: "notificationsEnabled") }
    }
    
    public var dailyGoal: Int {
        get { userDefaults.integer(forKey: "dailyGoal") }
        set { userDefaults.set(newValue, forKey: "dailyGoal") }
    }
    // Add more preferences as needed
} 
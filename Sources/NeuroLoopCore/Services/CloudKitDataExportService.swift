import CloudKit
import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels

@available(iOS 17.0, macOS 14.0, *)
public class CloudKitDataExportService: DataExportServiceProtocol {
    private let container: CKContainer
    private let privateDB: CKDatabase

    /// Utility function to safely initialize a CKContainer
    private static func safeContainer(_ container: CKContainer?) -> CKContainer {
        #if DEBUG
            if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
                // Use a mock or stub for previews
                return CKContainer(identifier: "iCloud.com.neuroloop.NeuroLoop100")
            }
        #endif

        // Use the ServiceFactory.cloudKitAvailable flag to determine behavior
        if !ServiceFactory.cloudKitAvailable {
            // Return a mock container instead of crashing
            return CKContainer(identifier: "iCloud.com.neuroloop.NeuroLoop100")
        }

        let realContainer = container ?? CKContainer.default()

        // Optional runtime check - don't crash, just log
        var accountStatus: CKAccountStatus = .couldNotDetermine
        let semaphore = DispatchSemaphore(value: 0)
        realContainer.accountStatus { status, _ in
            let localStatus = status
            accountStatus = localStatus
            semaphore.signal()
        }
        semaphore.wait()

        if accountStatus != .available {
            print(
                "WARNING: CloudKit is not available: account status = \(accountStatus). Using mock container."
            )
            return CKContainer(identifier: "iCloud.com.neuroloop.NeuroLoop100")
        }

        return realContainer
    }

    public init(container: CKContainer? = nil) {
        let realContainer = Self.safeContainer(container)
        self.container = realContainer
        self.privateDB = realContainer.privateCloudDatabase
    }

    public func exportData() async throws -> URL {
        // Create a temporary directory for the export
        let tempDir = FileManager.default.temporaryDirectory
        let exportDir = tempDir.appendingPathComponent("NeuroLoopExport")
        try FileManager.default.createDirectory(at: exportDir, withIntermediateDirectories: true)

        // Export affirmations
        let affirmationService = try ServiceFactory.shared.getAffirmationService()
        let affirmations = try await affirmationService.fetchAffirmations()

        // Convert affirmations to dictionary format
        let affirmationDicts = affirmations.map { affirmation -> [String: Any] in
            var dict: [String: Any] = [
                "id": affirmation.id.uuidString,
                "text": affirmation.text,
                "category": affirmation.category.rawValue,
                "createdAt": affirmation.createdAt,
                "updatedAt": affirmation.updatedAt,
                "currentCycleDay": affirmation.currentCycleDay,
                "completedCycles": affirmation.completedCycles,
                "currentRepetitions": affirmation.currentRepetitions,
                "energyLevel": affirmation.energyLevel,
                "isFavorite": affirmation.isFavorite,
                "playCount": affirmation.playCount,
                "hasActiveCycle": affirmation.hasActiveCycle,
                "isCurrentCycleComplete": affirmation.isCurrentCycleComplete,
                "todayProgress": affirmation.todayProgress,
                "cycleProgress": affirmation.cycleProgress,
                "hasTodayQuotaMet": affirmation.hasTodayQuotaMet,
                "canPerformRepetition": affirmation.canPerformRepetition,
            ]

            if let recordingURL = affirmation.recordingURL {
                dict["recordingURL"] = recordingURL.absoluteString
            }
            if let cycleStartDate = affirmation.cycleStartDate {
                dict["cycleStartDate"] = cycleStartDate
            }
            if let lastRepetitionDate = affirmation.lastRepetitionDate {
                dict["lastRepetitionDate"] = lastRepetitionDate
            }
            if let moodRating = affirmation.moodRating {
                dict["moodRating"] = moodRating
            }
            if let notes = affirmation.notes {
                dict["notes"] = notes
            }

            return dict
        }

        // Create JSON data
        let data = try JSONSerialization.data(
            withJSONObject: affirmationDicts, options: .prettyPrinted)

        // Save to file
        let exportFile = exportDir.appendingPathComponent("affirmations.json")
        try data.write(to: exportFile)

        return exportDir
    }

    public func deleteAllData() async throws {
        // Delete local data
        let affirmationService = try ServiceFactory.shared.getAffirmationService()
        let affirmations = try await affirmationService.fetchAffirmations()

        for affirmation in affirmations {
            try await affirmationService.deleteAffirmation(id: affirmation.id)
        }

        // Delete CloudKit data
        let query = CKQuery(recordType: "Affirmation", predicate: NSPredicate(value: true))
        let (results, _) = try await privateDB.records(matching: query)

        for (_, result) in results {
            switch result {
            case .success(let record):
                try await privateDB.deleteRecord(withID: record.recordID)
            case .failure(let error):
                print("Failed to delete record: \(error)")
            }
        }
    }

    public func importData(from url: URL) async throws {
        // TODO: Implement actual CloudKit data import
        throw NSError(
            domain: "CloudKitDataExportService", code: -1,
            userInfo: [NSLocalizedDescriptionKey: "Not implemented"])
    }
}

import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

@MainActor
public final class PurchaseManager: PurchaseManagerProtocol, PurchaseManagerSendable, @unchecked Sendable {
    public static let shared = PurchaseManager()
    private init() {}
    
    public func purchasePremium() async throws {
        // TODO: Implement purchase logic
    }
    
    public func restorePurchases() async throws {
        // TODO: Implement restore logic
    }
} 
import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@MainActor
public final class PremiumFeatureUnlocker: ObservableObject {
    @Published var isUnlocked: Bool = false
    @Published var showUnlockAnimation: Bool = false
    @Published var unlockProgress: Double = 0.0
    @Published private(set) var featureAvailability: [NeuroLoopTypes.PremiumFeature: Bool] = [:]

    public let featureName: String
    private let unlockDuration: TimeInterval = 2.0
    private let premiumService: PremiumServiceProtocol

    public init(featureName: String, premiumService: PremiumServiceProtocol) {
        self.featureName = featureName
        self.premiumService = premiumService
        self.isUnlocked = false
        self.showUnlockAnimation = false
        self.unlockProgress = 0.0
    }

    public func checkFeatureAvailability(_ feature: NeuroLoopTypes.PremiumFeature) async {
        let isAvailable = premiumService.checkFeatureAvailability(feature)
        featureAvailability[feature] = isAvailable
    }

    public func premiumFeatureView<Content: View>(
        _ feature: NeuroLoopTypes.PremiumFeature,
        onUpgrade: (() -> Void)? = nil,
        @ViewBuilder content: () -> Content
    ) -> some View {
        Group {
            if featureAvailability[feature] ?? false {
                content()
            } else {
                PremiumFeatureLockedView(feature: feature, onUpgrade: onUpgrade)
            }
        }
        .task {
            await self.checkFeatureAvailability(feature)
        }
    }

    public func premiumFeatureButton<Content: View>(
        _ feature: NeuroLoopTypes.PremiumFeature,
        onUpgrade: (() -> Void)? = nil,
        @ViewBuilder content: () -> Content
    ) -> some View {
        Group {
            if featureAvailability[feature] ?? false {
                content()
            } else {
                Button(action: { onUpgrade?() }) {
                    HStack {
                        content()
                        Image(systemName: "lock.fill")
                            .foregroundColor(.gray)
                    }
                }
                .disabled(true)
            }
        }
        .task {
            await self.checkFeatureAvailability(feature)
        }
    }

    public func unlock() {
        withAnimation(.easeInOut(duration: unlockDuration)) {
            showUnlockAnimation = true
            unlockProgress = 1.0
        }

        Task { @MainActor in
            try? await Task.sleep(for: .seconds(unlockDuration))
            withAnimation {
                self.showUnlockAnimation = false
                self.isUnlocked = true
            }
        }
    }
}

public struct PremiumFeatureLockedView: View {
    public let feature: NeuroLoopTypes.PremiumFeature
    public var onUpgrade: (() -> Void)?
    @EnvironmentObject private var themeManager: ThemeManager

    public init(feature: NeuroLoopTypes.PremiumFeature, onUpgrade: (() -> Void)? = nil) {
        self.feature = feature
        self.onUpgrade = onUpgrade
    }

    public var body: some View {
        VStack(spacing: 10) {
            Image(systemName: "lock.fill")
                .font(.system(size: 40))
                .foregroundColor(.gray)
                .accessibilityHidden(true)

            Text("Premium Feature")
                .font(.headline)
                .accessibilityAddTraits(.isHeader)
                .accessibilityLabel("Premium Feature Locked")

            Text(featureDescription)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .accessibilityLabel(featureDescription)

            Button(action: {
                #if os(iOS)
                    let generator = UIImpactFeedbackGenerator(style: .medium)
                    generator.impactOccurred()
                #endif
                onUpgrade?()
            }) {
                Text("Upgrade to Premium")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                AnyShapeStyle(
                                    themeManager.currentTheme.cardBackgroundColor.asGradient
                                        ?? themeManager.currentTheme.cardBackgroundColor.asColor
                                        .asFallbackGradient())
                            )
                            .shadow(
                                color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                                radius: 10, x: 0, y: 5)
                    )
            }
            .padding(.top)
            .accessibilityIdentifier("PremiumUpgradeButton")
            .accessibilityLabel("Upgrade to Premium")
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    AnyShapeStyle(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? themeManager.currentTheme.cardBackgroundColor.asColor
                            .asFallbackGradient())
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.1), radius: 10,
                    x: 0, y: 5)
        )
        .accessibilityElement(children: .combine)
        .accessibilityIdentifier("PremiumFeatureLockedView")
    }

    private var featureDescription: String {
        switch feature {
        case .unlimitedAffirmations:
            return
                "Create and manage unlimited affirmations with advanced organization and tracking"
        case .customThemes:
            return "Personalize your experience with custom gradient themes and visual effects"
        case .advancedAnalytics:
            return
                "Get detailed insights into your practice with advanced progress tracking and streak analytics"
        case .dataExport:
            return "Export and backup your affirmations, progress, and statistics for safekeeping"
        }
    }
}

// MARK: - Preview

struct PremiumFeatureLockedView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            PremiumFeatureLockedView(feature: .unlimitedAffirmations)
            PremiumFeatureLockedView(feature: .customThemes)
            PremiumFeatureLockedView(feature: .advancedAnalytics)
            PremiumFeatureLockedView(feature: .dataExport)
        }
        .padding()
        .environmentObject(ThemeManager.shared)
    }
}

struct PremiumFeatureUnlockView: View {
    @ObservedObject var unlocker: PremiumFeatureUnlocker
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        ZStack {
            Rectangle()
                .fill(
                    AnyShapeStyle(
                        themeManager.currentTheme.backgroundColor.asGradient
                            ?? themeManager.currentTheme.backgroundColor.asColor
                            .asFallbackGradient())
                )
                .ignoresSafeArea()

            VStack(spacing: 24) {
                // Feature icon
                Image(systemName: "star.fill")
                    .font(.system(size: 60))
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)

                // Feature name
                Text(unlocker.featureName)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                // Unlock progress
                if unlocker.showUnlockAnimation {
                    ProgressView(value: unlocker.unlockProgress)
                        .progressViewStyle(
                            LinearProgressViewStyle(
                                tint: themeManager.currentTheme.accentColor.asColor)
                        )
                        .frame(width: 200)
                }

                // Unlock button
                Button(action: {
                    unlocker.unlock()
                }) {
                    Text("Unlock Feature")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(width: 200, height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .background(
                                    AnyShapeStyle(
                                        themeManager.currentTheme.accentColor.asGradient
                                            ?? themeManager.currentTheme.accentColor.asColor
                                            .asFallbackGradient())
                                )
                                .shadow(
                                    color: themeManager.currentTheme.shadowColor.color.opacity(0.2),
                                    radius: 10, x: 0, y: 5)
                        )
                }
                .disabled(unlocker.showUnlockAnimation)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .background(
                        AnyShapeStyle(
                            themeManager.currentTheme.cardBackgroundColor.asGradient
                                ?? themeManager.currentTheme.cardBackgroundColor.asColor
                                .asFallbackGradient())
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.1), radius: 20,
                        x: 0, y: 10)
            )
            .padding()
        }
    }
}

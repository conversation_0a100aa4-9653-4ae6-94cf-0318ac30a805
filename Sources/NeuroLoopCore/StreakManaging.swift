import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels

/// Protocol for managing streaks
@available(macOS 10.15, iOS 13.0, *)
public protocol StreakManaging {
    /// Gets the current streak for an affirmation
    /// - Parameter affirmation: The affirmation to get the streak for
    /// - Returns: The current streak
    func getCurrentStreak(for affirmation: AffirmationProtocol) -> Int

    /// Updates the streak for an affirmation
    /// - Parameters:
    ///   - affirmation: The affirmation to update the streak for
    ///   - count: The new repetition count
    /// - Returns: The updated streak
    func updateStreak(for affirmation: AffirmationProtocol, count: Int) -> Int

    /// Resets the streak for an affirmation
    /// - Parameter affirmation: The affirmation to reset the streak for
    func resetStreak(for affirmation: AffirmationProtocol)

    /// Checks if the streak is complete (7 days)
    /// - Parameter affirmation: The affirmation to check
    /// - Returns: True if the streak is complete
    func isStreakComplete(for affirmation: AffirmationProtocol) -> <PERSON><PERSON>
}

import Foundation
import SwiftUI
import NeuroLoopTypes
import NeuroLoopInterfaces

@MainActor
public class ThemeManager: ObservableObject, ThemeManagingSendable, @unchecked Sendable {
    public static let shared = ThemeManager()

    public var currentTheme: NeuroLoopTypes.Theme = NeuroLoopTypes.Theme.blue
    @Published public private(set) var builtInThemes: [NeuroLoopTypes.Theme] = [NeuroLoopTypes.Theme.blue, NeuroLoopTypes.Theme.light, NeuroLoopTypes.Theme.dark]
    @Published public private(set) var customThemes: [NeuroLoopTypes.Theme] = []

    // Computed property to get all available themes
    public var availableThemes: [NeuroLoopTypes.Theme] {
        builtInThemes + customThemes
    }

    private init() {
        loadThemes()
    }

    public func setTheme(_ theme: NeuroLoopTypes.Theme) {
        self.currentTheme = theme
        saveCurrentTheme()
    }

    // CRUD for custom themes
    public func addCustomTheme(_ theme: NeuroLoopTypes.Theme) {
        customThemes.append(theme)
        saveThemes()
    }

    public func updateCustomTheme(_ theme: NeuroLoopTypes.Theme) {
        if let idx = customThemes.firstIndex(where: { $0.id == theme.id }) {
            customThemes[idx] = theme
            saveThemes()
        }
    }

    public func deleteCustomTheme(_ theme: NeuroLoopTypes.Theme) {
        customThemes.removeAll { $0.id == theme.id }
        saveThemes()
    }

    // Persistence (to be implemented in ThemePersistenceService)
    private func loadThemes() {
        customThemes = ThemePersistenceService.loadCustomThemes() as [NeuroLoopTypes.Theme]
        let allThemes = builtInThemes + customThemes
        if let loaded = ThemePersistenceService.loadCurrentTheme(from: allThemes as [NeuroLoopTypes.Theme]) {
            self.currentTheme = loaded
        }
    }
    private func saveThemes() {
        ThemePersistenceService.saveCustomThemes(customThemes as [NeuroLoopTypes.Theme])
    }
    private func saveCurrentTheme() {
        ThemePersistenceService.saveCurrentTheme(currentTheme as NeuroLoopTypes.Theme)
    }
}

import Foundation
import NeuroLoopTypes
@preconcurrency import NeuroLoopTypes

@MainActor
public struct ThemePersistenceService {
    private static let customThemesKey = "customThemes"
    private static let currentThemeIDKey = "currentThemeID"
    private static let userDefaults = UserDefaults.standard

    public static func saveCustomThemes(_ themes: [NeuroLoopTypes.Theme]) {
        let encoder = JSONEncoder()
        if let data = try? encoder.encode(themes) {
            userDefaults.set(data, forKey: customThemesKey)
        }
    }

    public static func loadCustomThemes() -> [NeuroLoopTypes.Theme] {
        guard let data = userDefaults.data(forKey: customThemesKey) else { return [] }
        let decoder = JSONDecoder()
        return (try? decoder.decode([NeuroLoopTypes.Theme].self, from: data)) ?? []
    }

    public static func saveCurrentTheme(_ theme: NeuroLoopTypes.Theme) {
        userDefaults.set(theme.id.uuidString, forKey: currentThemeIDKey)
    }

    public static func loadCurrentTheme(from allThemes: [NeuroLoopTypes.Theme]) -> NeuroLoopTypes.Theme? {
        guard let idString = userDefaults.string(forKey: currentThemeIDKey),
              let id = UUID(uuidString: idString)
        else { return nil }
        return allThemes.first(where: { $0.id == id })
    }
}

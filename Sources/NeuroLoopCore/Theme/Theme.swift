import SwiftUI

// Renamed to avoid conflict with NeuroLoopShared.Theme
@available(macOS 10.15, iOS 13.0, *)
public struct CoreTheme {
    public let primaryColor: Color
    public let secondaryColor: Color
    public let backgroundColor: Color
    public let textColor: Color
    public let accentColor: Color

    public init(
        primaryColor: Color,
        secondaryColor: Color,
        backgroundColor: Color,
        textColor: Color,
        accentColor: Color
    ) {
        self.primaryColor = primaryColor
        self.secondaryColor = secondaryColor
        self.backgroundColor = backgroundColor
        self.textColor = textColor
        self.accentColor = accentColor
    }

    // Default theme
    @available(macOS 10.15, iOS 13.0, *)
    public static let `default` = CoreTheme(
        primaryColor: Color.blue,
        secondaryColor: Color.blue.opacity(0.7),
        backgroundColor: Color.white,
        textColor: Color.black,
        accentColor: Color.blue
    )

    // Dark theme
    @available(macOS 10.15, iOS 13.0, *)
    public static let dark = CoreTheme(
        primaryColor: Color.blue,
        secondaryColor: Color.blue.opacity(0.7),
        backgroundColor: Color.black,
        textColor: Color.white,
        accentColor: Color.blue
    )

}

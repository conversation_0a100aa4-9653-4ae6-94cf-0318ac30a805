import Foundation
import Network
import Combine

/// A class that monitors network connectivity status
@MainActor
public final class NetworkMonitor: ObservableObject {
    /// Shared instance for app-wide network monitoring
    public static let shared = NetworkMonitor()
    
    /// Published property that indicates whether the device is connected to the internet
    @Published public private(set) var isConnected: Bool = true
    
    /// Published property that indicates the current connection type
    @Published public private(set) var connectionType: ConnectionType = .unknown
    
    /// Network path monitor from Network framework
    private let monitor = NWPathMonitor()
    
    /// Queue for processing network updates
    private let queue = DispatchQueue(label: "NetworkMonitor", qos: .background)
    
    /// Connection types that can be detected
    public enum ConnectionType {
        case wifi
        case cellular
        case ethernet
        case unknown
    }
    
    private init() {
        startMonitoring()
    }
    
    /// Start monitoring network changes
    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            // Dispatch to main actor since we're updating published properties
            Task { @MainActor [weak self] in
                guard let self = self else { return }
                
                // Update connection status
                self.isConnected = path.status == .satisfied
                
                // Determine connection type
                self.connectionType = self.getConnectionType(path)
                
                print("NetworkMonitor: Connection status changed - connected: \(self.isConnected), type: \(self.connectionType)")
            }
        }
        
        // Start monitoring on the background queue
        monitor.start(queue: queue)
    }
    
    /// Stop monitoring network changes
    public func stopMonitoring() {
        monitor.cancel()
    }
    
    /// Get the current connection type from the network path
    private func getConnectionType(_ path: NWPath) -> ConnectionType {
        if path.usesInterfaceType(.wifi) {
            return .wifi
        } else if path.usesInterfaceType(.cellular) {
            return .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            return .ethernet
        } else {
            return .unknown
        }
    }
}

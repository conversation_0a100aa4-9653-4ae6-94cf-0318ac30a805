import Foundation
import SwiftUI

/// A comprehensive error handling system for the NeuroLoop app
@MainActor
public final class ErrorHandler {
    /// Shared instance for app-wide error handling
    public static let shared = ErrorHandler()
    
    /// Types of errors that can occur in the app
    public enum ErrorType {
        case audioPermission
        case audioRecording
        case audioPlayback
        case speechRecognition
        case dataAccess
        case network
        case validation
        case unknown
    }
    
    /// Structure to represent an error with additional context
    public struct AppError: Identifiable, Error {
        public let id = UUID()
        public let type: ErrorType
        public let message: String
        public let originalError: Error?
        public let timestamp: Date
        public let recoveryAction: (() -> Void)?
        
        public init(
            type: ErrorType,
            message: String,
            originalError: Error? = nil,
            recoveryAction: (() -> Void)? = nil
        ) {
            self.type = type
            self.message = message
            self.originalError = originalError
            self.timestamp = Date()
            self.recoveryAction = recoveryAction
        }
    }
    
    /// Published property that contains the most recent error
    @Published public private(set) var currentError: AppError?
    
    /// Array of recent errors for debugging
    private var recentErrors: [AppError] = []
    
    /// Maximum number of recent errors to keep
    private let maxRecentErrors = 10
    
    /// Private initializer for singleton
    private init() {}
    
    /// Reports an error to the error handler
    /// - Parameters:
    ///   - error: The error to report
    ///   - type: The type of error
    ///   - message: Optional custom message to display
    ///   - recoveryAction: Optional action to recover from the error
    /// - Returns: The created AppError
    @discardableResult
    public func reportError(
        _ error: Error,
        type: ErrorType = .unknown,
        message: String? = nil,
        recoveryAction: (() -> Void)? = nil
    ) -> AppError {
        let errorMessage = message ?? error.localizedDescription
        
        // Create an AppError
        let appError = AppError(
            type: type,
            message: errorMessage,
            originalError: error,
            recoveryAction: recoveryAction
        )
        
        // Log the error
        print("ErrorHandler: [\(type)] \(errorMessage)")
        if let originalError = error as NSError? {
            print("ErrorHandler: Original error - Domain: \(originalError.domain), Code: \(originalError.code)")
        }
        
        // Store the error
        currentError = appError
        addToRecentErrors(appError)
        
        return appError
    }
    
    /// Creates and reports a custom error
    /// - Parameters:
    ///   - type: The type of error
    ///   - message: The error message
    ///   - recoveryAction: Optional action to recover from the error
    /// - Returns: The created AppError
    @discardableResult
    public func reportCustomError(
        type: ErrorType,
        message: String,
        recoveryAction: (() -> Void)? = nil
    ) -> AppError {
        // Create a custom NSError
        let error = NSError(
            domain: "com.neuroloop.app",
            code: errorCodeForType(type),
            userInfo: [NSLocalizedDescriptionKey: message]
        )
        
        return reportError(error, type: type, message: message, recoveryAction: recoveryAction)
    }
    
    /// Clears the current error
    public func clearCurrentError() {
        currentError = nil
    }
    
    /// Gets recent errors for debugging
    /// - Returns: Array of recent errors
    public func getRecentErrors() -> [AppError] {
        return recentErrors
    }
    
    /// Adds an error to the recent errors list
    /// - Parameter error: The error to add
    private func addToRecentErrors(_ error: AppError) {
        recentErrors.insert(error, at: 0)
        
        // Trim the list if it gets too long
        if recentErrors.count > maxRecentErrors {
            recentErrors = Array(recentErrors.prefix(maxRecentErrors))
        }
    }
    
    /// Gets an error code for a given error type
    /// - Parameter type: The error type
    /// - Returns: An integer error code
    private func errorCodeForType(_ type: ErrorType) -> Int {
        switch type {
        case .audioPermission:
            return 1001
        case .audioRecording:
            return 1002
        case .audioPlayback:
            return 1003
        case .speechRecognition:
            return 1004
        case .dataAccess:
            return 2001
        case .network:
            return 3001
        case .validation:
            return 4001
        case .unknown:
            return 9999
        }
    }
}

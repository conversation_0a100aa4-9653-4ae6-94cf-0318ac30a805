import Foundation
import SwiftUI

/// A comprehensive alert management system for the NeuroLoop app
@MainActor
public final class AlertManager: ObservableObject {
    /// Shared instance for app-wide alert management
    public static let shared = AlertManager()
    
    /// Structure to represent an alert
    public struct AlertItem: Identifiable {
        public let id = UUID()
        public let title: String
        public let message: String
        public let dismissButton: Alert.Button
        public let primaryButton: Alert.Button?
        public let secondaryButton: Alert.Button?
        public let showSettingsButton: Bool
        public let settingsAction: (() -> Void)?
        public let timestamp: Date
        
        public init(
            title: String,
            message: String,
            dismissButtonLabel: String = "OK",
            dismissAction: (() -> Void)? = nil,
            primaryButtonLabel: String? = nil,
            primaryAction: (() -> Void)? = nil,
            secondaryButtonLabel: String? = nil,
            secondaryAction: (() -> Void)? = nil,
            showSettingsButton: Bool = false,
            settingsAction: (() -> Void)? = nil
        ) {
            self.title = title
            self.message = message
            self.timestamp = Date()
            self.showSettingsButton = showSettingsButton
            self.settingsAction = settingsAction
            
            // Create dismiss button
            self.dismissButton = Alert.Button.default(Text(dismissButtonLabel)) {
                dismissAction?()
            }
            
            // Create primary button if provided
            if let primaryButtonLabel = primaryButtonLabel {
                self.primaryButton = Alert.Button.default(Text(primaryButtonLabel)) {
                    primaryAction?()
                }
            } else {
                self.primaryButton = nil
            }
            
            // Create secondary button if provided
            if let secondaryButtonLabel = secondaryButtonLabel {
                self.secondaryButton = Alert.Button.cancel(Text(secondaryButtonLabel)) {
                    secondaryAction?()
                }
            } else {
                self.secondaryButton = nil
            }
        }
    }
    
    /// Published property that contains the current alert
    @Published public private(set) var currentAlert: AlertItem?
    
    /// Flag to indicate if an alert is currently being shown
    @Published public private(set) var isShowingAlert = false
    
    /// Array of recent alerts for debugging
    private var recentAlerts: [AlertItem] = []
    
    /// Maximum number of recent alerts to keep
    private let maxRecentAlerts = 10
    
    /// Minimum time between alerts in seconds
    private let minAlertInterval: TimeInterval = 1.0
    
    /// Time of the last shown alert
    private var lastAlertTime: Date = Date(timeIntervalSince1970: 0)
    
    /// Queue of pending alerts
    private var alertQueue: [AlertItem] = []
    
    /// Private initializer for singleton
    private init() {}
    
    /// Shows an alert
    /// - Parameter alert: The alert to show
    public func showAlert(_ alert: AlertItem) {
        let now = Date()
        
        // Check if we're showing too many alerts too quickly
        if now.timeIntervalSince(lastAlertTime) < minAlertInterval {
            // Queue the alert instead of showing it immediately
            alertQueue.append(alert)
            print("AlertManager: Alert queued - \(alert.title)")
            
            // If this is the first queued alert, schedule processing
            if alertQueue.count == 1 {
                scheduleQueueProcessing()
            }
            
            return
        }
        
        // Show the alert
        currentAlert = alert
        isShowingAlert = true
        lastAlertTime = now
        
        // Add to recent alerts
        addToRecentAlerts(alert)
        
        print("AlertManager: Showing alert - \(alert.title)")
    }
    
    /// Dismisses the current alert
    public func dismissAlert() {
        isShowingAlert = false
        currentAlert = nil
        
        // Process the next alert in the queue if any
        processAlertQueue()
    }
    
    /// Gets recent alerts for debugging
    /// - Returns: Array of recent alerts
    public func getRecentAlerts() -> [AlertItem] {
        return recentAlerts
    }
    
    /// Adds an alert to the recent alerts list
    /// - Parameter alert: The alert to add
    private func addToRecentAlerts(_ alert: AlertItem) {
        recentAlerts.insert(alert, at: 0)
        
        // Trim the list if it gets too long
        if recentAlerts.count > maxRecentAlerts {
            recentAlerts = Array(recentAlerts.prefix(maxRecentAlerts))
        }
    }
    
    /// Schedules processing of the alert queue
    private func scheduleQueueProcessing() {
        // Wait for the minimum interval before processing the queue
        Task {
            try? await Task.sleep(nanoseconds: UInt64(minAlertInterval * 1_000_000_000))
            processAlertQueue()
        }
    }
    
    /// Processes the next alert in the queue
    private func processAlertQueue() {
        // Only process if we're not showing an alert
        guard !isShowingAlert, !alertQueue.isEmpty else {
            return
        }
        
        // Get the next alert
        let nextAlert = alertQueue.removeFirst()
        
        // Show it
        currentAlert = nextAlert
        isShowingAlert = true
        lastAlertTime = Date()
        
        // Add to recent alerts
        addToRecentAlerts(nextAlert)
        
        print("AlertManager: Showing queued alert - \(nextAlert.title)")
        
        // If there are more alerts, schedule processing
        if !alertQueue.isEmpty {
            scheduleQueueProcessing()
        }
    }
}

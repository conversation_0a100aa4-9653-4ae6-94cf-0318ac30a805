// CollectionsWrapper.swift
// This file provides a wrapper for the Collections module

import OrderedCollections

// Re-export OrderedDictionary
public typealias OrderedDictionary<Key: Hashable, Value> = OrderedCollections.OrderedDictionary<Key, Value>

// Re-export OrderedSet
public typealias OrderedSet<Element: Hashable> = OrderedCollections.OrderedSet<Element>

// Create a Collections namespace
public enum Collections {
    // Empty enum to provide a namespace
}

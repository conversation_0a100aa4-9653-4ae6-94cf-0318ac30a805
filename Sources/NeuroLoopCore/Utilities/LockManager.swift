import Foundation

/// A robust lock manager that prevents deadlocks and orphaned locks
@MainActor
public final class LockManager {
    /// Shared instance for app-wide lock management
    public static let shared = LockManager()
    
    /// Structure to track lock information
    private struct LockInfo {
        let owner: String
        let acquiredTime: Date
        var timeoutTask: Task<Void, Never>?
    }
    
    /// Dictionary to store active locks
    private var locks: [String: LockInfo] = [:]
    
    /// Default timeout for locks in seconds
    private let defaultTimeout: TimeInterval = 10.0
    
    /// Private initializer for singleton
    private init() {}
    
    /// Acquires a lock with the specified ID
    /// - Parameters:
    ///   - lockId: Unique identifier for the lock
    ///   - owner: Name of the component acquiring the lock (for debugging)
    ///   - timeout: Optional timeout in seconds (defaults to 10 seconds)
    /// - Returns: True if lock was acquired, false otherwise
    public func acquireLock(for lockId: String, owner: String = "Unknown", timeout: TimeInterval? = nil) async -> Bool {
        // Check if lock is already held
        if let existingLock = locks[lockId] {
            let elapsedTime = Date().timeIntervalSince(existingLock.acquiredTime)
            print("LockManager: Lock \(lockId) already held by \(existingLock.owner) for \(String(format: "%.2f", elapsedTime))s")
            return false
        }
        
        // Create timeout task
        let timeoutTask = Task { [weak self] in
            let actualTimeout = timeout ?? defaultTimeout
            try? await Task.sleep(nanoseconds: UInt64(actualTimeout * 1_000_000_000))
            
            // Check if task was cancelled
            if Task.isCancelled {
                return
            }
            
            // Release the lock if it's still held after timeout
            await self?.releaseLock(for: lockId, reason: "timeout after \(actualTimeout)s")
        }
        
        // Acquire the lock
        locks[lockId] = LockInfo(
            owner: owner,
            acquiredTime: Date(),
            timeoutTask: timeoutTask
        )
        
        print("LockManager: Lock \(lockId) acquired by \(owner)")
        return true
    }
    
    /// Releases a lock with the specified ID
    /// - Parameters:
    ///   - lockId: Unique identifier for the lock
    ///   - reason: Optional reason for releasing the lock (for debugging)
    /// - Returns: True if lock was released, false if it wasn't held
    public func releaseLock(for lockId: String, reason: String = "normal release") async -> Bool {
        // Check if lock exists
        guard let lockInfo = locks[lockId] else {
            print("LockManager: Attempted to release lock \(lockId) but it wasn't held")
            return false
        }
        
        // Cancel timeout task
        lockInfo.timeoutTask?.cancel()
        
        // Remove lock
        locks.removeValue(forKey: lockId)
        
        let elapsedTime = Date().timeIntervalSince(lockInfo.acquiredTime)
        print("LockManager: Lock \(lockId) released by \(lockInfo.owner) after \(String(format: "%.2f", elapsedTime))s (\(reason))")
        return true
    }
    
    /// Checks if a lock is currently held
    /// - Parameter lockId: Unique identifier for the lock
    /// - Returns: True if lock is held, false otherwise
    public func isLockHeld(for lockId: String) -> Bool {
        return locks[lockId] != nil
    }
    
    /// Gets information about all currently held locks
    /// - Returns: Dictionary of lock IDs and their owners
    public func getActiveLocks() -> [String: String] {
        var result: [String: String] = [:]
        for (lockId, lockInfo) in locks {
            let elapsedTime = Date().timeIntervalSince(lockInfo.acquiredTime)
            result[lockId] = "\(lockInfo.owner) (held for \(String(format: "%.2f", elapsedTime))s)"
        }
        return result
    }
    
    /// Releases all locks (emergency use only)
    public func releaseAllLocks() async {
        let lockIds = Array(locks.keys)
        for lockId in lockIds {
            await releaseLock(for: lockId, reason: "emergency release")
        }
    }
}

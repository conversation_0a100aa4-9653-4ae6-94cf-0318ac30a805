import Foundation

/// A utility class for managing async tasks to prevent memory leaks
@MainActor
public final class TaskManager {
    /// Dictionary to store active tasks by their identifier
    private var tasks: [String: Task<Void, Error>] = [:]

    /// Creates a new TaskManager instance
    public init() {}

    /// Starts a new task with the given identifier
    /// - Parameters:
    ///   - id: Unique identifier for the task
    ///   - priority: Priority for the task (default: .medium)
    ///   - operation: The async operation to perform
    /// - Returns: The created task
    @discardableResult
    public func startTask(
        id: String,
        priority: TaskPriority = .medium,
        operation: @escaping () async throws -> Void
    ) -> Task<Void, Error> {
        // Cancel any existing task with the same ID
        cancelTask(id: id)

        // Create a new task
        let task = Task(priority: priority) {
            do {
                try await operation()
            } catch is CancellationError {
                print("TaskManager: Task \(id) was cancelled")
            } catch {
                print("TaskManager: Task \(id) failed with error: \(error.localizedDescription)")
                throw error
            }
        }

        // Store the task
        tasks[id] = task

        return task
    }

    /// Cancels a task with the given identifier
    /// - Parameter id: Unique identifier for the task
    /// - Returns: True if a task was cancelled, false otherwise
    @discardableResult
    public func cancelTask(id: String) -> Bool {
        guard let task = tasks[id] else {
            return false
        }

        task.cancel()
        tasks.removeValue(forKey: id)
        return true
    }

    /// Cancels all tasks
    public func cancelAllTasks() {
        for (id, task) in tasks {
            print("TaskManager: Cancelling task \(id)")
            task.cancel()
        }
        tasks.removeAll()
    }

    /// Checks if a task with the given identifier is running
    /// - Parameter id: Unique identifier for the task
    /// - Returns: True if the task is running, false otherwise
    public func isTaskRunning(id: String) -> Bool {
        guard let task = tasks[id] else {
            return false
        }

        return !task.isCancelled
    }

    /// Gets the number of active tasks
    /// - Returns: The number of active tasks
    public func activeTaskCount() -> Int {
        return tasks.count
    }

    /// Gets a list of active task identifiers
    /// - Returns: Array of active task identifiers
    public func activeTaskIds() -> [String] {
        return Array(tasks.keys)
    }

    /// Deinitializer that cancels all tasks
    deinit {
        // Cannot call actor-isolated method directly in deinit
        // Just cancel all tasks directly
        for (id, task) in tasks {
            print("TaskManager: Cancelling task \(id) in deinit")
            task.cancel()
        }
        tasks.removeAll()
    }
}

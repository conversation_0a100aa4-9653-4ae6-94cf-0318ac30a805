import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/**
 * PreviewStreakService
 *
 * A preview implementation of StreakServiceProtocol for use in SwiftUI previews and testing.
 * This implementation provides realistic simulation of affirmation streak tracking and calendar data.
 *
 * ## Usage Example
 * ```
 * let service = PreviewServiceFactory.shared.getStreakService()
 * let viewModel = StreakVisualizationViewModel(streakService: service)
 * ```
 *
 * ## Preview Data
 * This implementation provides the following test data:
 * - Simulated streak tracking with realistic progression
 * - Calendar data with varying completion states
 * - Streak statistics with realistic distributions
 * - Risk assessment based on recent activity
 *
 * ## Actor Isolation
 * This implementation is isolated to MainActor and uses @unchecked Sendable for preview purposes.
 */

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewStreakService: StreakServiceProtocol, @unchecked Sendable {
    // MARK: - Private Properties

    private var streakStates: [UUID: StreakState] = [:]
    private let calendar = Calendar.current

    // MARK: - Initialization

    public init() {}

    // MARK: - Streak Methods

    public func validateStreaks() async throws -> StreakValidationResult {
        var validatedCount = 0
        var brokenCount = 0

        for (_, state) in streakStates {
            validatedCount += 1
            if state.isBroken {
                brokenCount += 1
            }
        }

        return StreakValidationResult(
            success: true,
            error: nil,
            validatedAffirmations: validatedCount,
            brokenStreaks: brokenCount
        )
    }

    public func getStreakStatistics() async throws -> StreakStatistics {
        var stats = StreakStatistics()

        for (_, state) in streakStates {
            stats.totalAffirmations += 1
            if state.hasActiveCycle {
                stats.activeStreaks += 1
            }
            stats.completedCycles += state.completedCycles
            stats.longestCurrentStreak = max(stats.longestCurrentStreak, state.currentStreak)
            if state.currentStreak > stats.longestCurrentStreak {
                stats.longestStreakAffirmationId = state.affirmationId
            }
        }

        if !streakStates.isEmpty {
            let totalStreaks = streakStates.values.map { $0.currentStreak }
            stats.averageStreakLength =
                Double(totalStreaks.reduce(0, +)) / Double(totalStreaks.count)
        }

        return stats
    }

    public func getStreakCalendarData(for affirmation: any AffirmationProtocol, numberOfDays: Int)
        async -> [StreakCalendarDay]
    {
        let state = getOrCreateState(for: affirmation)
        let today = calendar.startOfDay(for: Date())

        return (0..<numberOfDays).map { dayOffset in
            guard let date = calendar.date(byAdding: .day, value: -dayOffset, to: today) else {
                return StreakCalendarDay(
                    date: today,
                    repetitions: 0,
                    isComplete: false,
                    progress: 0.0
                )
            }

            // Generate realistic calendar data
            let repetitions = generateRepetitions(for: date, state: state)
            let isComplete = repetitions >= state.dailyQuota
            let progress = Double(repetitions) / Double(state.dailyQuota)

            return StreakCalendarDay(
                date: date,
                repetitions: repetitions,
                isComplete: isComplete,
                progress: progress
            )
        }
    }

    public func isStreakAtRisk(for affirmation: any AffirmationProtocol) async -> Bool {
        let state = getOrCreateState(for: affirmation)
        guard let lastRepetition = state.lastRepetitionDate else {
            return false
        }

        let riskThreshold: TimeInterval = 86400 * 1.5  // 1.5 days
        return Date().timeIntervalSince(lastRepetition) > riskThreshold
    }

    public func updateStreak(for affirmation: any AffirmationProtocol) async throws {
        let state = getOrCreateState(for: affirmation)
        state.lastRepetitionDate = Date()

        if state.isBroken {
            state.currentStreak = 0
            state.isBroken = false
        }
    }

    public func getStreakInfo(for affirmation: any AffirmationProtocol) async -> StreakInfo {
        let state = getOrCreateState(for: affirmation)

        return StreakInfo(
            currentStreak: state.currentStreak,
            longestStreak: state.longestStreak,
            completedCycles: state.completedCycles,
            hasActiveCycle: state.hasActiveCycle,
            cycleStartDate: state.cycleStartDate,
            lastRepetitionDate: state.lastRepetitionDate
        )
    }

    public func getStreakCalendar(for affirmation: any AffirmationProtocol) async
        -> [StreakCalendarDay]
    {
        return await getStreakCalendarData(for: affirmation, numberOfDays: 30)
    }

    public func validateStreak(for affirmation: any AffirmationProtocol) async -> Bool {
        let state = getOrCreateState(for: affirmation)
        return !state.isBroken
    }

    public func resetStreak(for affirmation: any AffirmationProtocol) async throws {
        let state = getOrCreateState(for: affirmation)
        state.currentStreak = 0
        state.isBroken = false
        state.lastRepetitionDate = nil
    }

    // MARK: - Private Methods

    private func getOrCreateState(for affirmation: any AffirmationProtocol) -> StreakState {
        if let existing = streakStates[affirmation.id] {
            return existing
        }

        let newState = StreakState(
            affirmationId: affirmation.id,
            dailyQuota: 5,
            currentStreak: Int.random(in: 1...10),
            longestStreak: Int.random(in: 10...30),
            completedCycles: Int.random(in: 1...5),
            hasActiveCycle: Bool.random(),
            cycleStartDate: Date().addingTimeInterval(-Double.random(in: 0...86400 * 7)),
            lastRepetitionDate: Date().addingTimeInterval(-Double.random(in: 0...86400)),
            isBroken: false
        )

        streakStates[affirmation.id] = newState
        return newState
    }

    private func generateRepetitions(for date: Date, state: StreakState) -> Int {
        // Generate realistic repetition data based on streak state
        let baseRepetitions = state.currentStreak > 0 ? state.dailyQuota : 0
        let randomFactor = Double.random(in: 0.7...1.3)
        let repetitions = Int(Double(baseRepetitions) * randomFactor)

        // Ensure we don't exceed the daily quota
        return min(repetitions, state.dailyQuota)
    }
}

// MARK: - Supporting Types

private class StreakState {
    let affirmationId: UUID
    let dailyQuota: Int
    var currentStreak: Int
    var longestStreak: Int
    var completedCycles: Int
    var hasActiveCycle: Bool
    var cycleStartDate: Date?
    var lastRepetitionDate: Date?
    var isBroken: Bool

    init(
        affirmationId: UUID,
        dailyQuota: Int,
        currentStreak: Int,
        longestStreak: Int,
        completedCycles: Int,
        hasActiveCycle: Bool,
        cycleStartDate: Date?,
        lastRepetitionDate: Date?,
        isBroken: Bool
    ) {
        self.affirmationId = affirmationId
        self.dailyQuota = dailyQuota
        self.currentStreak = currentStreak
        self.longestStreak = longestStreak
        self.completedCycles = completedCycles
        self.hasActiveCycle = hasActiveCycle
        self.cycleStartDate = cycleStartDate
        self.lastRepetitionDate = lastRepetitionDate
        self.isBroken = isBroken
    }
}

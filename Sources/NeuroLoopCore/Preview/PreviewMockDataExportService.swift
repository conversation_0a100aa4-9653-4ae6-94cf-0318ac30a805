import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

@MainActor
public final class PreviewMockDataExportService: DataExportServiceProtocol {
    public init() {}
    
    public func exportData() async throws -> URL {
        // Return a temporary URL for previews
        return URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("preview_export")
    }
    
    public func deleteAllData() async throws {
        // No-op in preview
    }
} 
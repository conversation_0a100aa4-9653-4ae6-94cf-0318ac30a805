import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

@MainActor
public final class PreviewMockAffirmationService: @unchecked Sendable, AffirmationServiceProtocol {
    private var affirmations: [any AffirmationProtocol] = [
        AffirmationStub(
            text: "I am capable of achieving great things",
            category: AffirmationCategory.confidence,
            recordingURL: nil,
            isFavorite: true
        ),
        AffirmationStub(
            text: "I am worthy of love and respect",
            category: AffirmationCategory.selfLove,
            recordingURL: nil,
            isFavorite: false
        ),
        AffirmationStub(
            text: "I am in control of my thoughts and emotions",
            category: AffirmationCategory.mindfulness,
            recordingURL: nil,
            isFavorite: true
        ),
    ]

    public init() {}

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        return affirmations
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        return affirmations.first { $0.id == id }
    }

    public func createAffirmation(
        text: String, category: AffirmationCategory, recordingURL: URL? = nil
    ) async throws -> any AffirmationProtocol {
        let affirmation = AffirmationStub(
            text: text, category: category, recordingURL: recordingURL)
        affirmations.append(affirmation)
        return affirmation
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            affirmations[index] = affirmation
        }
    }

    public func deleteAffirmation(id: UUID) async throws {
        affirmations.removeAll { $0.id == id }
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        return affirmations.filter { $0.category == category }
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        return affirmations.filter { $0.isFavorite }
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        return affirmations.first { $0.hasActiveCycle }
    }

    public func toggleFavorite(_ affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let updatedAffirmation = affirmation
            try updatedAffirmation.recordRepetition()
            affirmations[index] = updatedAffirmation
            return updatedAffirmation
        }
        return affirmation
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let updatedAffirmation = affirmation
            try updatedAffirmation.recordRepetition()
            affirmations[index] = updatedAffirmation
        }
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let updatedAffirmation = affirmation
            try updatedAffirmation.recordRepetition()
            affirmations[index] = updatedAffirmation
            return updatedAffirmation
        }
        return affirmation
    }

    public func updateEnergyLevel(_ level: Double, for affirmation: any AffirmationProtocol)
        async throws -> any AffirmationProtocol
    {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let updatedAffirmation = affirmation
            updatedAffirmation.updateEnergyLevel(level)
            affirmations[index] = updatedAffirmation
            return updatedAffirmation
        }
        return affirmation
    }

    public func recordMood(_ rating: Int, notes: String?, for affirmation: any AffirmationProtocol)
        async throws -> any AffirmationProtocol
    {
        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let updatedAffirmation = affirmation
            updatedAffirmation.recordMood(rating, notes: notes)
            affirmations[index] = updatedAffirmation
            return updatedAffirmation
        }
        return affirmation
    }

    public func getStatistics() async throws -> AffirmationStatistics {
        var statistics = AffirmationStatistics()
        statistics.totalAffirmations = affirmations.count
        statistics.activeAffirmations = affirmations.filter { $0.hasActiveCycle }.count
        statistics.favoriteAffirmations = affirmations.filter { $0.isFavorite }.count
        var categoryCounts: [AffirmationCategory: Int] = [:]
        for affirmation in affirmations {
            categoryCounts[affirmation.category, default: 0] += 1
        }
        statistics.categoryDistribution = categoryCounts
        statistics.totalCompletedCycles = affirmations.reduce(0) { $0 + $1.completedCycles }
        statistics.totalRepetitions = affirmations.reduce(0) { $0 + $1.currentRepetitions }
        return statistics
    }
}

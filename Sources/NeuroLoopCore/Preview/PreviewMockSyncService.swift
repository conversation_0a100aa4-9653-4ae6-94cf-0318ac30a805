import Combine
import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

public final class PreviewMockSyncService: @unchecked Sendable, SyncServiceProtocol {
    private let _syncStatus = CurrentValueSubject<SyncStatus, Never>(.idle)
    public var syncStatus: SyncStatus { _syncStatus.value }
    public var syncStatusPublisher: AnyPublisher<SyncStatus, Never> {
        _syncStatus.eraseToAnyPublisher()
    }
    public private(set) var lastSyncError: Error?
    public private(set) var lastSyncDate: Date?
    public private(set) var isAutomaticSyncEnabled: Bool = false

    public init() {}

    public func syncNow() async throws {
        _syncStatus.send(.inProgress)
        try await Task.sleep(nanoseconds: 1_000_000_000)  // 1 second
        lastSyncDate = Date()
        _syncStatus.send(.success)
    }

    public func setAutomaticSyncEnabled(_ enabled: Bool) async {
        isAutomaticSyncEnabled = enabled
    }
}

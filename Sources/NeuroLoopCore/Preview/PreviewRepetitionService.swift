import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/**
 * PreviewRepetitionService
 *
 * A preview implementation of RepetitionServiceProtocol for use in SwiftUI previews and testing.
 * This implementation provides realistic simulation of affirmation repetition tracking and cycle management.
 *
 * ## Usage Example
 * ```
 * let service = PreviewServiceFactory.shared.getRepetitionService()
 * let viewModel = RepetitionTrackingViewModel(repetitionService: service)
 * ```
 *
 * ## Preview Data
 * This implementation provides the following test data:
 * - Simulated repetition tracking with realistic progress
 * - Cycle management with various states (active, broken, complete)
 * - Streak tracking with realistic progression
 * - Time-based restrictions for repetitions
 *
 * ## Actor Isolation
 * This implementation is isolated to MainActor and uses @unchecked Sendable for preview purposes.
 */

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewRepetitionService: RepetitionServiceProtocol, @unchecked Sendable {
    // MARK: - Private Properties

    private let affirmationService: AffirmationServiceProtocol
    private var repetitionStates: [UUID: RepetitionState] = [:]

    // MARK: - Initialization

    public init(affirmationService: AffirmationServiceProtocol) {
        self.affirmationService = affirmationService
    }

    // MARK: - Repetition Methods

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> RepetitionResult
    {
        // Get the current state
        let state = getOrCreateState(for: affirmation)

        // Store the previous count for logging
        let previousCount = state.currentRepetitions

        // Increment by exactly 1
        state.currentRepetitions += 1
        state.lastRepetitionDate = Date()

        print("[PreviewRepetitionService] Incremented repetitions from \(previousCount) to: \(state.currentRepetitions)")
        print("[PreviewRepetitionService] VERIFICATION - Increment amount: \(state.currentRepetitions - previousCount)")

        let isQuotaMet = state.currentRepetitions >= state.dailyQuota
        let isCycleComplete = state.currentDay >= state.totalDays && isQuotaMet

        if isCycleComplete {
            state.completedCycles += 1
            state.currentStreak += 1
            state.longestStreak = max(state.longestStreak, state.currentStreak)
            state.hasActiveCycle = false
        }

        // Save the updated state
        repetitionStates[affirmation.id] = state

        // Create a modified affirmation with the updated repetition count
        // This ensures the UI gets the correct count
        let updatedAffirmation = AffirmationStub(
            id: affirmation.id,
            text: affirmation.text,
            category: affirmation.category,
            recordingURL: affirmation.recordingURL,
            createdAt: affirmation.createdAt,
            updatedAt: Date(),
            currentCycleDay: affirmation.currentCycleDay,
            cycleStartDate: affirmation.cycleStartDate,
            completedCycles: affirmation.completedCycles,
            currentRepetitions: state.currentRepetitions, // Use the updated count
            dailyProgress: affirmation.dailyProgress,
            lastRepetitionDate: Date(),
            energyLevel: affirmation.energyLevel,
            moodRating: affirmation.moodRating,
            notes: affirmation.notes,
            isFavorite: affirmation.isFavorite,
            playCount: affirmation.playCount,
            hasActiveCycle: affirmation.hasActiveCycle,
            isCurrentCycleComplete: isCycleComplete,
            todayProgress: Double(state.currentRepetitions) / 100.0, // Use exact 100.0 for consistent conversion
            cycleProgress: affirmation.cycleProgress,
            hasTodayQuotaMet: isQuotaMet
        )

        return RepetitionResult(
            success: true,
            updatedAffirmation: updatedAffirmation,
            isQuotaMet: isQuotaMet,
            isCycleComplete: isCycleComplete
        )
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        let state = getOrCreateState(for: affirmation)
        state.hasActiveCycle = true
        state.cycleStartDate = Date()
        state.currentDay = 1
        state.currentRepetitions = 0

        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }

    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return try await startCycle(for: affirmation)
    }

    // --- Nonisolated protocol stubs for Swift 6 ---
    public nonisolated func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        MainActor.assumeIsolated { self._getProgress(for: affirmation) }
    }
    private func _getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        let state = getOrCreateState(for: affirmation)

        // Log the current repetitions to help with debugging
        print("[PreviewRepetitionService] Getting progress - currentRepetitions: \(state.currentRepetitions)")

        return ProgressInfo(
            todayProgress: Double(state.currentRepetitions) / 100.0, // Use exact 100.0 for consistent conversion
            cycleProgress: Double(state.currentDay) / Double(state.totalDays),
            currentDay: state.currentDay,
            totalDays: state.totalDays,
            currentRepetitions: state.currentRepetitions,
            totalRepetitions: state.dailyQuota * state.totalDays,
            hasTodayQuotaMet: state.currentRepetitions >= state.dailyQuota,
            isCycleComplete: state.currentDay >= state.totalDays
                && state.currentRepetitions >= state.dailyQuota,
            hasActiveCycle: state.hasActiveCycle
        )
    }

    public nonisolated func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        MainActor.assumeIsolated { self._getStreakInfo(for: affirmation) }
    }
    private func _getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        let state = getOrCreateState(for: affirmation)
        return StreakInfo(
            currentStreak: state.currentStreak,
            longestStreak: state.longestStreak,
            completedCycles: state.completedCycles,
            hasActiveCycle: state.hasActiveCycle,
            cycleStartDate: state.cycleStartDate,
            lastRepetitionDate: state.lastRepetitionDate
        )
    }

    public nonisolated func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        MainActor.assumeIsolated { self._canPerformRepetition(for: affirmation) }
    }
    private func _canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        guard let timeUntilNext = _timeUntilNextRepetition(for: affirmation) else {
            return true
        }
        return timeUntilNext <= 0
    }

    public nonisolated func timeUntilNextRepetition(for affirmation: any AffirmationProtocol)
        -> TimeInterval?
    {
        MainActor.assumeIsolated { self._timeUntilNextRepetition(for: affirmation) }
    }
    private func _timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval?
    {
        let state = getOrCreateState(for: affirmation)
        guard let lastRepetition = state.lastRepetitionDate else {
            return nil
        }
        let minimumInterval: TimeInterval = 3600  // 1 hour
        let timeSinceLastRepetition = Date().timeIntervalSince(lastRepetition)
        let timeUntilNext = minimumInterval - timeSinceLastRepetition
        return timeUntilNext > 0 ? timeUntilNext : nil
    }

    public nonisolated func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        MainActor.assumeIsolated { self._isCycleBroken(for: affirmation) }
    }
    private func _isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        let state = getOrCreateState(for: affirmation)
        guard let lastRepetition = state.lastRepetitionDate else {
            return false
        }
        let maxBreakTime: TimeInterval = 86400 * 2  // 2 days
        return Date().timeIntervalSince(lastRepetition) > maxBreakTime
    }

    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws
        -> CycleResult
    {
        let state = getOrCreateState(for: affirmation)
        state.currentStreak = 0
        state.hasActiveCycle = false

        return try await startCycle(for: affirmation)
    }

    // MARK: - Private Methods

    private func getOrCreateState(for affirmation: any AffirmationProtocol) -> RepetitionState {
        if let existing = repetitionStates[affirmation.id] {
            print("[PreviewRepetitionService] Using existing state for affirmation \(affirmation.id) - currentRepetitions: \(existing.currentRepetitions)")
            return existing
        }

        // Use the affirmation's current repetitions if available, otherwise start at 0
        let startingRepetitions = affirmation.currentRepetitions
        print("[PreviewRepetitionService] Creating new state for affirmation \(affirmation.id) - starting with repetitions: \(startingRepetitions)")

        let newState = RepetitionState(
            dailyQuota: 100, // Set to 100 to match AffirmationConstants.DAILY_REPETITIONS
            totalDays: 7,
            currentDay: 1,
            currentRepetitions: startingRepetitions,
            currentStreak: 0,
            longestStreak: 0,
            completedCycles: 0,
            hasActiveCycle: true,
            cycleStartDate: Date(),
            lastRepetitionDate: nil
        )

        repetitionStates[affirmation.id] = newState
        return newState
    }
}

// MARK: - Supporting Types

private class RepetitionState {
    let dailyQuota: Int
    let totalDays: Int
    var currentDay: Int
    var currentRepetitions: Int
    var currentStreak: Int
    var longestStreak: Int
    var completedCycles: Int
    var hasActiveCycle: Bool
    var cycleStartDate: Date?
    var lastRepetitionDate: Date?

    init(
        dailyQuota: Int,
        totalDays: Int,
        currentDay: Int,
        currentRepetitions: Int,
        currentStreak: Int,
        longestStreak: Int,
        completedCycles: Int,
        hasActiveCycle: Bool,
        cycleStartDate: Date?,
        lastRepetitionDate: Date?
    ) {
        self.dailyQuota = dailyQuota
        self.totalDays = totalDays
        self.currentDay = currentDay
        self.currentRepetitions = currentRepetitions
        self.currentStreak = currentStreak
        self.longestStreak = longestStreak
        self.completedCycles = completedCycles
        self.hasActiveCycle = hasActiveCycle
        self.cycleStartDate = cycleStartDate
        self.lastRepetitionDate = lastRepetitionDate
    }
}

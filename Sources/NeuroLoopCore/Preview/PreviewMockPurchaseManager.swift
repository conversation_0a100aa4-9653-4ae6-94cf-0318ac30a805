import NeuroLoopInterfaces
import NeuroLoopTypes

@MainActor
public final class PreviewMockPurchaseManager: PurchaseManagerSendable {
    public var isPremiumUser: Bool = false

    public init() {}

    public func purchasePremium() async throws {
        isPremiumUser = true
    }

    public func restorePurchases() async throws {
        isPremiumUser = true
    }

    public func checkPurchaseStatus() async throws {
        // No-op in preview
    }
}

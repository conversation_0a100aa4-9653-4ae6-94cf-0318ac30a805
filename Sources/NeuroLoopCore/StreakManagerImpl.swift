import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels

/// Implementation of the StreakManaging protocol
@available(macOS 10.15, iOS 13.0, *)
@MainActor
public class StreakManagerImpl: StreakManaging {
    // MARK: - Properties
    private var streaks: [UUID: Int] = [:]
    private let requiredIterationsPerDay = 100
    private let requiredStreakDays = 7

    // MARK: - Initialization
    public init() {}

    // MARK: - StreakManaging
    public func getCurrentStreak(for affirmation: AffirmationProtocol) -> Int {
        return streaks[affirmation.id] ?? 0
    }

    public func updateStreak(for affirmation: AffirmationProtocol, count: Int) -> Int {
        let currentStreak = getCurrentStreak(for: affirmation)
        let newStreak = count >= requiredIterationsPerDay ? currentStreak + 1 : currentStreak
        streaks[affirmation.id] = newStreak
        return newStreak
    }

    public func resetStreak(for affirmation: AffirmationProtocol) {
        streaks[affirmation.id] = 0
    }

    public func isStreakComplete(for affirmation: AffirmationProtocol) -> Bool {
        return getCurrentStreak(for: affirmation) >= requiredStreakDays
    }
}

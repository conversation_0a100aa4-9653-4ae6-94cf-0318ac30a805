/*
import Foundation
import NeuroLoopInterfaces

/// In-memory implementation of the AffirmationRepositoryProtocol
public class InMemoryAffirmationRepository: AffirmationRepositoryProtocol {
    private var affirmations: [LegacyAffirmation] = []

    public init() {
        // Initialize with some sample data
        let sampleAffirmations = LegacyAffirmation.sampleAffirmations
        self.affirmations = sampleAffirmations
    }

    // Internal initializer with affirmations
    init(affirmations: [LegacyAffirmation]) {
        self.affirmations = affirmations
    }

    // MARK: - AffirmationRepositoryProtocol

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        return affirmations
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        return affirmations.first { $0.id == id }
    }

    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?) async throws -> any AffirmationProtocol {
        let newAffirmation = LegacyAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )
        affirmations.append(newAffirmation)
        return newAffirmation
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        guard let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) else {
            throw AffirmationError.notFound
        }

        guard let legacyAffirmation = affirmation as? LegacyAffirmation else {
            throw AffirmationError.invalidData
        }

        affirmations[index] = legacyAffirmation
    }

    public func deleteAffirmation(id: UUID) async throws {
        guard affirmations.contains(where: { $0.id == id }) else {
            throw AffirmationError.notFound
        }

        affirmations.removeAll { $0.id == id }
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws -> [any AffirmationProtocol] {
        return affirmations.filter { $0.category == category }
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        return affirmations.filter { $0.isFavorite }
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        // First try to find an affirmation with an active cycle
        if let active = affirmations.first(where: { $0.hasActiveCycle }) {
            return active
        }

        // If no active cycle, return the first affirmation or nil if empty
        return affirmations.first
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws {
        guard let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) else {
            throw AffirmationError.notFound
        }

        // Create a mutable copy
        var mutableAffirmation = affirmations[index]

        // Record the repetition
        mutableAffirmation.currentRepetitions += 1

        // Update the affirmation in the array
        affirmations[index] = mutableAffirmation
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
        guard let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) else {
            throw AffirmationError.notFound
        }

        // Start a new cycle for this affirmation
        var mutableAffirmation = affirmations[index]
        mutableAffirmation.currentCycleDay = 1
        mutableAffirmation.currentRepetitions = 0
        mutableAffirmation.cycleStartDate = Date()

        // Update the affirmation in the array
        affirmations[index] = mutableAffirmation
    }
}

// MARK: - Helper Types

// Legacy implementation of AffirmationProtocol for in-memory storage
class LegacyAffirmation: AffirmationProtocol, Equatable {
    static func == (lhs: LegacyAffirmation, rhs: LegacyAffirmation) -> Bool {
        lhs.id == rhs.id
    }

    let id: UUID
    var text: String
    let category: AffirmationCategory
    var isFavorite: Bool
    var recordingURL: URL?
    let createdAt: Date
    var updatedAt: Date

    // Cycle tracking
    var currentCycleDay: Int
    var currentRepetitions: Int
    var cycleStartDate: Date?
    var completedCycles: Int
    var totalRepetitions: Int

    // Additional properties required by AffirmationProtocol
    var dailyProgress: [Date: Int] = [:]
    var lastRepetitionDate: Date?
    var energyLevel: Double = 0.0
    var moodRating: Int?
    var notes: String?
    var playCount: Int = 0
    var hasActiveCycle: Bool = false

    var isCurrentCycleComplete: Bool {
        return currentRepetitions >= 100
    }

    var todayProgress: Double {
        let targetDailyRepetitions = 15 // Assuming 15 repetitions per day is the target
        return min(Double(currentRepetitions) / Double(targetDailyRepetitions), 1.0)
    }

    var cycleProgress: Double {
        return min(Double(currentRepetitions) / 100.0, 1.0)
    }

    var hasTodayQuotaMet: Bool {
        return currentRepetitions >= 15 // Assuming 15 repetitions per day is the quota
    }

    var canPerformRepetition: Bool {
        return true // For simplicity, always allow repetitions
    }

    init(
        id: UUID = UUID(),
        text: String,
        category: AffirmationCategory,
        isFavorite: Bool = false,
        recordingURL: URL? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        currentCycleDay: Int = 0,
        currentRepetitions: Int = 0,
        cycleStartDate: Date? = nil,
        completedCycles: Int = 0,
        totalRepetitions: Int = 0,
        dailyProgress: [Date: Int] = [:],
        lastRepetitionDate: Date? = nil,
        energyLevel: Double = 0.0,
        moodRating: Int? = nil,
        notes: String? = nil,
        playCount: Int = 0,
        hasActiveCycle: Bool = false
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.isFavorite = isFavorite
        self.recordingURL = recordingURL
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.currentCycleDay = currentCycleDay
        self.currentRepetitions = currentRepetitions
        self.cycleStartDate = cycleStartDate
        self.completedCycles = completedCycles
        self.totalRepetitions = totalRepetitions
        self.dailyProgress = dailyProgress
        self.lastRepetitionDate = lastRepetitionDate
        self.energyLevel = energyLevel
        self.moodRating = moodRating
        self.notes = notes
        self.playCount = playCount
        self.hasActiveCycle = hasActiveCycle
    }

    // Required methods from AffirmationProtocol
    func recordRepetition() throws {
        currentRepetitions += 1
        totalRepetitions += 1
        lastRepetitionDate = Date()

        // Update daily progress
        let today = Calendar.current.startOfDay(for: Date())
        dailyProgress[today, default: 0] += 1

        // Update cycle status
        if currentRepetitions >= 100 {
            completedCycles += 1
            hasActiveCycle = false
        }
    }

    func updateEnergyLevel(_ level: Double) {
        energyLevel = level
    }

    func recordMood(_ rating: Int, notes: String?) {
        moodRating = rating
        self.notes = notes
    }

    // Sample data for testing
    static var sampleAffirmations: [LegacyAffirmation] {
        [
            LegacyAffirmation(
                text: "I am capable of achieving my goals",
                category: .confidence,
                isFavorite: true,
                currentCycleDay: 3,
                currentRepetitions: 42,
                cycleStartDate: Calendar.current.date(byAdding: .day, value: -3, to: Date()),
                completedCycles: 2,
                totalRepetitions: 242,
                hasActiveCycle: true
            ),
            LegacyAffirmation(
                text: "I embrace the present moment",
                category: .mindfulness,
                currentCycleDay: 0,
                completedCycles: 1,
                totalRepetitions: 100
            ),
            LegacyAffirmation(
                text: "I am grateful for all the abundance in my life",
                category: .gratitude
            ),
            LegacyAffirmation(
                text: "My body is healthy and strong",
                category: .health,
                isFavorite: true
            ),
            LegacyAffirmation(
                text: "I attract success and prosperity",
                category: .success
            )
        ]
    }
}
*/

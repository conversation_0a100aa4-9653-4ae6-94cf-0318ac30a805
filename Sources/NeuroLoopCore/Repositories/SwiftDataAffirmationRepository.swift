/*
import Foundation
import SwiftData
import NeuroLoopInterfaces
import NeuroLoopModels

// Using AffirmationError from NeuroLoopInterfaces directly

/// SwiftData implementation of the AffirmationRepositoryProtocol
public class SwiftDataAffirmationRepository: AffirmationRepositoryProtocol {
    // MARK: - Properties

    private let modelContainer: ModelContainer
    private let modelContext: ModelContext

    // MARK: - Initialization

    public init() throws {
        let schema = Schema([
            Affirmation.self,
            RepetitionCycle.self
        ])

        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            self.modelContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
            self.modelContext = ModelContext(modelContainer)
        } catch {
            print("Failed to create model container: \(error)")
            throw error
        }
    }

    // For testing purposes
    init(modelContainer: ModelContainer) {
        self.modelContainer = modelContainer
        self.modelContext = ModelContext(modelContainer)
    }

    // MARK: - AffirmationRepositoryProtocol

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        let start = Date()
        let descriptor = FetchDescriptor<Affirmation>(sortBy: [SortDescriptor(\.updatedAt, order: .reverse)])
        do {
            let affirmations = try modelContext.fetch(descriptor)
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchAffirmations: \(elapsed)s")
            return affirmations
        } catch {
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchAffirmations (error): \(elapsed)s")
            throw error
        }
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        let start = Date()
        let predicate = #Predicate<Affirmation> { $0.id == id }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate)
        do {
            let affirmations = try modelContext.fetch(descriptor)
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchAffirmation: \(elapsed)s")
            return affirmations.first
        } catch {
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchAffirmation (error): \(elapsed)s")
            throw error
        }
    }

    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?) async throws -> any AffirmationProtocol {
        let start = Date()
        let newAffirmation = Affirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )
        modelContext.insert(newAffirmation)
        do {
            try modelContext.save()
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.createAffirmation: \(elapsed)s")
            return newAffirmation
        } catch {
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.createAffirmation (error): \(elapsed)s")
            throw error
        }
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        let start = Date()
        guard let affirmation = affirmation as? Affirmation else {
            throw AffirmationError.invalidData
        }
        affirmation.updatedAt = Date()
        do {
            try modelContext.save()
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.updateAffirmation: \(elapsed)s")
        } catch {
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.updateAffirmation (error): \(elapsed)s")
            throw error
        }
    }

    public func deleteAffirmation(id: UUID) async throws {
        let start = Date()
        guard let affirmation = try await fetchAffirmation(id: id) as? Affirmation else {
            throw AffirmationError.notFound
        }
        modelContext.delete(affirmation)
        do {
            try modelContext.save()
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.deleteAffirmation: \(elapsed)s")
        } catch {
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.deleteAffirmation (error): \(elapsed)s")
            throw error
        }
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws -> [any AffirmationProtocol] {
        let start = Date()
        let predicate = #Predicate<Affirmation> { $0.categoryRawValue == category.rawValue }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate, sortBy: [SortDescriptor(\.updatedAt, order: .reverse)])
        do {
            let affirmations = try modelContext.fetch(descriptor)
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchAffirmationsByCategory: \(elapsed)s")
            return affirmations
        } catch {
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchAffirmationsByCategory (error): \(elapsed)s")
            throw error
        }
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        let start = Date()
        let predicate = #Predicate<Affirmation> { $0.isFavorite == true }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate, sortBy: [SortDescriptor(\.updatedAt, order: .reverse)])
        do {
            let affirmations = try modelContext.fetch(descriptor)
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchFavoriteAffirmations: \(elapsed)s")
            return affirmations
        } catch {
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchFavoriteAffirmations (error): \(elapsed)s")
            throw error
        }
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        let start = Date()
        let activePredicate = #Predicate<Affirmation> { $0.hasActiveCycle == true }
        let activeDescriptor = FetchDescriptor<Affirmation>(predicate: activePredicate, sortBy: [SortDescriptor(\.updatedAt, order: .reverse)])
        do {
            let activeAffirmations = try modelContext.fetch(activeDescriptor)
            if let active = activeAffirmations.first {
                let elapsed = Date().timeIntervalSince(start)
                print("[Performance] SwiftDataAffirmationRepository.fetchCurrentAffirmation (active): \(elapsed)s")
                return active
            }
            var descriptor = FetchDescriptor<Affirmation>(sortBy: [SortDescriptor(\.updatedAt, order: .reverse)])
            descriptor.fetchLimit = 1
            let affirmations = try modelContext.fetch(descriptor)
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchCurrentAffirmation (fallback): \(elapsed)s")
            return affirmations.first
        } catch {
            let elapsed = Date().timeIntervalSince(start)
            print("[Performance] SwiftDataAffirmationRepository.fetchCurrentAffirmation (error): \(elapsed)s")
            throw error
        }
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws {
        guard let affirmation = affirmation as? Affirmation else {
            throw AffirmationError.invalidData
        }

        do {
            try affirmation.recordRepetition()
            try modelContext.save()
        } catch {
            print("Failed to record repetition: \(error)")
            throw error
        }
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
        guard let affirmation = affirmation as? Affirmation else {
            throw AffirmationError.invalidData
        }

        affirmation.startNewCycle()

        do {
            try modelContext.save()
        } catch {
            print("Failed to start cycle: \(error)")
            throw error
        }
    }

    // MARK: - Helper Methods

    /// Validate all active cycles and deactivate any that have missed a day
    public func validateActiveCycles() async throws {
        let activePredicate = #Predicate<Affirmation> { $0.hasActiveCycle == true }
        let activeDescriptor = FetchDescriptor<Affirmation>(predicate: activePredicate)

        do {
            let activeAffirmations = try modelContext.fetch(activeDescriptor)

            for affirmation in activeAffirmations {
                if let cycle = affirmation.currentCycle {
                    if !cycle.validateCycle() {
                        affirmation.hasActiveCycle = false
                    }
                }
            }

            try modelContext.save()
        } catch {
            print("Failed to validate active cycles: \(error)")
            throw error
        }
    }

    /// Migrate data from legacy storage
    internal func migrateFromLegacy(_ legacyAffirmations: [LegacyAffirmation]) async throws {
        for legacyAffirmation in legacyAffirmations {
            let newAffirmation = Affirmation(
                id: legacyAffirmation.id,
                text: legacyAffirmation.text,
                category: legacyAffirmation.category,
                recordingURL: legacyAffirmation.recordingURL,
                createdAt: legacyAffirmation.createdAt,
                updatedAt: legacyAffirmation.updatedAt,
                completedCycles: legacyAffirmation.completedCycles,
                currentRepetitions: legacyAffirmation.currentRepetitions,
                lastRepetitionDate: legacyAffirmation.lastRepetitionDate,
                energyLevel: legacyAffirmation.energyLevel,
                moodRating: legacyAffirmation.moodRating,
                notes: legacyAffirmation.notes,
                isFavorite: legacyAffirmation.isFavorite,
                playCount: legacyAffirmation.playCount,
                hasActiveCycle: legacyAffirmation.hasActiveCycle
            )

            modelContext.insert(newAffirmation)

            // If there's an active cycle, create it
            if legacyAffirmation.hasActiveCycle, let cycleStartDate = legacyAffirmation.cycleStartDate {
                let cycle = RepetitionCycle(
                    startDate: cycleStartDate,
                    currentDay: legacyAffirmation.currentCycleDay,
                    isActive: true,
                    dailyProgress: legacyAffirmation.dailyProgress,
                    affirmation: newAffirmation
                )

                newAffirmation.cycles.append(cycle)
            }
        }

        try modelContext.save()
    }
*/


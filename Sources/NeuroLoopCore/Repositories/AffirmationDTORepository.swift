/*
import Foundation
import SwiftData
import NeuroLoopInterfaces
import NeuroLoopModels

// NOTE: This file is disabled due to Swift 6.1 and SwiftData incompatibility. All repository calls should use the in-memory stub.

public final class AffirmationDTORepository {
    private let modelContainer: ModelContainer
    private let modelContext: ModelContext

    public init() throws {
        // The @Model macro provides PersistentModel conformance for Affirmation and RepetitionCycle in Swift 6.1
        let schema = Schema([Affirmation.self, RepetitionCycle.self])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
        self.modelContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
        self.modelContext = ModelContext(modelContainer)
    }

    // For testing purposes
    init(modelContainer: ModelContainer) {
        self.modelContainer = modelContainer
        self.modelContext = ModelContext(modelContainer)
    }

    // MARK: - Fetch
    @MainActor
    public func fetchAll() async throws -> [AffirmationDTO] {
        // WARNING: Swift 6.1/SwiftData: ReferenceWritableKeyPath does not conform to Sendable. This is safe because all access is on the main actor.
        let descriptor = FetchDescriptor<Affirmation>(sortBy: [SortDescriptor(\Affirmation.updatedAt, order: .reverse)])
        let affirmations = try modelContext.fetch(descriptor)
        return affirmations.map { AffirmationDTO.from(model: $0) }
    }

    @MainActor
    public func fetchById(_ id: UUID) async throws -> AffirmationDTO? {
        let predicate = #Predicate<Affirmation> { affirmation in affirmation.id == id }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate)
        let affirmations = try modelContext.fetch(descriptor)
        return affirmations.first.map { AffirmationDTO.from(model: $0) }
    }

    @MainActor
    public func fetchByCategory(_ category: AffirmationCategory) async throws -> [AffirmationDTO] {
        // WARNING: Swift 6.1/SwiftData: ReferenceWritableKeyPath does not conform to Sendable. This is safe because all access is on the main actor.
        let predicate = #Predicate<Affirmation> { affirmation in affirmation.categoryRawValue == category.rawValue }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate, sortBy: [SortDescriptor(\Affirmation.updatedAt, order: .reverse)])
        let affirmations = try modelContext.fetch(descriptor)
        return affirmations.map { AffirmationDTO.from(model: $0) }
    }

    @MainActor
    public func fetchFavorites() async throws -> [AffirmationDTO] {
        // WARNING: Swift 6.1/SwiftData: ReferenceWritableKeyPath does not conform to Sendable. This is safe because all access is on the main actor.
        let predicate = #Predicate<Affirmation> { affirmation in affirmation.isFavorite == true }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate, sortBy: [SortDescriptor(\Affirmation.updatedAt, order: .reverse)])
        let affirmations = try modelContext.fetch(descriptor)
        return affirmations.map { AffirmationDTO.from(model: $0) }
    }

    @MainActor
    public func fetchCurrent() async throws -> AffirmationDTO? {
        // WARNING: Swift 6.1/SwiftData: ReferenceWritableKeyPath does not conform to Sendable. This is safe because all access is on the main actor.
        let activePredicate = #Predicate<Affirmation> { affirmation in affirmation.hasActiveCycle == true }
        let activeDescriptor = FetchDescriptor<Affirmation>(predicate: activePredicate, sortBy: [SortDescriptor(\Affirmation.updatedAt, order: .reverse)])
        let activeAffirmations = try modelContext.fetch(activeDescriptor)
        if let active = activeAffirmations.first {
            return AffirmationDTO.from(model: active)
        }
        var descriptor = FetchDescriptor<Affirmation>(sortBy: [SortDescriptor(\Affirmation.updatedAt, order: .reverse)])
        descriptor.fetchLimit = 1
        let affirmations = try modelContext.fetch(descriptor)
        return affirmations.first.map { AffirmationDTO.from(model: $0) }
    }

    // MARK: - Create
    @MainActor
    public func create(text: String, category: AffirmationCategory, recordingURL: URL?) async throws -> AffirmationDTO {
        let newAffirmation = Affirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )
        modelContext.insert(newAffirmation)
        try modelContext.save()
        return AffirmationDTO.from(model: newAffirmation)
    }

    // MARK: - Update
    @MainActor
    public func update(_ dto: AffirmationDTO) async throws -> AffirmationDTO {
        // This method is not available when using the in-memory stub
        throw AffirmationError.notSupported
    }

    // MARK: - Delete
    @MainActor
    public func delete(id: UUID) async throws {
        let predicate = #Predicate<Affirmation> { affirmation in affirmation.id == id }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate)
        guard let affirmation = try modelContext.fetch(descriptor).first else {
            throw AffirmationError.notFound
        }
        modelContext.delete(affirmation)
        try modelContext.save()
    }

    // MARK: - Mutating Operations
    @MainActor
    public func recordRepetition(for id: UUID) async throws -> AffirmationDTO {
        let predicate = #Predicate<Affirmation> { affirmation in affirmation.id == id }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate)
        guard let affirmation = try modelContext.fetch(descriptor).first else {
            throw AffirmationError.notFound
        }
        try affirmation.recordRepetition()
        try modelContext.save()
        return AffirmationDTO.from(model: affirmation)
    }

    @MainActor
    public func updateEnergyLevel(for id: UUID, level: Double) async throws -> AffirmationDTO {
        let predicate = #Predicate<Affirmation> { affirmation in affirmation.id == id }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate)
        guard let affirmation = try modelContext.fetch(descriptor).first else {
            throw AffirmationError.notFound
        }
        affirmation.updateEnergyLevel(level)
        try modelContext.save()
        return AffirmationDTO.from(model: affirmation)
    }

    @MainActor
    public func recordMood(for id: UUID, rating: Int, notes: String?) async throws -> AffirmationDTO {
        let predicate = #Predicate<Affirmation> { affirmation in affirmation.id == id }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate)
        guard let affirmation = try modelContext.fetch(descriptor).first else {
            throw AffirmationError.notFound
        }
        affirmation.recordMood(rating, notes: notes)
        try modelContext.save()
        return AffirmationDTO.from(model: affirmation)
    }

    // MARK: - Cycle Operations
    @MainActor
    public func startCycle(for id: UUID) async throws -> AffirmationDTO {
        let predicate = #Predicate<Affirmation> { affirmation in affirmation.id == id }
        let descriptor = FetchDescriptor<Affirmation>(predicate: predicate)
        guard let affirmation = try modelContext.fetch(descriptor).first else {
            throw AffirmationError.notFound
        }
        affirmation.startNewCycle()
        try modelContext.save()
        return AffirmationDTO.from(model: affirmation)
    }

    @MainActor
    public func validateActiveCycles() async throws -> [AffirmationDTO] {
        let activePredicate = #Predicate<Affirmation> { affirmation in affirmation.hasActiveCycle == true }
        let activeDescriptor = FetchDescriptor<Affirmation>(predicate: activePredicate)
        let activeAffirmations = try modelContext.fetch(activeDescriptor)
        var updatedDTOs: [AffirmationDTO] = []
        for affirmation in activeAffirmations {
            if let cycle = affirmation.currentCycle {
                if !cycle.validateCycle() {
                    affirmation.hasActiveCycle = false
                }
            }
            updatedDTOs.append(AffirmationDTO.from(model: affirmation))
        }
        try modelContext.save()
        return updatedDTOs
    }
} 
*/ 
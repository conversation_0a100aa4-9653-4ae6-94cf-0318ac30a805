/*
import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels

/// Manager for handling data migration between different storage implementations
public class DataMigrationManager {
    // MARK: - Properties

    private let legacyRepository: AffirmationRepositoryProtocol
    private let swiftDataRepository: SwiftDataAffirmationRepository

    // MARK: - Initialization

    public init(
        legacyRepository: AffirmationRepositoryProtocol,
        swiftDataRepository: SwiftDataAffirmationRepository
    ) {
        self.legacyRepository = legacyRepository
        self.swiftDataRepository = swiftDataRepository
    }

    // MARK: - Public Methods

    /// Migrate data from legacy repository to SwiftData repository
    public func migrateData() async throws -> MigrationResult {
        let startTime = Date()
        var result = MigrationResult()

        do {
            // Fetch all affirmations from legacy repository
            let legacyAffirmations = try await legacyRepository.fetchAffirmations()
            result.totalItems = legacyAffirmations.count

            // Convert legacy affirmations to the format expected by SwiftDataAffirmationRepository
            var convertedAffirmations: [LegacyAffirmation] = []

            for affirmation in legacyAffirmations {
                if let legacyAffirmation = affirmation as? LegacyAffirmation {
                    convertedAffirmations.append(legacyAffirmation)
                    result.successfulItems += 1
                } else {
                    // Gather all async properties first
                    let id = await affirmation.id
                    let text = await affirmation.text
                    let category = await affirmation.category
                    let isFavorite = await affirmation.isFavorite
                    let recordingURL = await affirmation.recordingURL
                    let createdAt = await affirmation.createdAt
                    let updatedAt = await affirmation.updatedAt
                    let currentCycleDay = await affirmation.currentCycleDay
                    let currentRepetitions = await affirmation.currentRepetitions
                    let cycleStartDate = await affirmation.cycleStartDate
                    let completedCycles = await affirmation.completedCycles
                    let totalRepetitions = await affirmation.currentRepetitions
                    let dailyProgress = await affirmation.dailyProgress
                    let lastRepetitionDate = await affirmation.lastRepetitionDate
                    let energyLevel = await affirmation.energyLevel
                    let moodRating = await affirmation.moodRating
                    let notes = await affirmation.notes
                    let playCount = await affirmation.playCount
                    let hasActiveCycle = await affirmation.hasActiveCycle

                    let legacyAffirmation = LegacyAffirmation(
                        id: id,
                        text: text,
                        category: category,
                        isFavorite: isFavorite,
                        recordingURL: recordingURL,
                        createdAt: createdAt,
                        updatedAt: updatedAt,
                        currentCycleDay: currentCycleDay,
                        currentRepetitions: currentRepetitions,
                        cycleStartDate: cycleStartDate,
                        completedCycles: completedCycles,
                        totalRepetitions: totalRepetitions,
                        dailyProgress: dailyProgress,
                        lastRepetitionDate: lastRepetitionDate,
                        energyLevel: energyLevel,
                        moodRating: moodRating,
                        notes: notes,
                        playCount: playCount,
                        hasActiveCycle: hasActiveCycle
                    )
                    convertedAffirmations.append(legacyAffirmation)
                    result.successfulItems += 1
                }
            }

            // Migrate the converted affirmations to SwiftData
            try await swiftDataRepository.migrateFromLegacy(convertedAffirmations)

            result.duration = Date().timeIntervalSince(startTime)
            result.success = true

            return result
        } catch {
            result.success = false
            result.error = error
            result.duration = Date().timeIntervalSince(startTime)

            return result
        }
    }
}

// MARK: - Helper Types

/// Result of a migration operation
public struct MigrationResult {
    /// Whether the migration was successful
    public var success: Bool = false

    /// Total number of items to migrate
    public var totalItems: Int = 0

    /// Number of items successfully migrated
    public var successfulItems: Int = 0

    /// Duration of the migration in seconds
    public var duration: TimeInterval = 0

    /// Error that occurred during migration, if any
    public var error: Error? = nil

    /// Percentage of items successfully migrated
    public var successPercentage: Double {
        guard totalItems > 0 else { return 0 }
        return Double(successfulItems) / Double(totalItems) * 100
    }
}
*/

import Foundation

/// Extension to provide named task priorities
public extension TaskPriority {
    /// High priority task, suitable for user-initiated actions
    static let userInitiated = TaskPriority.high
    
    /// Medium priority task, suitable for background operations that should complete reasonably quickly
    static let utility = TaskPriority.medium
    
    /// Low priority task, suitable for background operations that can take a long time
    static let background = TaskPriority.low
}

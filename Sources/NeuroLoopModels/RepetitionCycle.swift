import Foundation
import NeuroLoopInterfaces
import SwiftData

/// A model representing a 7-day repetition cycle for an affirmation
@Model
@preconcurrency
public final class RepetitionCycle {
    // MARK: - Properties

    /// Unique identifier for the cycle
    @Attribute(.unique) public var id: UUID

    /// The date when the cycle started
    public var startDate: Date

    /// The current day in the cycle (1-7)
    public var currentDay: Int

    /// Whether the cycle is active
    public var isActive: Bool

    /// Whether the cycle is complete
    public var isComplete: Bool

    /// The date when the cycle was completed (if applicable)
    public var completionDate: Date?

    /// Daily progress for each day in the cycle
    public var dailyProgressData: Data

    /// The affirmation ID this cycle belongs to
    public var affirmationId: UUID?

    /// Transient reference to the affirmation (not persisted)
    /// This is used only for temporary operations and not stored in the database
    /// to avoid circular references
    @Transient
    private var _affirmation: Affirmation?

    /// Get or set the affirmation reference (transient)
    public var affirmation: Affirmation? {
        get { return _affirmation }
        set {
            _affirmation = newValue
            if let newAffirmation = newValue {
                affirmationId = newAffirmation.id
            }
        }
    }

    // MARK: - Computed Properties

    /// Daily progress as a dictionary with Date keys
    public var dailyProgress: [Date: Int] {
        get {
            do {
                let decoder = JSONDecoder()
                let dateFormatter = ISO8601DateFormatter()

                // Custom decoding for Date keys
                let stringDict = try decoder.decode([String: Int].self, from: dailyProgressData)
                var result: [Date: Int] = [:]

                for (key, value) in stringDict {
                    if let date = dateFormatter.date(from: key) {
                        result[date] = value
                    }
                }

                return result
            } catch {
                print("Error decoding daily progress: \(error)")
                return [:]
            }
        }
        set {
            do {
                let encoder = JSONEncoder()
                let dateFormatter = ISO8601DateFormatter()

                // Custom encoding for Date keys
                var stringDict: [String: Int] = [:]

                for (key, value) in newValue {
                    let dateString = dateFormatter.string(from: key)
                    stringDict[dateString] = value
                }

                dailyProgressData = try encoder.encode(stringDict)
            } catch {
                print("Error encoding daily progress: \(error)")
            }
        }
    }

    /// Progress for the current cycle (0.0 to 1.0)
    public var progress: Double {
        var progress = 0.0

        // Calculate progress based on completed days and today's progress
        let completedDaysProgress = Double(currentDay - 1) / Double(AffirmationConstants.CYCLE_DAYS)
        let todayWeight = 1.0 / Double(AffirmationConstants.CYCLE_DAYS)
        let todayProgress = self.todayProgress

        progress = completedDaysProgress + (todayProgress * todayWeight)
        return min(progress, 1.0)
    }

    /// Progress for today's repetitions (0.0 to 1.0)
    public var todayProgress: Double {
        let today = Calendar.current.startOfDay(for: Date())
        let todayCount = dailyProgress[today] ?? 0
        return Double(todayCount) / Double(AffirmationConstants.DAILY_REPETITIONS)
    }

    /// Whether today's repetition quota has been met
    public var hasTodayQuotaMet: Bool {
        let today = Calendar.current.startOfDay(for: Date())
        let todayCount = dailyProgress[today] ?? 0
        return todayCount >= AffirmationConstants.DAILY_REPETITIONS
    }

    /// Total repetitions performed in this cycle
    public var totalRepetitions: Int {
        dailyProgress.values.reduce(0, +)
    }

    // MARK: - Initialization

    public init(
        id: UUID = UUID(),
        startDate: Date = Date(),
        currentDay: Int = 1,
        isActive: Bool = true,
        isComplete: Bool = false,
        completionDate: Date? = nil,
        dailyProgress: [Date: Int] = [:],
        affirmation: Affirmation? = nil
    ) {
        self.id = id
        self.startDate = startDate
        self.currentDay = currentDay
        self.isActive = isActive
        self.isComplete = isComplete
        self.completionDate = completionDate
        self.affirmationId = affirmation?.id

        // Initialize dailyProgressData before setting affirmation
        // Encode daily progress
        let encoder = JSONEncoder()
        let dateFormatter = ISO8601DateFormatter()

        var stringDict: [String: Int] = [:]
        for (key, value) in dailyProgress {
            let dateString = dateFormatter.string(from: key)
            stringDict[dateString] = value
        }

        do {
            self.dailyProgressData = try encoder.encode(stringDict)
        } catch {
            print("Error encoding daily progress: \(error)")
            self.dailyProgressData = Data()
        }

        // Now it's safe to set the affirmation
        self._affirmation = affirmation
    }

    // MARK: - Methods

    /// Check if the cycle is still valid (not missed a day)
    public func validateCycle() -> Bool {
        guard isActive, !isComplete else {
            return false
        }

        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        // Get yesterday's date
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: today) else {
            return false
        }

        // Check if yesterday's quota was met
        let yesterdayCount = dailyProgress[yesterday] ?? 0
        let yesterdayQuotaMet = yesterdayCount >= AffirmationConstants.DAILY_REPETITIONS

        // If we're past day 1 and yesterday's quota wasn't met, the cycle is broken
        if currentDay > 1 && !yesterdayQuotaMet {
            isActive = false
            return false
        }

        return true
    }
}

// The @Model macro already provides the PersistentModel conformance

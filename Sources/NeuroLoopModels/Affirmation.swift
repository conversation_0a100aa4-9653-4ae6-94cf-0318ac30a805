import CloudKit
import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftData

@Model
public final class Affirmation: SwiftDataAffirmationProtocol, Equatable, @unchecked Sendable {
    // MARK: - Properties

    @Attribute(.unique) public var id: UUID
    public var text: String
    public var categoryRawValue: String
    public var recordingURLString: String?
    public var createdAt: Date
    public var updatedAt: Date
    public var completedCycles: Int
    public var currentRepetitions: Int
    public var lastRepetitionDateValue: Date?
    public var energyLevel: Double
    public var moodRatingValue: Int?
    public var notes: String?
    public var isFavorite: Bool
    public var playCount: Int
    public var hasActiveCycle: Bool
    public var currentCycleDayValue: Int = 1
    public var cycleStartDateValue: Date? = nil

    // New property to track the longest streak achieved
    public var longestStreak: Int = 0

    // Relationships
    public var cycles: [RepetitionCycle] = []

    // MARK: - Computed Properties

    public var category: NeuroLoopTypes.AffirmationCategory {
        get {
            NeuroLoopTypes.AffirmationCategory(rawValue: categoryRawValue) ?? .custom
        }
        set {
            categoryRawValue = newValue.rawValue
        }
    }

    public var recordingURL: URL? {
        get {
            if let urlString = recordingURLString {
                return URL(string: urlString)
            }
            return nil
        }
        set {
            recordingURLString = newValue?.absoluteString
        }
    }

    public var lastRepetitionDate: Date? {
        get { lastRepetitionDateValue }
        set { lastRepetitionDateValue = newValue }
    }

    public var moodRating: Int? {
        get { moodRatingValue }
        set { moodRatingValue = newValue }
    }

    public var currentCycle: RepetitionCycle? {
        let cycle = cycles.first(where: { $0.isActive })
        cycle?.affirmation = self
        return cycle
    }

    public var cycleStartDate: Date? {
        get { cycleStartDateValue ?? currentCycle?.startDate }
        set { cycleStartDateValue = newValue }
    }

    public var currentCycleDay: Int {
        get { currentCycleDayValue }
        set { currentCycleDayValue = newValue }
    }

    public var dailyProgress: [Date: Int] {
        get {
            currentCycle?.dailyProgress ?? [:]
        }
        set {
            if let cycle = currentCycle {
                cycle.dailyProgress = newValue
            }
        }
    }

    public var isCurrentCycleComplete: Bool {
        currentCycle?.isComplete ?? false
    }

    public var todayProgress: Double {
        currentCycle?.todayProgress ?? 0.0
    }

    public var cycleProgress: Double {
        currentCycle?.progress ?? 0.0
    }

    public var hasTodayQuotaMet: Bool {
        currentCycle?.hasTodayQuotaMet ?? false
    }

    public var hasRecording: Bool {
        recordingURL != nil
    }

    public var canPerformRepetition: Bool {
        guard hasActiveCycle, let cycle = currentCycle else { return false }

        // Check if we've already met today's quota
        if cycle.hasTodayQuotaMet {
            return false
        }

        // Check if minimum delay has passed since last repetition
        if let lastDate = lastRepetitionDate {
            let timeSinceLastRepetition = Date().timeIntervalSince(lastDate)
            return timeSinceLastRepetition >= AffirmationConstants.MINIMUM_DELAY
        }

        return true
    }

    // MARK: - Initialization

    public init(
        id: UUID = UUID(),
        text: String,
        category: NeuroLoopTypes.AffirmationCategory = .custom,
        recordingURL: URL? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        completedCycles: Int = 0,
        currentRepetitions: Int = 0,
        lastRepetitionDate: Date? = nil,
        energyLevel: Double = 0.5,
        moodRating: Int? = nil,
        notes: String? = nil,
        isFavorite: Bool = false,
        playCount: Int = 0,
        hasActiveCycle: Bool = false,
        cycles: [RepetitionCycle] = [],
        // Include the new longestStreak property in the initializer
        longestStreak: Int = 0
    ) {
        self.id = id
        self.text = text
        self.categoryRawValue = category.rawValue
        self.recordingURLString = recordingURL?.absoluteString
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.completedCycles = completedCycles
        self.currentRepetitions = currentRepetitions
        self.lastRepetitionDateValue = lastRepetitionDate
        self.energyLevel = energyLevel
        self.moodRatingValue = moodRating
        self.notes = notes
        self.isFavorite = isFavorite
        self.playCount = playCount
        self.hasActiveCycle = hasActiveCycle
        self.cycles = cycles
        self.longestStreak = longestStreak  // Assign the new property
    }

    // MARK: - Methods

    public func updateEnergyLevel(_ level: Double) {
        energyLevel = min(max(level, 0.0), 1.0)
        updatedAt = Date()
    }

    public func recordMood(_ rating: Int, notes: String?) {
        moodRating = rating
        if let notes = notes {
            self.notes = notes
        }
        updatedAt = Date()
    }

    static public func fromCKRecord(_ record: CKRecord) -> Affirmation? {
        guard let idString = record["id"] as? String,
            let id = UUID(uuidString: idString),
            let text = record["text"] as? String,
            let categoryRaw = record["category"] as? String,
            let createdAt = record["createdAt"] as? Date,
            let updatedAt = record["updatedAt"] as? Date
        else { return nil }
        let category = NeuroLoopTypes.AffirmationCategory(rawValue: categoryRaw) ?? .custom
        let recordingURLString = record["recordingURL"] as? String
        let recordingURL = recordingURLString.flatMap { URL(string: $0) }
        let completedCycles = record["completedCycles"] as? Int ?? 0
        let currentRepetitions = record["currentRepetitions"] as? Int ?? 0
        let lastRepetitionDate = record["lastRepetitionDate"] as? Date
        let energyLevel = record["energyLevel"] as? Double ?? 0.0
        let moodRating = record["moodRating"] as? Int
        let notes = record["notes"] as? String
        let isFavorite = record["isFavorite"] as? Bool ?? false
        let playCount = record["playCount"] as? Int ?? 0
        let hasActiveCycle = record["hasActiveCycle"] as? Bool ?? false
        let _ = record["currentCycleDay"] as? Int ?? 1
        let _ = record["cycleStartDate"] as? Date
        return Affirmation(
            id: id,
            text: text,
            category: category,
            recordingURL: recordingURL,
            createdAt: createdAt,
            updatedAt: updatedAt,
            completedCycles: completedCycles,
            currentRepetitions: currentRepetitions,
            lastRepetitionDate: lastRepetitionDate,
            energyLevel: energyLevel,
            moodRating: moodRating,
            notes: notes,
            isFavorite: isFavorite,
            playCount: playCount,
            hasActiveCycle: hasActiveCycle
        )
    }

    // MARK: - Equatable
    public static func == (lhs: Affirmation, rhs: Affirmation) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - AffirmationProtocol Conformance

@MainActor
extension Affirmation: AffirmationProtocol {
    // All properties and methods required by AffirmationProtocol that need to be actor-isolated can be implemented here if needed.
    // If any property needs to be explicitly @MainActor, add it here.

    /// Record a repetition of the affirmation
    public func recordRepetition() throws {
        // Validate that a repetition can be performed
        guard canPerformRepetition else {
            throw AffirmationError.cannotPerformRepetition
        }

        // Update repetition count
        currentRepetitions += 1
        lastRepetitionDate = Date()
        updatedAt = Date()

        // Update the current cycle's daily progress
        if let cycle = currentCycle {
            let today = Calendar.current.startOfDay(for: Date())
            var progress = cycle.dailyProgress
            progress[today, default: 0] += 1
            cycle.dailyProgress = progress

            // Check if we've completed the cycle (reached daily repetition goal)
            if currentRepetitions >= AffirmationConstants.DAILY_REPETITIONS {
                completedCycles += 1
                cycle.isComplete = true
                cycle.isActive = false
                cycle.completionDate = Date()
                hasActiveCycle = false

                // Update longest streak if current streak is longer
                if currentCycleDay > longestStreak {
                    longestStreak = currentCycleDay
                }
            }
        }
    }
}

// Using AffirmationError from NeuroLoopInterfaces

// MARK: - Legacy Support

@preconcurrency
public final class LegacyAffirmation {
    public let id: UUID
    public let text: String
    public let category: NeuroLoopTypes.AffirmationCategory
    public let recordingURL: URL?
    public let createdAt: Date
    public let updatedAt: Date
    public let completedCycles: Int
    public let currentRepetitions: Int
    public let lastRepetitionDate: Date?
    public let energyLevel: Double
    public let moodRating: Int?
    public let notes: String?
    public let isFavorite: Bool
    public let playCount: Int
    public let hasActiveCycle: Bool
    public let currentCycleDay: Int
    public let cycleStartDate: Date?

    public init(
        id: UUID = UUID(),
        text: String,
        category: NeuroLoopTypes.AffirmationCategory = .custom,
        recordingURL: URL? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        completedCycles: Int = 0,
        currentRepetitions: Int = 0,
        lastRepetitionDate: Date? = nil,
        energyLevel: Double = 0.5,
        moodRating: Int? = nil,
        notes: String? = nil,
        isFavorite: Bool = false,
        playCount: Int = 0,
        hasActiveCycle: Bool = false,
        currentCycleDay: Int = 1,
        cycleStartDate: Date? = nil
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.recordingURL = recordingURL
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.completedCycles = completedCycles
        self.currentRepetitions = currentRepetitions
        self.lastRepetitionDate = lastRepetitionDate
        self.energyLevel = energyLevel
        self.moodRating = moodRating
        self.notes = notes
        self.isFavorite = isFavorite
        self.playCount = playCount
        self.hasActiveCycle = hasActiveCycle
        self.currentCycleDay = currentCycleDay
        self.cycleStartDate = cycleStartDate
    }
}

extension RepetitionCycle {
    public static func fromCKRecord(_ record: CKRecord) -> RepetitionCycle? {
        guard let idString = record["id"] as? String,
            let id = UUID(uuidString: idString),
            let startDate = record["startDate"] as? Date,
            let currentDay = record["currentDay"] as? Int,
            let isActive = record["isActive"] as? Bool,
            let isComplete = record["isComplete"] as? Bool
        else { return nil }
        let completionDate = record["completionDate"] as? Date
        let dailyProgressData = record["dailyProgressData"] as? Data ?? Data()
        // Decode dailyProgress from dailyProgressData
        var dailyProgress: [Date: Int] = [:]
        do {
            let decoder = JSONDecoder()
            let dateFormatter = ISO8601DateFormatter()
            let stringDict = try decoder.decode([String: Int].self, from: dailyProgressData)
            for (key, value) in stringDict {
                if let date = dateFormatter.date(from: key) {
                    dailyProgress[date] = value
                }
            }
        } catch {
            // Ignore decoding errors, leave as empty
        }
        return RepetitionCycle(
            id: id,
            startDate: startDate,
            currentDay: currentDay,
            isActive: isActive,
            isComplete: isComplete,
            completionDate: completionDate,
            dailyProgress: dailyProgress,
            affirmation: nil
        )
    }
}

// Protocol specifically for SwiftData models
public protocol SwiftDataAffirmationProtocol {
    var id: UUID { get }
    var text: String { get set }
    var categoryRawValue: String { get set }
    var recordingURLString: String? { get set }
    var createdAt: Date { get set }
    var updatedAt: Date { get set }
    var completedCycles: Int { get set }
    var currentRepetitions: Int { get set }
    var lastRepetitionDateValue: Date? { get set }
    var energyLevel: Double { get set }
    var moodRatingValue: Int? { get set }
    var notes: String? { get set }
    var isFavorite: Bool { get set }
    var playCount: Int { get set }
    var hasActiveCycle: Bool { get set }
    var currentCycleDayValue: Int { get set }
    var cycleStartDateValue: Date? { get set }
    var cycles: [RepetitionCycle] { get set }
}

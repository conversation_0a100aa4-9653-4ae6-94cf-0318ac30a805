/*
 DTOs.swift

 Data Transfer Objects (DTOs) for the SwiftData stubbing solution.

 - Purpose: Provide plain, Sendable, actor-safe structs that mirror SwiftData models for use with the in-memory stub (MemoryStorageService).
 - Temporary: These types are only needed while SwiftData is incompatible with Swift 6.1 actor isolation.
 - Migration: Remove these DTOs and conversion methods when switching back to real SwiftData models.
 - Usage: Used throughout the stubbed repository and service layers to enable development and testing without SwiftData.
*/

import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

public struct AffirmationDTO: Sendable, Identifiable, Hashable {
    public let id: UUID
    public let text: String
    public let category: AffirmationCategory
    public let recordingURL: URL?
    public let createdAt: Date
    public let updatedAt: Date
    public let completedCycles: Int
    public let currentRepetitions: Int
    public let lastRepetitionDate: Date?
    public let energyLevel: Double
    public let moodRating: Int?
    public let notes: String?
    public let isFavorite: Bool
    public let playCount: Int
    public let hasActiveCycle: Bool
    public let currentCycleDay: Int
    public let cycleStartDate: Date?
    public let cycles: [RepetitionCycleDTO]
    // Renamed to avoid recursion with the protocol property
    public private(set) var _longestStreak: Int

    public init(
        id: UUID,
        text: String,
        category: AffirmationCategory,
        recordingURL: URL?,
        createdAt: Date,
        updatedAt: Date,
        completedCycles: Int,
        currentRepetitions: Int,
        lastRepetitionDate: Date?,
        energyLevel: Double,
        moodRating: Int?,
        notes: String?,
        isFavorite: Bool,
        playCount: Int,
        hasActiveCycle: Bool,
        currentCycleDay: Int,
        cycleStartDate: Date?,
        cycles: [RepetitionCycleDTO],
        longestStreak: Int = 0
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.recordingURL = recordingURL
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.completedCycles = completedCycles
        self.currentRepetitions = currentRepetitions
        self.lastRepetitionDate = lastRepetitionDate
        self.energyLevel = energyLevel
        self.moodRating = moodRating
        self.notes = notes
        self.isFavorite = isFavorite
        self.playCount = playCount
        self.hasActiveCycle = hasActiveCycle
        self.currentCycleDay = currentCycleDay
        self.cycleStartDate = cycleStartDate
        self.cycles = cycles
        self._longestStreak = longestStreak
    }

    @MainActor
    /// Convert a SwiftData Affirmation model to a DTO for use in the stub
    /// - Migration: Remove this when switching back to SwiftData
    internal static func from(model: Affirmation) -> AffirmationDTO {
        AffirmationDTO(
            id: model.id,
            text: model.text,
            category: model.category,
            recordingURL: model.recordingURL,
            createdAt: model.createdAt,
            updatedAt: model.updatedAt,
            completedCycles: model.completedCycles,
            currentRepetitions: model.currentRepetitions,
            lastRepetitionDate: model.lastRepetitionDate,
            energyLevel: model.energyLevel,
            moodRating: model.moodRating,
            notes: model.notes,
            isFavorite: model.isFavorite,
            playCount: model.playCount,
            hasActiveCycle: model.hasActiveCycle,
            currentCycleDay: model.currentCycleDayValue,
            cycleStartDate: model.cycleStartDate,
            cycles: model.cycles.map { RepetitionCycleDTO.from(model: $0) },
            longestStreak: model.longestStreak
        )
    }
}

public struct RepetitionCycleDTO: Sendable, Identifiable, Hashable {
    public let id: UUID
    public let startDate: Date
    public let currentDay: Int
    public let isActive: Bool
    public let isComplete: Bool
    public let completionDate: Date?
    public let dailyProgressData: Data
    public let affirmationId: UUID?

    public init(
        id: UUID,
        startDate: Date,
        currentDay: Int,
        isActive: Bool,
        isComplete: Bool,
        completionDate: Date?,
        dailyProgressData: Data,
        affirmationId: UUID?
    ) {
        self.id = id
        self.startDate = startDate
        self.currentDay = currentDay
        self.isActive = isActive
        self.isComplete = isComplete
        self.completionDate = completionDate
        self.dailyProgressData = dailyProgressData
        self.affirmationId = affirmationId
    }

    @MainActor
    /// Convert a SwiftData RepetitionCycle model to a DTO for use in the stub
    /// - Migration: Remove this when switching back to SwiftData
    internal static func from(model: RepetitionCycle) -> RepetitionCycleDTO {
        RepetitionCycleDTO(
            id: model.id,
            startDate: model.startDate,
            currentDay: model.currentDay,
            isActive: model.isActive,
            isComplete: model.isComplete,
            completionDate: model.completionDate,
            dailyProgressData: model.dailyProgressData,
            affirmationId: model.affirmationId
        )
    }
}

// MARK: - Protocol Conformance for Stubbing
//
// These extensions allow DTOs to be used in place of SwiftData models.
// All mutation methods are no-ops; computed properties return default values.
// Remove these extensions when migrating back to SwiftData.
extension AffirmationDTO: AffirmationProtocol {
    public var dailyProgress: [Date: Int] { [:] }
    public var isCurrentCycleComplete: Bool { false }
    public var todayProgress: Double { 0.0 }
    public var cycleProgress: Double { 0.0 }
    public var hasTodayQuotaMet: Bool { false }
    public var canPerformRepetition: Bool { false }
    public var hasRecording: Bool { recordingURL != nil }
    // We need to use a different name for the stored property to avoid recursion
    // The protocol requires a getter and setter, but we'll make the setter a no-op
    public var longestStreak: Int {
        get { _longestStreak }
        set { /* No-op for DTO */ }
    }
    public func recordRepetition() throws {}
    public func updateEnergyLevel(_ level: Double) {}
    public func recordMood(_ rating: Int, notes: String?) {}
}

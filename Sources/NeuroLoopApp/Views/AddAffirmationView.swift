import NeuroLoopInterfaces
import Neuro<PERSON><PERSON><PERSON>ore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import NeuroLoopUI
import Swift<PERSON>

#if DEBUG
    private final class MockSyncService: SyncServiceProtocol {
        func saveAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
        func loadAffirmations() async throws -> [any AffirmationProtocol] { [] }
        func deleteAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
    }
    private final class MockPerformanceMonitor: PerformanceMonitorProtocol {
        func trackAffirmationShown(_ affirmation: any AffirmationProtocol) async {}
        func getMetrics() async -> [String: Double] { [:] }
        func resetMetrics() async {}
    }
#endif

/// View for adding a new affirmation
public struct AddAffirmationView: View {
    // MARK: - Properties

    @Environment(\.dismiss) private var dismiss
    @ObservedObject var viewModel: AffirmationViewModel

    @State private var text = ""
    @State private var category = AffirmationCategory.custom
    @State private var isRecording = false
    @State private var recordingURL: URL?
    @State private var recordingPower: Float = 0
    @State private var isPlaying = false
    @State private var showSubscriptionSheet = false

    @StateObject private var audioRecordingViewModel = {
        #if os(iOS)
        return AudioRecordingViewModel(
            audioRecordingService: AudioRecordingService(audioFileManager: AudioFileManager.shared))
        #else
        return AudioRecordingViewModel(
            audioRecordingService: AudioRecordingService())
        #endif
    }()

    private let premiumUnlocker = PremiumFeatureUnlocker(
        featureName: "Unlimited Affirmations", premiumService: PremiumService.shared)
    private let FREE_AFFIRMATION_LIMIT = 3

    // MARK: - Initialization

    public init(viewModel: AffirmationViewModel) {
        self.viewModel = viewModel
    }

    // MARK: - Body

    public var body: some View {
        NavigationView {
            ZStack {
                viewModel.theme.backgroundColor.asColor
                    .ignoresSafeArea()

                premiumUnlocker.premiumFeatureView(
                    .unlimitedAffirmations, onUpgrade: { showSubscriptionSheet = true }
                ) {
                    MainContentView(
                        viewModel: viewModel,
                        text: $text,
                        category: $category,
                        audioRecordingViewModel: audioRecordingViewModel,
                        isValid: isValid,
                        onDismiss: { dismiss() }
                    )
                }
                .sheet(isPresented: $showSubscriptionSheet) {
                    SubscriptionView(
                        viewModel: SubscriptionViewModel(premiumService: PremiumService()))
                }
            }
            .navigationTitle("New Affirmation")
            #if os(iOS)
                .navigationBarTitleDisplayMode(.inline)
            #endif
            .toolbar {
                #if os(iOS)
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("Cancel") {
                            audioRecordingViewModel.deleteRecording()
                            dismiss()
                        }
                    }
                #elseif os(macOS)
                    ToolbarItem(placement: .automatic) {
                        Button("Cancel") {
                            audioRecordingViewModel.deleteRecording()
                            dismiss()
                        }
                    }
                #endif
            }
        }
    }

    // MARK: - Computed Properties

    private var isValid: Bool {
        !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
}

// MARK: - Main Content View
private struct MainContentView: View {
    @ObservedObject var viewModel: AffirmationViewModel
    @Binding var text: String
    @Binding var category: AffirmationCategory
    @ObservedObject var audioRecordingViewModel: AudioRecordingViewModel
    let isValid: Bool
    let onDismiss: () -> Void

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                AffirmationInputView(text: $text, theme: viewModel.theme)
                CategorySelectionView(category: $category, theme: viewModel.theme)
                AudioRecordingSectionView(
                    viewModel: audioRecordingViewModel, theme: viewModel.theme)
                CreateButtonView(
                    viewModel: viewModel,
                    text: text,
                    category: category,
                    recordingURL: audioRecordingViewModel.publicRecordingURL,
                    isValid: isValid,
                    onDismiss: onDismiss
                )
            }
            .padding()
        }
    }
}

// MARK: - Affirmation Input View
private struct AffirmationInputView: View {
    @Binding var text: String
    let theme: Theme

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Affirmation")
                .font(.headline)
                .foregroundColor(theme.primaryTextColor.color)

            TextEditor(text: $text)
                .frame(minHeight: 100)
                .padding()
                .background(
                    AnyShapeStyle(
                        theme.cardBackgroundColor.asGradient
                            ?? theme.cardBackgroundColor.asColor.asFallbackGradient()
                    )
                )
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(theme.borderColor.color, lineWidth: 1)
                )
        }
    }
}

// MARK: - Category Selection View
private struct CategorySelectionView: View {
    @Binding var category: AffirmationCategory
    let theme: Theme

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Category")
                .font(.headline)
                .foregroundColor(theme.primaryTextColor.color)

            Picker("Category", selection: $category) {
                ForEach(AffirmationCategory.allCases) { category in
                    Label(
                        category.displayName,
                        systemImage: category.iconName
                    )
                    .tag(category)
                }
            }
            .pickerStyle(MenuPickerStyle())
            .padding()
            .background(
                AnyShapeStyle(
                    theme.cardBackgroundColor.asGradient
                        ?? theme.cardBackgroundColor.asColor.asFallbackGradient()
                )
            )
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(theme.borderColor.color, lineWidth: 1)
            )
        }
    }
}

// MARK: - Audio Recording Section View
private struct AudioRecordingSectionView: View {
    @ObservedObject var viewModel: AudioRecordingViewModel
    let theme: Theme

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Voice Recording (Optional)")
                .font(.headline)
                .foregroundColor(theme.primaryTextColor.color)

            AudioRecordingView(viewModel: viewModel)
        }
    }
}

// MARK: - Create Button View
private struct CreateButtonView: View {
    @ObservedObject var viewModel: AffirmationViewModel
    let text: String
    let category: AffirmationCategory
    let recordingURL: URL?
    let isValid: Bool
    let onDismiss: () -> Void

    var body: some View {
        Button(action: {
            Task {
                await viewModel.createAffirmation(
                    text: text,
                    category: category,
                    recordingURL: recordingURL
                )
                onDismiss()
            }
        }) {
            Text("Create Affirmation")
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    AnyShapeStyle(
                        isValid
                            ? (viewModel.theme.accentColor.asGradient
                                ?? viewModel.theme.accentColor.asColor.asFallbackGradient())
                            : viewModel.theme.disabledColor.color.asFallbackGradient()
                    )
                )
                .cornerRadius(10)
        }
        .disabled(!isValid)
        .padding(.top)
    }
}

#if DEBUG
    struct AddAffirmationView_Previews: PreviewProvider {
        static var previews: some View {
            Text("Preview not available")
            // Temporarily disabled due to async initialization issues
        }
    }
#endif

import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import NeuroLoopUI
import SwiftUI

#if DEBUG
    private class PreviewAffirmationRepository: NeuroLoopInterfaces.AffirmationRepositoryProtocol,
        @unchecked Sendable
    {
        public init() {}
        public func fetchAffirmations() async throws -> [any NeuroLoopInterfaces
            .AffirmationProtocol]
        {
            return [
                await NeuroLoopInterfaces.AffirmationStub(
                    text: "Preview Affirmation", category: .confidence, recordingURL: nil,
                    isFavorite: false, todayProgress: 0.5, cycleProgress: 0.3)
            ]
        }
        public func fetchAffirmation(id: UUID) async throws -> (
            any NeuroLoopInterfaces.AffirmationProtocol
        )? {
            return await NeuroLoopInterfaces.AffirmationStub(
                text: "Preview Affirmation", category: .confidence, recordingURL: nil,
                isFavorite: false, todayProgress: 0.5, cycleProgress: 0.3)
        }
        public func createAffirmation(
            text: String, category: AffirmationCategory, recordingURL: URL?
        ) async throws -> any NeuroLoopInterfaces.AffirmationProtocol {
            return await NeuroLoopInterfaces.AffirmationStub(
                text: text, category: category, recordingURL: recordingURL, isFavorite: false,
                todayProgress: 0.0, cycleProgress: 0.0)
        }
        public func updateAffirmation(_ affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
            async throws
        {}
        public func deleteAffirmation(id: UUID) async throws {}
        public func fetchAffirmations(category: AffirmationCategory) async throws
            -> [any NeuroLoopInterfaces.AffirmationProtocol]
        {
            return [
                await NeuroLoopInterfaces.AffirmationStub(
                    text: "Preview Affirmation", category: category, recordingURL: nil,
                    isFavorite: false, todayProgress: 0.5, cycleProgress: 0.3)
            ]
        }
        public func fetchFavoriteAffirmations() async throws -> [any NeuroLoopInterfaces
            .AffirmationProtocol]
        {
            return [
                await NeuroLoopInterfaces.AffirmationStub(
                    text: "Favorite Preview Affirmation", category: .confidence, recordingURL: nil,
                    isFavorite: true, todayProgress: 1.0, cycleProgress: 0.8)
            ]
        }
        public func fetchCurrentAffirmation() async throws -> (
            any NeuroLoopInterfaces.AffirmationProtocol
        )? {
            return await NeuroLoopInterfaces.AffirmationStub(
                text: "Preview Affirmation", category: .confidence, recordingURL: nil,
                isFavorite: false, todayProgress: 0.5, cycleProgress: 0.3)
        }
        public func recordRepetition(for affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
            async throws
        {}
        public func startCycle(for affirmation: any NeuroLoopInterfaces.AffirmationProtocol)
            async throws
        {}
    }
#endif

public struct AffirmationsView: View {
    @StateObject private var viewModel: AffirmationViewModel

    @State private var isLoading: Bool = true

    public init(viewModel: AffirmationViewModel? = nil) {
        if let viewModel = viewModel {
            _viewModel = StateObject(wrappedValue: viewModel)
        } else {
            // Use a mock implementation for preview/testing
            let affirmationService = AffirmationService(repository: PreviewAffirmationRepository())
            let repetitionService = RepetitionService(affirmationService: affirmationService)
            let streakService = StreakService(repository: PreviewAffirmationRepository())
            #if os(iOS)
            let audioRecordingService = AudioRecordingService(
                audioFileManager: AudioFileManager.shared)
            #else
            let audioRecordingService = AudioRecordingService()
            #endif
            let viewModel = AffirmationViewModel(
                affirmationService: affirmationService,
                repetitionService: repetitionService,
                streakService: streakService,
                audioRecordingService: audioRecordingService,
                hapticManager: HapticManager.shared,
                themeManager: ThemeManager.shared
            )
            _viewModel = StateObject(wrappedValue: viewModel)
        }
    }

    public var body: some View {
        VStack(spacing: 20) {
            if isLoading || viewModel.isLoading {
                AffirmationsSkeletonView()
            } else {
                Text("Daily Affirmations")
                    .font(.largeTitle)
                    .bold()
                    .foregroundColor(viewModel.theme.primaryTextColor.foreground)
                    .accessibilityAddTraits(.isHeader)

                if let currentAffirmation = viewModel.currentAffirmation {
                    Text(currentAffirmation.text)
                        .font(.title2)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        .foregroundColor(viewModel.theme.primaryTextColor.foreground)
                        .accessibilityLabel("Current affirmation: \(currentAffirmation.text)")

                    Text("Day \(viewModel.currentCycleDay) of \(viewModel.totalCycleDays)")
                        .font(.headline)
                        .foregroundColor(viewModel.theme.secondaryTextColor.foreground)
                        .padding(.top, 5)
                        .accessibilityLabel(
                            "Day \(viewModel.currentCycleDay) of \(viewModel.totalCycleDays)")

                    ZStack {
                        // Background for contrast
                        RoundedRectangle(cornerRadius: 20)
                            .fill(
                                viewModel.theme.accentColor.asGradient
                                    ?? LinearGradient(
                                        gradient: Gradient(colors: [
                                            viewModel.theme.accentColor.asColor
                                        ]), startPoint: .top, endPoint: .bottom)
                            )
                            .frame(width: 220, height: 220)

                        RepetitionCounterView(
                            currentCount: viewModel.currentRepetitions,
                            totalCount: viewModel.totalRepetitions,
                            onTap: {
                                Task {
                                    await viewModel.recordRepetition()
                                }
                            }
                        )
                        .disabled(!viewModel.canRecordRepetition)
                        .opacity(viewModel.canRecordRepetition ? 1.0 : 0.6)
                    }
                    .padding(.vertical, 20)
                    .background(
                        viewModel.theme.accentColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [viewModel.theme.accentColor.asColor]),
                                startPoint: .top, endPoint: .bottom)
                    )

                    if viewModel.isCurrentCycleComplete {
                        Text("Cycle Complete! 🎉")
                            .font(.headline)
                            .foregroundColor(.green)
                            .padding()
                            .accessibilityLabel("Cycle Complete!")
                    } else if viewModel.hasTodayQuotaMet {
                        Text("Today's Goal Met! 🎯")
                            .font(.headline)
                            .foregroundColor(.green)
                            .padding()
                            .accessibilityLabel("Today's Goal Met!")
                    }

                    Toggle(isOn: $viewModel.isAutoRepeatEnabled) {
                        Text("Auto-Repeat Mode")
                            .foregroundColor(viewModel.theme.primaryTextColor.foreground)
                    }
                    .padding(.horizontal, 40)
                    .accessibilityHint(
                        "When enabled, repetitions will be automatically counted at regular intervals"
                    )

                    // Manual repetition button
                    Button(action: {
                        print("🔍 Manual repetition increment")
                        Task {
                            await viewModel.recordRepetition()
                        }
                    }) {
                        Text("Record Repetition")
                            .font(.caption)
                            .padding(8)
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                    .padding(.top, 10)
                } else {
                    Text("No active affirmation")
                        .font(.title2)
                        .foregroundColor(viewModel.theme.primaryTextColor.foreground.opacity(0.7))
                        .padding()

                    Button("Create Your First Affirmation") {
                        // This would navigate to affirmation creation screen
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        viewModel.theme.accentColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [viewModel.theme.accentColor.asColor]),
                                startPoint: .top, endPoint: .bottom)
                    )
                    .cornerRadius(10)
                    .accessibilityHint("Tap to create your first affirmation")
                }

                Spacer()
            }
        }
        .padding()
        .onAppear {
            Task {
                await viewModel.loadAffirmations()
                isLoading = false
            }
        }
    }
}

// MARK: - Affirmations Skeleton View

private struct AffirmationsSkeletonView: View {
    var body: some View {
        VStack(spacing: 24) {
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.2))
                .frame(height: 40)
                .redacted(reason: .placeholder)
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.15))
                .frame(height: 60)
                .redacted(reason: .placeholder)
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.15))
                .frame(height: 220)
                .redacted(reason: .placeholder)
        }
        .padding()
        .accessibilityHidden(true)
    }
}

#Preview {
    AffirmationsView()
}

import SwiftUI
import UserNotifications
import NeuroLoopCore

public struct NotificationSettingsView: View {
    @ObservedObject var viewModel: NotificationViewModel

    public init(viewModel: NotificationViewModel) {
        self.viewModel = viewModel
    }

    public var body: some View {
        Form {
            Section {
                Toggle(isOn: $viewModel.dailyReminderEnabled) {
                    Text("Daily Reminder")
                }
                .onChange(of: viewModel.dailyReminderEnabled, initial: false) { oldValue, newValue in
                    viewModel.toggleDailyReminder(newValue)
                }

                if viewModel.dailyReminderEnabled {
                    DatePicker(
                        "Preferred Time",
                        selection: $viewModel.preferredTime,
                        displayedComponents: .hourAndMinute
                    )
                    .onChange(of: viewModel.preferredTime, initial: false) { oldValue, newValue in
                        viewModel.updatePreferredTime(newValue)
                    }
                }
            } header: {
                Text("Reminders")
            }

            Section {
                Toggle(isOn: $viewModel.milestoneNotificationsEnabled) {
                    Text("Milestone Celebrations")
                }
                .onChange(of: viewModel.milestoneNotificationsEnabled, initial: false) { oldValue, newValue in
                    viewModel.toggleMilestoneNotifications(newValue)
                }

                Toggle(isOn: $viewModel.streakProtectionEnabled) {
                    Text("Streak Protection Alerts")
                }
                .onChange(of: viewModel.streakProtectionEnabled, initial: false) { oldValue, newValue in
                    viewModel.toggleStreakProtection(newValue)
                }
            } header: {
                Text("Milestones & Streak Protection")
            }

            Section {
                HStack {
                    Text("Notification Status")
                    Spacer()
                    Text(permissionStatusText)
                        .foregroundColor(permissionStatusColor)
                        .fontWeight(.semibold)
                }
                if viewModel.authorizationStatus != .authorized {
                    Button("Request Permission") {
                        viewModel.requestPermission()
                    }
                }
            } header: {
                Text("Permissions")
            }

            if let error = viewModel.error {
                Section {
                    Text(error)
                        .foregroundColor(.red)
                }
            }
        }
        .navigationTitle("Notifications")
#if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
#endif
    }

    private var permissionStatusText: String {
        switch viewModel.authorizationStatus {
        case .authorized: return "Enabled"
        case .denied: return "Denied"
        case .notDetermined: return "Not Determined"
        case .provisional: return "Provisional"
        case .ephemeral: return "Ephemeral"
        @unknown default: return "Unknown"
        }
    }

    private var permissionStatusColor: Color {
        switch viewModel.authorizationStatus {
        case .authorized: return .green
        case .denied: return .red
        default: return .orange
        }
    }
}
import Foundation
import Combine
import NeuroLoopCore
import UserNotifications
import NeuroLoopInterfaces

@MainActor
public class NotificationViewModel: ObservableObject {
    @Published public var dailyReminderEnabled: Bool = false
    @Published public var milestoneNotificationsEnabled: Bool = true
    @Published public var streakProtectionEnabled: Bool = true
    @Published public var preferredTime: Date = Calendar.current.date(bySettingHour: 8, minute: 0, second: 0, of: Date()) ?? Date()
    @Published public var authorizationStatus: UserNotifications.UNAuthorizationStatus = .notDetermined
    @Published public var error: String?

    private let notificationService: NotificationServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    public init(notificationService: NotificationServiceProtocol = NotificationService.shared) {
        self.notificationService = notificationService
        loadPreferences()
        checkPermission()
    }

    public func requestPermission() {
        notificationService.requestAuthorization { [weak self] granted, error in
            if let error = error {
                self?.error = error.localizedDescription
            }
            self?.checkPermission()
        }
    }

    public func checkPermission() {
        notificationService.checkAuthorizationStatus { [weak self] status in
#if os(iOS)
            self?.authorizationStatus = status
#else
            self?.authorizationStatus = status
#endif
        }
    }

    public func toggleDailyReminder(_ enabled: Bool) {
        dailyReminderEnabled = enabled
        savePreferences()
        if enabled {
            scheduleDailyReminder()
        } else {
            notificationService.cancelDailyReminder()
        }
    }

    public func updatePreferredTime(_ date: Date) {
        preferredTime = date
        savePreferences()
        if dailyReminderEnabled {
            scheduleDailyReminder()
        }
    }

    public func toggleMilestoneNotifications(_ enabled: Bool) {
        milestoneNotificationsEnabled = enabled
        savePreferences()
        if !enabled {
            notificationService.cancelMilestoneNotifications()
        }
    }

    public func toggleStreakProtection(_ enabled: Bool) {
        streakProtectionEnabled = enabled
        savePreferences()
        if !enabled {
            notificationService.cancelStreakProtectionAlerts()
        }
    }

    private func scheduleDailyReminder() {
        let message = "Don't forget to practice your affirmations today!"
        notificationService.scheduleDailyReminder(at: preferredTime, message: message)
    }

    // MARK: - Persistence
    private func loadPreferences() {
        let defaults = UserDefaults.standard
        dailyReminderEnabled = defaults.bool(forKey: "dailyReminderEnabled")
        milestoneNotificationsEnabled = defaults.object(forKey: "milestoneNotificationsEnabled") as? Bool ?? true
        streakProtectionEnabled = defaults.object(forKey: "streakProtectionEnabled") as? Bool ?? true
        if let time = defaults.object(forKey: "preferredTime") as? Date {
            preferredTime = time
        }
    }

    private func savePreferences() {
        let defaults = UserDefaults.standard
        defaults.set(dailyReminderEnabled, forKey: "dailyReminderEnabled")
        defaults.set(milestoneNotificationsEnabled, forKey: "milestoneNotificationsEnabled")
        defaults.set(streakProtectionEnabled, forKey: "streakProtectionEnabled")
        defaults.set(preferredTime, forKey: "preferredTime")
    }
} 
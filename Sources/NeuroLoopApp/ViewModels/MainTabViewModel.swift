import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import NeuroLoop<PERSON>

@MainActor
@available(iOS 17.0, macOS 14.0, *)
public class MainTabViewModel: ObservableObject {
    // MARK: - Child View Models

    public let affirmationsViewModel: AffirmationViewModel
    public let settingsViewModel: NeuroLoopUI.SettingsViewModel

    // MARK: - Services

    private let affirmationService: AffirmationServiceProtocol
    private let repetitionService: RepetitionServiceProtocol
    private let streakService: StreakServiceProtocol
    private let audioService: AudioRecordingServiceProtocol

    // MARK: - Initialization

    public init(
        affirmationService: AffirmationServiceProtocol,
        repetitionService: RepetitionServiceProtocol,
        streakService: StreakServiceProtocol,
        audioService: AudioRecordingServiceProtocol
    ) {
        self.affirmationService = affirmationService
        self.repetitionService = repetitionService
        self.streakService = streakService
        self.audioService = audioService

        // Initialize child view models
        self.affirmationsViewModel = AffirmationViewModel(
            affirmationService: affirmationService,
            repetitionService: repetitionService,
            streakService: streakService,
            audioRecordingService: audioService,
            hapticManager: NeuroLoopTypes.HapticManager.shared,
            themeManager: NeuroLoopCore.ThemeManager.shared
        )

        // Initialize settings view model with required services
        let factory = ServiceFactory.shared
        self.settingsViewModel = NeuroLoopUI.SettingsViewModel(
            userDefaults: .standard,
            purchaseManager: factory.getPurchaseManager(),
            dataExportService: factory.getDataExportService(),
            themeManager: NeuroLoopCore.ThemeManager.shared,
            hapticManager: NeuroLoopTypes.HapticManager.shared,
            syncService: factory.getSyncService()
        )
    }
}

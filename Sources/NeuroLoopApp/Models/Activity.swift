import Foundation

/// Represents a user activity in the app for display in the activity feed
public struct Activity: Identifiable {
    public let id: UUID
    public let title: String
    public let subtitle: String
    public let icon: String
    public let time: String
    
    public init(id: UUID = UUID(), title: String, subtitle: String, icon: String, time: String) {
        self.id = id
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.time = time
    }
}

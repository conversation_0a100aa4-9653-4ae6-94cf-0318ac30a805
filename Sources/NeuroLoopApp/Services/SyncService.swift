import NeuroLoopInterfaces
import Foundation

@MainActor
public final class SyncService: SyncServiceProtocol {
    private let fileManager: FileManager
    private let documentsPath: String
    
    public init() {
        self.fileManager = FileManager.default
        self.documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
    }
    
    public func saveAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        let encoder = JSONEncoder()
        guard let stub = affirmation as? AffirmationStub else {
            throw NSError(domain: "SyncService", code: 1, userInfo: [NSLocalizedDescriptionKey: "Only AffirmationStub can be saved"])
        }
        let data = try encoder.encode(stub)
        let filePath = (documentsPath as NSString).appendingPathComponent("affirmation_\(affirmation.id).json")
        try data.write(to: URL(fileURLWithPath: filePath))
    }
    
    public func loadAffirmations() async throws -> [any AffirmationProtocol] {
        let files = try fileManager.contentsOfDirectory(atPath: documentsPath)
        let affirmationFiles = files.filter { $0.hasPrefix("affirmation_") && $0.hasSuffix(".json") }
        
        var affirmations: [any AffirmationProtocol] = []
        let decoder = JSONDecoder()
        
        for file in affirmationFiles {
            let filePath = (documentsPath as NSString).appendingPathComponent(file)
            let data = try Data(contentsOf: URL(fileURLWithPath: filePath))
            let affirmation = try decoder.decode(AffirmationStub.self, from: data)
            affirmations.append(affirmation)
        }
        
        return affirmations.sorted { (a, b) in
            guard let a = a as? AffirmationStub, let b = b as? AffirmationStub else { return false }
            return a.createdAt > b.createdAt
        }
    }
    
    public func deleteAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        let filePath = (documentsPath as NSString).appendingPathComponent("affirmation_\(affirmation.id).json")
        try fileManager.removeItem(atPath: filePath)
    }
} 
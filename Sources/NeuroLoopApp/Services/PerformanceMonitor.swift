import NeuroLoopInterfaces
import Foundation

@MainActor
public final class PerformanceMonitor: PerformanceMonitorProtocol {
    private var metrics: [String: Double] = [:]
    
    public init() {}
    
    public func trackAffirmationShown(_ affirmation: any AffirmationProtocol) async {
        let key = "affirmation_\(affirmation.id)"
        metrics[key] = (metrics[key] ?? 0) + 1
    }
    
    public func getMetrics() async -> [String: Double] {
        return metrics
    }
    
    public func resetMetrics() async {
        metrics.removeAll()
    }
} 
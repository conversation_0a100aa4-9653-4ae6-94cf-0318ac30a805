import SwiftUI
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import NeuroLoopModels
import NeuroLoopShared
import NeuroLoop<PERSON>

// MARK: - SimpleDiagnosticApp (App Version)
@available(iOS 17.0, macOS 14.0, *)
public struct SimpleDiagnosticAppWrapper: App {
    public init() {
        // Public initializer required by App protocol
    }

    public var body: some Scene {
        WindowGroup {
            SimpleDiagnosticView()
                .environmentObject(NeuroLoopUI.NavigationCoordinator())
                .environmentObject(ThemeManager.shared)
                .environmentObject(HapticManager.shared)
        }
    }
}

// Note: SimpleDiagnosticView is now defined directly in NeuroLoopApp.swift
// to avoid scope issues

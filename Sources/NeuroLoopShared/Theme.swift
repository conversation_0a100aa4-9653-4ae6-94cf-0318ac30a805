import SwiftUI

/// Theme for the app
@available(iOS 13.0, macOS 10.15, *)
public struct Theme {
    /// Primary text color
    public let primaryTextColor: Color

    /// Secondary text color
    public let secondaryTextColor: Color

    /// Background color
    public let backgroundColor: Color

    /// Card background color
    public let cardBackgroundColor: Color

    /// Accent color
    public let accentColor: Color

    /// Shadow color
    public let shadowColor: Color

    /// Error color
    public let errorColor: Color

    /// Success color
    public let successColor: Color

    /// Warning color
    public let warningColor: Color

    /// Default light theme
    @available(iOS 13.0, macOS 10.15, *)
    public static let defaultLight = Theme(
        primaryTextColor: Color.black,
        secondaryTextColor: Color.gray,
        backgroundColor: Color(red: 0.95, green: 0.95, blue: 0.97),
        cardBackgroundColor: Color.white,
        accentColor: Color.blue,
        shadowColor: Color.black.opacity(0.2),
        errorColor: Color.red,
        successColor: Color.green,
        warningColor: Color.orange
    )

    /// Default dark theme
    @available(iOS 13.0, macOS 10.15, *)
    public static let defaultDark = Theme(
        primaryTextColor: Color.white,
        secondaryTextColor: Color.gray,
        backgroundColor: Color(red: 0.1, green: 0.1, blue: 0.12),
        cardBackgroundColor: Color(red: 0.2, green: 0.2, blue: 0.22),
        accentColor: Color.blue,
        shadowColor: Color.black.opacity(0.4),
        errorColor: Color.red,
        successColor: Color.green,
        warningColor: Color.orange
    )

    /// Initialize a new theme
    /// - Parameters:
    ///   - primaryTextColor: Primary text color
    ///   - secondaryTextColor: Secondary text color
    ///   - backgroundColor: Background color
    ///   - cardBackgroundColor: Card background color
    ///   - accentColor: Accent color
    ///   - shadowColor: Shadow color
    ///   - errorColor: Error color
    ///   - successColor: Success color
    ///   - warningColor: Warning color
    public init(
        primaryTextColor: Color,
        secondaryTextColor: Color,
        backgroundColor: Color,
        cardBackgroundColor: Color,
        accentColor: Color,
        shadowColor: Color,
        errorColor: Color,
        successColor: Color,
        warningColor: Color
    ) {
        self.primaryTextColor = primaryTextColor
        self.secondaryTextColor = secondaryTextColor
        self.backgroundColor = backgroundColor
        self.cardBackgroundColor = cardBackgroundColor
        self.accentColor = accentColor
        self.shadowColor = shadowColor
        self.errorColor = errorColor
        self.successColor = successColor
        self.warningColor = warningColor
    }
}

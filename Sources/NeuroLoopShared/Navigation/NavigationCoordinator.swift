import NeuroLoopTypes
import SwiftUI

@MainActor
public class NavigationCoordinator: ObservableObject {
    @Published public var homePath: [NavigationRoute] = []
    @Published public var libraryPath: [NavigationRoute] = []
    @Published public var settingsPath: [NavigationRoute] = []

    public init() {}

    public func navigate(to route: NavigationRoute, in tab: Tab) {
        switch tab {
        case .home:
            homePath.append(route)
        case .library:
            libraryPath.append(route)
        case .settings:
            settingsPath.append(route)
        }
    }

    public func popToRoot(in tab: Tab) {
        switch tab {
        case .home:
            homePath.removeAll()
        case .library:
            libraryPath.removeAll()
        case .settings:
            settingsPath.removeAll()
        }
    }

    public func popLast(in tab: Tab) {
        switch tab {
        case .home:
            _ = homePath.popLast()
        case .library:
            _ = libraryPath.popLast()
        case .settings:
            _ = settingsPath.popLast()
        }
    }
}

public enum Tab {
    case home
    case library
    case settings
}

public enum NavigationRoute: Hashable {
    case affirmationDetail(id: UUID)
    case premium
    case theme
    case settings
    case profile
    case help
    case about
}

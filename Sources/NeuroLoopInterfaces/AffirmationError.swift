import Foundation

/// Errors that can occur when working with affirmations
public enum AffirmationError: Error {
    /// The affirmation was not found
    case notFound
    
    /// The affirmation data is invalid
    case invalidData
    
    /// Cannot perform a repetition for the affirmation
    case cannotPerformRepetition
    
    /// The affirmation is already in use
    case alreadyInUse
    
    /// The affirmation has no active cycle
    case noActiveCycle
    
    /// The affirmation has reached the maximum number of cycles
    case maximumCyclesReached
    
    /// The affirmation has reached the maximum number of repetitions for today
    case maximumRepetitionsReached
    
    /// The affirmation cannot be deleted
    case cannotDelete
    
    /// An unknown error occurred
    case unknown
}

import Foundation

/// Represents a day in the streak calendar for UI display
public struct StreakCalendarDay: Identifiable, Sendable {
    /// Unique identifier for the calendar day
    public let id: UUID
    
    /// The date of the calendar day
    public let date: Date
    
    /// Number of repetitions performed on this day
    public let repetitions: Int
    
    /// Whether the day's quota was met
    public let isComplete: Bool
    
    /// Progress for the day (0.0 to 1.0)
    public let progress: Double
    
    /// Initialize a new streak calendar day
    /// - Parameters:
    ///   - id: Unique identifier for the calendar day
    ///   - date: The date of the calendar day
    ///   - repetitions: Number of repetitions performed on this day
    ///   - isComplete: Whether the day's quota was met
    ///   - progress: Progress for the day (0.0 to 1.0)
    public init(
        id: UUID = UUID(),
        date: Date,
        repetitions: Int,
        isComplete: Bool,
        progress: Double
    ) {
        self.id = id
        self.date = date
        self.repetitions = repetitions
        self.isComplete = isComplete
        self.progress = progress
    }
}

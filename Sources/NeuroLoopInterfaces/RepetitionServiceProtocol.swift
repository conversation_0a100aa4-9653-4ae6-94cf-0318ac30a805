import NeuroLoopInterfaces
// Moved to NeuroLoopCore/Services/RepetitionService.swift due to Swift 6.1 cross-module existential bug. See that file for protocol and types.
// This file is intentionally left blank for now.

import Foundation

public protocol RepetitionServiceProtocol: Sendable {
    func recordRepetition(for affirmation: any AffirmationProtocol) async throws -> RepetitionResult
    func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult
    func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo
    func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo
    func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool
    func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval?
    func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult
    func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool
    func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult
}

public struct RepetitionResult: Sendable {
    public let success: Bool
    public let error: Error?
    public let updatedAffirmation: any AffirmationProtocol
    public let isQuotaMet: Bool
    public let isCycleComplete: Bool
    public init(
        success: Bool,
        error: Error? = nil,
        updatedAffirmation: any AffirmationProtocol,
        isQuotaMet: Bool,
        isCycleComplete: Bool
    ) {
        self.success = success
        self.error = error
        self.updatedAffirmation = updatedAffirmation
        self.isQuotaMet = isQuotaMet
        self.isCycleComplete = isCycleComplete
    }
}

public struct CycleResult: Sendable {
    public let success: Bool
    public let error: Error?
    public let updatedAffirmation: any AffirmationProtocol
    public init(
        success: Bool,
        error: Error? = nil,
        updatedAffirmation: any AffirmationProtocol
    ) {
        self.success = success
        self.error = error
        self.updatedAffirmation = updatedAffirmation
    }
}

public struct ProgressInfo: Sendable {
    public let todayProgress: Double
    public let cycleProgress: Double
    public let currentDay: Int
    public let totalDays: Int
    public let currentRepetitions: Int
    public let totalRepetitions: Int
    public let hasTodayQuotaMet: Bool
    public let isCycleComplete: Bool
    public let hasActiveCycle: Bool
    public init(
        todayProgress: Double,
        cycleProgress: Double,
        currentDay: Int,
        totalDays: Int,
        currentRepetitions: Int,
        totalRepetitions: Int,
        hasTodayQuotaMet: Bool,
        isCycleComplete: Bool,
        hasActiveCycle: Bool
    ) {
        self.todayProgress = todayProgress
        self.cycleProgress = cycleProgress
        self.currentDay = currentDay
        self.totalDays = totalDays
        self.currentRepetitions = currentRepetitions
        self.totalRepetitions = totalRepetitions
        self.hasTodayQuotaMet = hasTodayQuotaMet
        self.isCycleComplete = isCycleComplete
        self.hasActiveCycle = hasActiveCycle
    }
}

public struct StreakInfo: Sendable {
    public let currentStreak: Int
    public let longestStreak: Int
    public let completedCycles: Int
    public let hasActiveCycle: Bool
    public let cycleStartDate: Date?
    public let lastRepetitionDate: Date?
    public init(
        currentStreak: Int,
        longestStreak: Int,
        completedCycles: Int,
        hasActiveCycle: Bool,
        cycleStartDate: Date?,
        lastRepetitionDate: Date?
    ) {
        self.currentStreak = currentStreak
        self.longestStreak = longestStreak
        self.completedCycles = completedCycles
        self.hasActiveCycle = hasActiveCycle
        self.cycleStartDate = cycleStartDate
        self.lastRepetitionDate = lastRepetitionDate
    }
}
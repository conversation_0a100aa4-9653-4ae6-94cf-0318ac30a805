import Foundation
import Combine

@preconcurrency
public protocol SyncServiceProtocol: Sendable {
    /// Start a manual sync operation
    func syncNow() async throws
    /// Returns the current sync status (idle, syncing, success, error)
    var syncStatus: SyncStatus { get }
    /// Publisher for sync status updates (for UI binding)
    @preconcurrency @MainActor var syncStatusPublisher: AnyPublisher<SyncStatus, Never> { get }
    /// Returns the last sync error, if any
    var lastSyncError: Error? { get async }
    /// Returns the date of the last successful sync
    var lastSyncDate: Date? { get async }
    /// Enable or disable automatic background sync
    func setAutomaticSyncEnabled(_ enabled: Bool) async
    /// Returns whether automatic sync is enabled
    var isAutomaticSyncEnabled: Bool { get async }
}

public enum SyncStatus: Equatable, Sendable {
    case idle
    case inProgress
    case success
    case error
}

public struct SyncChange: Equatable, Sendable {
    public let id: String
    public let type: ChangeType
    public let data: Data
    
    public enum ChangeType: Equatable, Sendable {
        case create
        case update
        case delete
    }
    
    public init(id: String, type: ChangeType, data: Data) {
        self.id = id
        self.type = type
        self.data = data
    }
} 
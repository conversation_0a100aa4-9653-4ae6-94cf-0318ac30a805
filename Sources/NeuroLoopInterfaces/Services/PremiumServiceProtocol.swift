import Combine
import Foundation
import NeuroLoopTypes

@MainActor
public protocol PremiumServiceProtocol {
    var isPremium: Bool { get }
    var isPremiumPublisher: AnyPublisher<Bool, Never> { get }

    func checkFeatureAvailability(_ feature: NeuroLoopTypes.PremiumFeature) -> Bool
    func purchaseSubscription() async throws
    func restorePurchases() async throws
    func getSubscriptionStatus() async throws -> SubscriptionStatus
}

public struct SubscriptionStatus {
    public let isActive: Bool
    public let expirationDate: Date?
    public let productId: String?

    public init(isActive: Bool, expirationDate: Date?, productId: String?) {
        self.isActive = isActive
        self.expirationDate = expirationDate
        self.productId = productId
    }
}

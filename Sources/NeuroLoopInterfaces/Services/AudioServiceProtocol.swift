import Foundation
import Combine

@MainActor
public protocol AudioServiceProtocol: AnyObject, Sendable {
    var isRecording: Bool { get }
    var recordingTime: TimeInterval { get }
    var recordingPower: Double { get }
    var recordingURL: URL? { get }
    var isPlaying: Bool { get }
    var playbackProgress: Double { get }
    var playbackTime: TimeInterval { get }
    var error: Error? { get }
    
    func startRecording() async throws
    func stopRecording() async throws -> URL
    func deleteRecording()
    func startPlayback() async throws
    func pausePlayback()
    func stopPlayback()
    func deleteRecording(at url: URL) async throws
    func getRecordingDuration(for url: URL) async throws -> TimeInterval
    func getRecordingWaveform(for url: URL) async throws -> [Float]
    
    /// Diagnostic method to test microphone access and audio session setup
    func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double)
    
    // Publishers for SwiftUI bindings
    var isRecordingPublisher: Published<Bool>.Publisher { get }
    var recordingTimePublisher: Published<TimeInterval>.Publisher { get }
    var recordingPowerPublisher: Published<Double>.Publisher { get }
    var recordingURLPublisher: Published<URL?>.Publisher { get }
    var isPlayingPublisher: Published<Bool>.Publisher { get }
    var playbackProgressPublisher: Published<Double>.Publisher { get }
    var playbackTimePublisher: Published<TimeInterval>.Publisher { get }
    var errorPublisher: Published<Error?>.Publisher { get }
}

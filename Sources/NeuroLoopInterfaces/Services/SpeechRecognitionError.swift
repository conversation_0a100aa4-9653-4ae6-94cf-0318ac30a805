import Foundation

/// Errors that can occur during speech recognition
public enum SpeechRecognitionError: Error, Equatable {
    /// Authorization was denied
    case authorizationDenied
    
    /// Authorization was restricted
    case authorizationRestricted
    
    /// Authorization was not determined
    case authorizationNotDetermined
    
    /// Recognition failed
    case recognitionFailed
    
    /// Recognition was cancelled
    case recognitionCancelled
    
    /// Recognition timed out
    case recognitionTimedOut
    
    /// No speech was detected
    case noSpeechDetected
    
    /// Generic error with a message
    case generic(String)
}

extension SpeechRecognitionError: LocalizedError {
    public var errorDescription: String? {
        switch self {
        case .authorizationDenied:
            return "Speech recognition authorization was denied"
        case .authorizationRestricted:
            return "Speech recognition authorization is restricted"
        case .authorizationNotDetermined:
            return "Speech recognition authorization is not determined"
        case .recognitionFailed:
            return "Speech recognition failed"
        case .recognitionCancelled:
            return "Speech recognition was cancelled"
        case .recognitionTimedOut:
            return "Speech recognition timed out"
        case .noSpeechDetected:
            return "No speech was detected"
        case .generic(let message):
            return message
        }
    }
}

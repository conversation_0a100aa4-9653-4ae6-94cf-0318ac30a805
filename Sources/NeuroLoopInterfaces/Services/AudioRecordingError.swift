import Foundation

/// Errors that can occur during audio recording
public enum AudioRecordingError: Error, Equatable {
    /// Permission to record audio was denied
    case permissionDenied
    
    /// Failed to initialize the audio recorder
    case recorderInitializationFailed
    
    /// Failed to start recording
    case recordingFailed
    
    /// Failed to stop recording
    case stoppingFailed
    
    /// Failed to save the recording
    case savingFailed
    
    /// Failed to play the recording
    case playbackFailed
    
    /// Failed to delete the recording
    case deletionFailed
    
    /// No recording available
    case noRecordingAvailable
    
    /// Audio session configuration failed
    case audioSessionConfigurationFailed
    
    /// Generic error with a message
    case generic(String)
}

extension AudioRecordingError: LocalizedError {
    public var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "Permission to record audio was denied"
        case .recorderInitializationFailed:
            return "Failed to initialize the audio recorder"
        case .recordingFailed:
            return "Failed to start recording"
        case .stoppingFailed:
            return "Failed to stop recording"
        case .savingFailed:
            return "Failed to save the recording"
        case .playbackFailed:
            return "Failed to play the recording"
        case .deletionFailed:
            return "Failed to delete the recording"
        case .noRecordingAvailable:
            return "No recording available"
        case .audioSessionConfigurationFailed:
            return "Audio session configuration failed"
        case .generic(let message):
            return message
        }
    }
}

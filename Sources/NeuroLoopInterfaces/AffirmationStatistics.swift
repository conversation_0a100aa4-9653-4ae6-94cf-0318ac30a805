import Foundation
import NeuroLoopTypes

public struct AffirmationStatistics: Sendable {
    public var totalAffirmations: Int = 0
    public var totalCompletedCycles: Int = 0
    public var totalRepetitions: Int = 0
    public var activeAffirmations: Int = 0
    public var favoriteAffirmations: Int = 0
    public var categoryDistribution: [AffirmationCategory: Int] = [:]
    public var activeStreaks: Int = 0
    public var completedCycles: Int = 0
    public var longestCurrentStreak: Int = 0

    public init() {}
}

import Foundation

public enum AffirmationSortOption: String, CaseIterable, Identifiable {
    case newest
    case oldest
    case alphabetical
    case favorite
    
    public var id: String { rawValue }
    public var displayName: String {
        switch self {
        case .newest: return "Newest"
        case .oldest: return "Oldest"
        case .alphabetical: return "A-Z"
        case .favorite: return "Favorite"
        }
    }
} 
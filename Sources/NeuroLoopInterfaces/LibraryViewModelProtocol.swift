import NeuroLoopInterfaces
import Foundation
import NeuroLoopTypes

@available(iOS 17.0, macOS 14.0, *)
public protocol LibraryViewModelProtocol: ObservableObject {
    var affirmations: [any AffirmationProtocol] { get set }
    var searchText: String { get set }
    var selectedCategory: AffirmationCategory? { get set }
    var sortOption: AffirmationSortOption { get set }
    func loadAffirmations() async
    func refreshLibrary() async
}

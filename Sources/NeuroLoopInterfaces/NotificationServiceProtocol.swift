import Foundation
import UserNotifications

public protocol NotificationServiceProtocol {
    func requestAuthorization(completion: @escaping (Bool, Error?) -> Void)
    func checkAuthorizationStatus(completion: @escaping (UNAuthorizationStatus) -> Void)
    func scheduleDailyReminder(at date: Date, message: String)
    func cancelDailyReminder()
    func scheduleMilestoneNotification(for days: Int, message: String)
    func cancelMilestoneNotifications()
    func scheduleStreakProtectionAlert(for date: Date, message: String)
    func cancelStreakProtectionAlerts()
    func handleNotificationResponse(_ response: UNNotificationResponse)
}

public enum NotificationCategory: String {
    case dailyReminder
    case milestone
    case streakProtection
} 
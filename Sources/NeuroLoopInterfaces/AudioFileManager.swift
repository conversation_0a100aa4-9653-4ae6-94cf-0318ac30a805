import Foundation

@MainActor
public class AudioFileManager {
    public static let shared = AudioFileManager()
    
    private init() {}
    
    public func createUniqueAudioFileURL() -> URL {
        #if os(iOS)
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = UUID().uuidString + ".m4a"
        return documentsDirectory.appendingPathComponent(fileName)
        #elseif os(macOS)
        // Use user's document directory on macOS as well
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = UUID().uuidString + ".m4a"
        return documentsDirectory.appendingPathComponent(fileName)
        #endif
    }
    
    public func deleteAudioFile(at url: URL) throws {
        #if os(iOS) || os(macOS)
        if FileManager.default.fileExists(atPath: url.path) {
            try FileManager.default.removeItem(at: url)
        }
        #endif
    }
} 
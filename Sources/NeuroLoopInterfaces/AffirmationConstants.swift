import Foundation

/// Constants used throughout the app for affirmation-related values
public enum AffirmationConstants {
    /// Number of days in a complete affirmation cycle
    public static let CYCLE_DAYS = 7
    
    /// Number of repetitions required per day
    public static let DAILY_REPETITIONS = 100
    
    /// Maximum number of affirmations allowed for free users
    public static let FREE_TIER_MAX_AFFIRMATIONS = 3
    
    /// Maximum number of custom categories allowed for free users
    public static let FREE_TIER_MAX_CATEGORIES = 2
    
    /// Maximum number of custom themes allowed for free users
    public static let FREE_TIER_MAX_THEMES = 1
    
    /// Minimum delay (in seconds) between repetitions
    public static let MINIMUM_DELAY: TimeInterval = 2.0
} 
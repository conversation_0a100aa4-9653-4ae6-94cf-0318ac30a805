import OrderedCollections

// This file serves as a wrapper for the Collections module
// It re-exports the OrderedCollections module as Collections

// Re-export OrderedDictionary
public typealias OrderedDictionary<Key: Hashable, Value> = OrderedCollections.OrderedDictionary<Key, Value>

// Re-export OrderedSet
public typealias OrderedSet<Element: Hashable> = OrderedCollections.OrderedSet<Element>

// Add any other types from Collections that might be needed

import NeuroLoopCore
import <PERSON>euroLoopTypes
import SwiftUI

extension View {
    /// Applies the blue theme styling to a view
    /// - Parameters:
    ///   - applyBackground: Whether to apply the blue background gradient
    ///   - applyTextColor: Whether to apply white text color
    /// - Returns: A view with the blue theme styling applied
    public func applyBlueTheme(applyBackground: Bool = true, applyTextColor: Bool = true) -> some View {
        self
            .modifier(BlueThemeModifier(applyBackground: applyBackground, applyTextColor: applyTextColor))
    }

    /// Applies the blue card styling to a view
    /// - Parameters:
    ///   - cornerRadius: The corner radius of the card
    ///   - applyTextColor: Whether to apply white text color
    /// - Returns: A view with the blue card styling applied
    public func applyBlueCardStyle(cornerRadius: CGFloat = 16, applyTextColor: Bool = true) -> some View {
        self
            .modifier(BlueCardStyleModifier(cornerRadius: cornerRadius, applyTextColor: applyTextColor))
    }
}

// MARK: - Modifiers

/// Modifier for applying blue theme styling
struct BlueThemeModifier: ViewModifier {
    let applyBackground: Bool
    let applyTextColor: Bool

    func body(content: Content) -> some View {
        content
            .modifier(ConditionalModifier(condition: applyBackground) {
                BlueBackgroundModifier()
            })
            .modifier(ConditionalModifier(condition: applyTextColor) {
                BlueTextModifier()
            })
    }
}

/// Modifier for applying blue card styling
struct BlueCardStyleModifier: ViewModifier {
    let cornerRadius: CGFloat
    let applyTextColor: Bool

    func body(content: Content) -> some View {
        content
            .modifier(BlueCardModifier(cornerRadius: cornerRadius))
            .modifier(ConditionalModifier(condition: applyTextColor) {
                BlueTextModifier()
            })
    }
}

/// Helper modifier for conditional application of another modifier
struct ConditionalModifier<M: ViewModifier>: ViewModifier {
    let condition: Bool
    let modifier: () -> M

    func body(content: Content) -> some View {
        if condition {
            content.modifier(modifier())
        } else {
            content
        }
    }
}

#if DEBUG
struct ViewBlueTheme_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            Text("Blue Theme Applied")
                .font(.headline)
                .padding()
                .applyBlueCardStyle()

            Text("Just Blue Card")
                .font(.headline)
                .padding()
                .applyBlueCardStyle()

            Text("Regular Text")
                .font(.body)
        }
        .padding()
        .applyBlueTheme()
    }
}
#endif

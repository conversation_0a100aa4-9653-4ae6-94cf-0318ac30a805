import SwiftUI
import NeuroLoopCore
import NeuroLoopTypes

// This extension provides easy access to background and card components
// throughout the app, ensuring consistent styling and proper imports.

extension View {
    /// Applies the blue background gradient to the view
    public func withBlueBackground() -> some View {
        ZStack {
            BlueBackgroundGradient()
            self
        }
    }
    
    /// Applies the blue card background to the view with optional padding
    public func withBlueCardBackground(cornerRadius: CGFloat = 16, padding: CGFloat = 16) -> some View {
        ZStack {
            BlueCardBackground(cornerRadius: cornerRadius)
            self.padding(padding)
        }
    }
    
    /// Applies a themed background based on the current theme
    public func withThemedBackground() -> some View {
        self.modifier(ThemedBackgroundModifier())
    }
    
    /// Applies a themed card background based on the current theme
    public func withThemedCardBackground(cornerRadius: CGFloat = 16) -> some View {
        self.modifier(ThemedCardBackgroundModifier(cornerRadius: cornerRadius))
    }
}

/// Modifier that applies the current theme's background
struct ThemedBackgroundModifier: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager
    
    func body(content: Content) -> some View {
        ZStack {
            // Background
            Group {
                if let gradient = themeManager.currentTheme.backgroundColor.asGradient {
                    gradient
                } else {
                    themeManager.currentTheme.backgroundColor.asColor
                }
            }
            .ignoresSafeArea()
            
            // Content
            content
        }
    }
}

/// Modifier that applies the current theme's card background
struct ThemedCardBackgroundModifier: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager
    private let cornerRadius: CGFloat
    
    init(cornerRadius: CGFloat = 16) {
        self.cornerRadius = cornerRadius
    }
    
    func body(content: Content) -> some View {
        content
            .padding()
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? themeManager.currentTheme.cardBackgroundColor.asColor
                                .asFallbackGradient()
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.2),
                        radius: 8, x: 0, y: 4
                    )
            )
    }
}

#if DEBUG
struct BackgroundComponents_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            Text("Blue Background")
                .foregroundColor(.white)
                .padding()
                .withBlueCardBackground()
                
            Text("Themed Background")
                .padding()
                .withThemedCardBackground()
        }
        .padding()
        .withBlueBackground()
        .environmentObject(ThemeManager.shared)
    }
}
#endif

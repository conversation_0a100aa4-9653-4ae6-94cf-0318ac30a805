import SwiftUI
import NeuroLoopCore
import NeuroLoopTypes

public extension View {
    func actionButtonStyle(isPrimary: Bool = true) -> some View {
        self
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isPrimary ? Color.accentColor : Color.gray.opacity(0.2))
            )
            .foregroundColor(isPrimary ? .white : .primary)
    }
}

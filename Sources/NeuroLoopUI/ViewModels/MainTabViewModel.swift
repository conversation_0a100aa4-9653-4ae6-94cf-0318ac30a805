import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

@MainActor
public class MainTabViewModel: ObservableObject {
    @Published public var selectedTab: MainTab = .home
    @Published public var affirmationRepository: AffirmationRepositoryProtocol

    public init() async {
        // Initialize with preview repository
        self.affirmationRepository = await PreviewAffirmationRepository()
    }
}

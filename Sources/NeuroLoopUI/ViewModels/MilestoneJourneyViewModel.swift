import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

public struct Achievement: Identifiable, Hashable {
    public enum MilestoneType: String, CaseIterable, Hashable {
        case repetition, streak, affirmation, time
        public var icon: String {
            switch self {
            case .repetition: return "repeat.circle.fill"
            case .streak: return "flame.fill"
            case .affirmation: return "quote.bubble.fill"
            case .time: return "calendar"
            }
        }
    }
    public let id: UUID
    public let type: MilestoneType
    public let description: String
    public let dateEarned: Date?
    public let icon: String
    public let isEarned: Bool
    public let value: Int?
    public init(
        type: MilestoneType, description: String, dateEarned: Date?, isEarned: Bool,
        value: Int? = nil
    ) {
        self.id = UUID()
        self.type = type
        self.description = description
        self.dateEarned = dateEarned
        self.icon = type.icon
        self.isEarned = isEarned
        self.value = value
    }
}

@MainActor
public final class MilestoneJourneyViewModel: ObservableObject {
    @Published public private(set) var achievements: [Achievement] = []
    @Published public private(set) var timeline: [Achievement] = []
    @Published public private(set) var earnedMilestones: [Achievement] = []
    @Published public private(set) var lockedMilestones: [Achievement] = []
    @Published public var showCelebration: Bool = false
    @Published public var congratulatoryMessage: String? = nil
    @Published public private(set) var completedMilestones: Int = 0
    @Published public private(set) var totalMilestones: Int = 0
    @Published public private(set) var currentStreak: Int = 0
    @Published public private(set) var affirmations: [any AffirmationProtocol] = []

    private let affirmationService: AffirmationService
    private let repetitionService: RepetitionService
    private var allAffirmations: [any AffirmationProtocol] = []
    private var allStreaks: [StreakInfo] = []

    public init(affirmationService: AffirmationService, repetitionService: RepetitionService) {
        self.affirmationService = affirmationService
        self.repetitionService = repetitionService
        Task { await loadData() }
    }

    public func loadData() async {
        do {
            let affirmations = try await affirmationService.fetchAffirmations()
            self.allAffirmations = affirmations
            self.allStreaks = affirmations.map { repetitionService.getStreakInfo(for: $0) }
            computeMilestones()
        } catch {
            print("Error loading journey data: \(error)")
        }
    }

    private func computeMilestones() {
        var milestones: [Achievement] = []
        // Repetition milestones
        let totalReps = allAffirmations.reduce(0) { $0 + $1.currentRepetitions }
        let repMilestones = [100, 500, 1000, 5000, 10000]
        for value in repMilestones {
            let earned = totalReps >= value
            let date: Date? = earned ? Date() : nil  // Placeholder: should track actual date
            milestones.append(
                Achievement(
                    type: .repetition, description: "\(value) Repetitions", dateEarned: date,
                    isEarned: earned, value: value))
        }
        // Streak milestones
        let longestStreak = allStreaks.map { $0.longestStreak }.max() ?? 0
        let streakMilestones = [7, 14, 30, 50, 100]
        for value in streakMilestones {
            let earned = longestStreak >= value
            let date: Date? = earned ? Date() : nil
            milestones.append(
                Achievement(
                    type: .streak, description: "\(value)-Day Streak", dateEarned: date,
                    isEarned: earned, value: value))
        }
        // Affirmation milestones
        let totalAffirmations = allAffirmations.count
        let affirmationMilestones = [1, 5, 10, 20]
        for value in affirmationMilestones {
            let earned = totalAffirmations >= value
            let date: Date? = earned ? Date() : nil
            milestones.append(
                Achievement(
                    type: .affirmation, description: "\(value) Affirmations", dateEarned: date,
                    isEarned: earned, value: value))
        }
        // Time-based milestones (placeholder: first week, month, etc.)
        let timeMilestones = [7, 30, 90, 180, 365]
        for value in timeMilestones {
            let earned = false  // Placeholder: implement actual logic
            milestones.append(
                Achievement(
                    type: .time, description: "\(value) Days of Practice", dateEarned: nil,
                    isEarned: earned, value: value))
        }
        self.achievements = milestones
        self.earnedMilestones = milestones.filter { $0.isEarned }
        self.lockedMilestones = milestones.filter { !$0.isEarned }
        self.timeline = milestones.sorted {
            ($0.dateEarned ?? Date.distantFuture) < ($1.dateEarned ?? Date.distantFuture)
        }

        // Update computed properties
        self.completedMilestones = self.earnedMilestones.count
        self.totalMilestones = milestones.count
        self.currentStreak = allStreaks.map { $0.currentStreak }.max() ?? 0
    }

    public func celebrateMilestone(_ achievement: Achievement) {
        showCelebration = true
        congratulatoryMessage = "Congratulations! You've reached: \(achievement.description)"
        // Optionally trigger haptic or sound
    }

    public func isMilestoneUnlocked(_ milestone: Achievement) -> Bool {
        return milestone.isEarned
    }

    public func selectMilestone(_ milestone: Achievement) {
        if milestone.isEarned {
            celebrateMilestone(milestone)
        }
    }
}

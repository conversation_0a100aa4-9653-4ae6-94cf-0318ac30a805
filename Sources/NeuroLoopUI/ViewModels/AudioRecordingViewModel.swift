import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class AudioRecordingViewModel: ObservableObject {
    private let audioRecordingService: AudioRecordingServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    @Published public private(set) var isRecording: Bool = false
    @Published public private(set) var recordingTime: TimeInterval = 0
    @Published public private(set) var recordingPower: Double = 0
    @Published public private(set) var hasRecording: Bool = false
    @Published public private(set) var isPlaying: Bool = false
    @Published public private(set) var playbackProgress: Double = 0
    @Published public private(set) var audioSamples: [Float] = []
    @Published public private(set) var error: Error?

    public var formattedTime: String {
        let minutes = Int(recordingTime) / 60
        let seconds = Int(recordingTime) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    public var publicRecordingURL: URL? {
        audioRecordingService.recordingURL
    }

    public init(audioRecordingService: AudioRecordingServiceProtocol) {
        self.audioRecordingService = audioRecordingService
        setupBindings()
    }

    private func setupBindings() {
        audioRecordingService.isRecordingPublisher
            .assign(to: &$isRecording)

        audioRecordingService.recordingTimePublisher
            .assign(to: &$recordingTime)

        audioRecordingService.recordingPowerPublisher
            .assign(to: &$recordingPower)

        audioRecordingService.recordingURLPublisher
            .map { $0 != nil }
            .assign(to: &$hasRecording)

        audioRecordingService.isPlayingPublisher
            .assign(to: &$isPlaying)

        audioRecordingService.playbackProgressPublisher
            .assign(to: &$playbackProgress)

        // Convert recording power to audio samples for visualization
        audioRecordingService.recordingPowerPublisher
            .map { [weak self] power in
                guard let self = self else { return [] }
                var samples = self.audioSamples
                samples.append(Float(power))
                if samples.count > 100 {
                    samples.removeFirst()
                }
                return samples
            }
            .assign(to: &$audioSamples)
    }

    public func startRecording() async throws {
        try await audioRecordingService.startRecording()
    }

    public func stopRecording() async throws {
        _ = try await audioRecordingService.stopRecording()
    }

    public func toggleRecording() {
        Task {
            do {
                if isRecording {
                    _ = try await audioRecordingService.stopRecording()
                } else {
                    try await audioRecordingService.startRecording()
                }
            } catch {
                self.error = error
            }
        }
    }

    public func togglePlayback() {
        Task {
            do {
                if isPlaying {
                    audioRecordingService.pausePlayback()
                } else {
                    try await audioRecordingService.startPlayback()
                }
            } catch {
                self.error = error
            }
        }
    }

    public func deleteRecording() {
        audioRecordingService.deleteRecording()
        audioSamples = []
    }

    public func playRecording(from url: URL) {
        // For now, just set error to nil and simulate playback, or extend protocol if needed
        // In a real implementation, you would call a method on the service to play from a specific URL
        // This is a placeholder for preview/testing
        // TODO: Extend AudioRecordingServiceProtocol if needed
    }
}

import Foundation
import SwiftUI

public class AchievementSharingViewModel: ObservableObject {
    @Published public var achievement: String
    @Published public var selectedTheme: String = "Classic"
    @Published public var selectedQuote: String = "Consistency is the key to success."
    @Published public var includeName: Bool = false
    @Published public var includeMetrics: Bool = true
    @Published public var includeDownloadLink: Bool = true
    public var availableThemes: [String] = ["Classic", "Modern", "Vibrant"]
    public var availableQuotes: [String] = [
        "Consistency is the key to success.",
        "Small steps every day.",
        "Believe in yourself!"
    ]
    public init(achievement: String = "Completed 100 Repetitions!") {
        self.achievement = achievement
    }
    public func shareAchievement() {}
} 
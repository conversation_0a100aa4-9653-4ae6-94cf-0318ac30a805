import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

@available(iOS 17.0, macOS 14.0, *)
public final class StreakVisualizationViewModel: ObservableObject {
    @Published public private(set) var streakDays: [StreakDay] = []
    @Published public private(set) var currentStreak: Int = 0
    @Published public private(set) var longestStreak: Int = 0
    @Published public private(set) var milestones: [StreakMilestone] = []
    @Published public private(set) var achievements: [StreakAchievement] = []
    @Published public private(set) var heatMapData: [StreakDay] = []
    @Published public var selectedMonth: Date = Date()

    private let streakService: StreakServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    public init(streakService: StreakServiceProtocol) {
        self.streakService = streakService
    }

    public func loadStreakData() {
        // Fetch streak data from the service (mocked for now)
        // In a real implementation, this would be async and fetch from persistence/network
        let today = Calendar.current.startOfDay(for: Date())
        var days: [StreakDay] = []
        for i in 0..<60 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: today)!
            let isComplete = Bool.random()
            let progress = isComplete ? 1.0 : Double.random(in: 0...0.8)
            days.append(
                StreakDay(
                    date: date,
                    progress: progress,
                    isComplete: isComplete,
                    repetitions: Int(progress * 10),
                    isInCurrentMonth: Calendar.current.isDate(
                        date, equalTo: selectedMonth, toGranularity: .month)
                ))
        }
        streakDays = days.sorted { $0.date < $1.date }
        currentStreak = calculateCurrentStreak(days: streakDays)
        longestStreak = calculateLongestStreak(days: streakDays)

        // Update milestones
        updateMilestones()

        // Update achievements
        updateAchievements()

        // Update heat map data
        updateHeatMapData()
    }

    private func updateMilestones() {
        var newMilestones: [StreakMilestone] = []

        // Current streak milestone
        newMilestones.append(
            StreakMilestone(
                type: .currentStreak,
                value: currentStreak,
                isAchieved: currentStreak > 0
            ))

        // Longest streak milestone
        newMilestones.append(
            StreakMilestone(
                type: .longestStreak,
                value: longestStreak,
                isAchieved: longestStreak > 0
            ))

        // Weekly milestones
        let weeklyMilestones = [7, 14, 21, 30]
        for milestone in weeklyMilestones {
            newMilestones.append(
                StreakMilestone(
                    type: .upcomingStreak,
                    value: milestone,
                    isAchieved: currentStreak >= milestone
                ))
        }

        milestones = newMilestones
    }

    private func updateAchievements() {
        var newAchievements: [StreakAchievement] = []

        // First week achievement
        newAchievements.append(
            StreakAchievement(
                title: "First Week",
                description: "Complete 7 days in a row",
                icon: "star.fill",
                isUnlocked: currentStreak >= 7
            ))

        // Two weeks achievement
        newAchievements.append(
            StreakAchievement(
                title: "Two Weeks Strong",
                description: "Maintain a 14-day streak",
                icon: "star.circle.fill",
                isUnlocked: currentStreak >= 14
            ))

        // Monthly achievement
        newAchievements.append(
            StreakAchievement(
                title: "Monthly Master",
                description: "Complete 30 days in a row",
                icon: "crown.fill",
                isUnlocked: currentStreak >= 30
            ))

        // Recovery achievement
        let hasRecovery = streakDays.contains { day in
            !day.isComplete && day.progress > 0
        }
        newAchievements.append(
            StreakAchievement(
                title: "Comeback Kid",
                description: "Recover from a broken streak",
                icon: "arrow.clockwise.circle.fill",
                isUnlocked: hasRecovery
            ))

        achievements = newAchievements
    }

    private func updateHeatMapData() {
        // Get the last 30 days for the heat map
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -29, to: today)!

        heatMapData =
            streakDays
            .filter { $0.date >= thirtyDaysAgo && $0.date <= today }
            .sorted { $0.date < $1.date }
    }

    private func calculateCurrentStreak(days: [StreakDay]) -> Int {
        var streak = 0
        for day in days.reversed() {
            if day.isComplete {
                streak += 1
            } else {
                break
            }
        }
        return streak
    }

    private func calculateLongestStreak(days: [StreakDay]) -> Int {
        var maxStreak = 0
        var current = 0
        for day in days {
            if day.isComplete {
                current += 1
                maxStreak = max(maxStreak, current)
            } else {
                current = 0
            }
        }
        return maxStreak
    }

    // MARK: - User Interactions

    /// Handle when a user taps on a specific day in the streak timeline
    /// - Parameter day: The streak day that was tapped
    public func handleDayTap(_ day: StreakDay) {
        // For now, just print the day that was tapped
        // In a real implementation, this could show details about that day's activities
        print(
            "Day tapped: \(day.date.formatted()), Complete: \(day.isComplete), Repetitions: \(day.repetitions)"
        )

        // You could also navigate to a detail view or show a modal with more information
        // This would typically be implemented by the parent view
    }

    // MARK: - Preview
    @MainActor
    public static var preview: StreakVisualizationViewModel {
        let vm = StreakVisualizationViewModel(streakService: MockStreakService())
        vm.loadStreakData()
        return vm
    }
}

// MARK: - Supporting Types

public struct StreakMilestone: Identifiable {
    public let id = UUID()
    public let type: MilestoneType
    public let value: Int
    public let isAchieved: Bool

    public enum MilestoneType {
        case currentStreak
        case longestStreak
        case completedCycles
        case upcomingStreak
    }
}

public struct StreakAchievement: Identifiable {
    public let id = UUID()
    public let title: String
    public let description: String
    public let icon: String
    public let isUnlocked: Bool
}

// MARK: - Mock Service for Preview
// UI-only build: Mark as @unchecked Sendable to suppress Sendable warnings
public class MockStreakService: StreakServiceProtocol {
    public init() {}
    public func validateStreaks() async throws -> StreakValidationResult {
        return StreakValidationResult(
            success: true, error: nil, validatedAffirmations: 1, brokenStreaks: 0)
    }
    public func getStreakStatistics() async throws -> StreakStatistics {
        return StreakStatistics()
    }
    public func getStreakCalendarData(for affirmation: any AffirmationProtocol, numberOfDays: Int)
        async -> [NeuroLoopInterfaces.StreakCalendarDay]
    {
        // Return empty or mock data
        return []
    }
    public func isStreakAtRisk(for affirmation: any AffirmationProtocol) async -> Bool { false }
    public func updateStreak(for affirmation: any AffirmationProtocol) async throws {}
    public func getStreakInfo(for affirmation: any AffirmationProtocol) async -> StreakInfo {
        // Provide mock values for all required fields
        return StreakInfo(
            currentStreak: 3,
            longestStreak: 7,
            completedCycles: 2,
            hasActiveCycle: true,
            cycleStartDate: Date(),
            lastRepetitionDate: Date()
        )
    }
    public func getStreakCalendar(for affirmation: any AffirmationProtocol) async
        -> [NeuroLoopInterfaces.StreakCalendarDay]
    {
        // Return empty or mock data
        return []
    }
    public func validateStreak(for affirmation: any AffirmationProtocol) async -> Bool { false }
    public func resetStreak(for affirmation: any AffirmationProtocol) async throws {}
}

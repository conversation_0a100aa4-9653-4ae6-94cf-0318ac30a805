import AVFoundation
import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes
import Speech
import Swift<PERSON>

/**
 * FixedSpeakAffirmationViewModel
 *
 * A modified version of SpeakAffirmationViewModel that addresses issues with
 * Debug Mode and repetition counting. This version ensures that:
 *
 * 1. Debug Mode properly bypasses speech recognition
 * 2. The repetition counter increments correctly
 * 3. Audio levels are properly detected and displayed
 */

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public class FixedSpeakAffirmationViewModel: ObservableObject {
    // MARK: - Published Properties

    @Published private(set) var affirmation: any AffirmationProtocol
    @Published public var todayRepetitions: Int = 0
    @Published private(set) var totalRepetitions: Int = 100
    @Published private(set) var currentDay: Int = 1
    @Published private(set) var isRecording: Bool = false
    @Published private(set) var isPlaying: Bool = false
    @Published private(set) var spokenText: String = ""
    @Published public var partialRecognitionText: String = ""
    @Published var alertItem: AlertItem?
    @Published public var currentAudioLevel: Double = 0.0

    // Debug options
    public var debugBypassSpeechRecognition: Bool = false {
        didSet {
            // When debug mode changes, update the repetition service
            if oldValue != debugBypassSpeechRecognition {
                print(
                    "FixedSpeakAffirmationViewModel: Debug mode changed to \(debugBypassSpeechRecognition)"
                )
                Task {
                    await updateRepetitionService()
                }
            }
        }
    }
    public var showRecognizedTextOnUI: Bool = true

    // MARK: - Properties

    private var repetitionService: any RepetitionServiceProtocol
    public let audioService: any AudioRecordingServiceProtocol
    private let affirmationService: any AffirmationServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    // Reference to the tracking view model for syncing repetition counts
    public var trackingViewModel: RepetitionTrackingViewModel?

    // Speech Recognition Properties
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()

    // Alert management
    private var alertDebounceTask: Task<Void, Never>?

    // Prevent multiple rapid calls
    private var isStoppingRecording = false

    // Audio session manager
    private let audioSessionManager = AudioSessionManager.shared

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: any RepetitionServiceProtocol,
        audioService: any AudioRecordingServiceProtocol,
        affirmationService: any AffirmationServiceProtocol
    ) {
        self.affirmation = affirmation
        self.repetitionService = repetitionService
        self.audioService = audioService
        self.affirmationService = affirmationService

        // Print service types for debugging
        print("FixedSpeakAffirmationViewModel: Initialized with services:")
        print("- Repetition Service: \(type(of: repetitionService))")
        print("- Audio Service: \(type(of: audioService))")
        print("- Affirmation Service: \(type(of: affirmationService))")

        // Initialize with the current repetition count
        self.todayRepetitions = affirmation.currentRepetitions
    }

    public func initialize() async {
        // Initialize with current progress
        await loadProgress()

        // Pre-warm permissions and services
        await prewarmServices()
    }

    private func prewarmServices() async {
        // Check and request permissions only if not already granted
        let permissionGranted = await audioSessionManager.checkMicrophonePermission()
        if !permissionGranted {
            // Request permission once and store the result
            let granted = await audioSessionManager.requestMicrophonePermission()
            if !granted {
                // Show a single, clear message about permissions
                alertItem = AlertItem(
                    title: "Microphone Access Required",
                    message:
                        "Please enable microphone access in Settings to use this feature. You'll only need to do this once."
                )
            }
        }

        // Pre-warm speech recognition
        await prewarmSpeechRecognition()
    }

    private func prewarmSpeechRecognition() async {
        print("FixedSpeakAffirmationViewModel: Pre-warming speech recognition")

        // Request speech recognition authorization in advance
        if SFSpeechRecognizer.authorizationStatus() == .notDetermined {
            await withCheckedContinuation { continuation in
                SFSpeechRecognizer.requestAuthorization { _ in
                    continuation.resume()
                }
            }
        }

        // Initialize the speech recognizer if needed
        if speechRecognizer == nil {
            let preferredLocale = Locale.current
            let localeIdentifier = preferredLocale.language.languageCode?.identifier ?? "en"
            let recognizerLocale = Locale(identifier: "\(localeIdentifier)-US")
            speechRecognizer = SFSpeechRecognizer(locale: recognizerLocale)
        }

        // Pre-configure audio session
        _ = await configureAudioSession(active: false)
    }

    /// Cleans up resources when the view model is no longer needed
    public func cleanup() async {
        print("FixedSpeakAffirmationViewModel: cleanup called")

        // Stop any ongoing recording
        if isRecording {
            print("FixedSpeakAffirmationViewModel: Stopping recording during cleanup")
            await stopRecording()
        }

        // Stop audio monitoring
        stopMonitoringAudioLevels()
        print("FixedSpeakAffirmationViewModel: Stopped audio monitoring")

        // Stop speech recognition if active
        await stopSpeechRecognition()
        print("FixedSpeakAffirmationViewModel: Stopped speech recognition")

        // Clean up any other resources
        print("FixedSpeakAffirmationViewModel: Cleanup complete")
    }

    /// Updates the repetition service based on the current debug mode
    @MainActor
    private func updateRepetitionService() async {
        do {
            // Get the appropriate repetition service based on debug mode
            if #available(iOS 17.0, macOS 14.0, *) {
                let factory = ServiceFactory.shared
                let newService = try factory.getRepetitionServiceForMode(
                    debugMode: debugBypassSpeechRecognition)

                // Update the service
                repetitionService = newService
                print(
                    "FixedSpeakAffirmationViewModel: Updated repetition service to \(type(of: newService))"
                )
                print("FixedSpeakAffirmationViewModel: Debug mode: \(debugBypassSpeechRecognition)")

                // Force UI update
                objectWillChange.send()
            }
        } catch {
            print(
                "FixedSpeakAffirmationViewModel: Error updating repetition service: \(error.localizedDescription)"
            )
        }
    }

    // MARK: - Public Methods

    /// Starts recording the user's spoken affirmation
    public func startRecording() {
        print("FixedSpeakAffirmationViewModel: startRecording called")

        Task {
            do {
                // Clear previous spoken text
                spokenText = ""
                partialRecognitionText = ""

                // Start recording
                try await audioService.startRecording()
                isRecording = true

                // Start monitoring audio levels
                startMonitoringAudioLevels()

                // Start speech recognition if not in debug mode
                if !debugBypassSpeechRecognition {
                    await startSpeechRecognition()
                } else {
                    print(
                        "FixedSpeakAffirmationViewModel: Debug mode enabled, skipping speech recognition"
                    )
                }
            } catch {
                print(
                    "FixedSpeakAffirmationViewModel: Error starting recording: \(error.localizedDescription)"
                )
                isRecording = false
                setAlert(AlertItem(
                    title: "Recording Error",
                    message: "Could not start recording: \(error.localizedDescription)"
                ))
            }
        }
    }

    /// Stops recording and processes the spoken affirmation
    public func stopRecording() async {
        print("FixedSpeakAffirmationViewModel: stopRecording called")

        // Prevent multiple rapid calls
        guard !isStoppingRecording else {
            print("FixedSpeakAffirmationViewModel: Already stopping recording, ignoring call")
            return
        }

        isStoppingRecording = true
        defer { isStoppingRecording = false }

        print(
            "FixedSpeakAffirmationViewModel: Current state - isRecording: \(isRecording), debugMode: \(debugBypassSpeechRecognition)"
        )
        print("FixedSpeakAffirmationViewModel: Spoken text: \"\(spokenText)\"")
        print("FixedSpeakAffirmationViewModel: Target text: \"\(affirmation.text)\"")

        // Add a small delay to ensure all speech is captured
        print(
            "FixedSpeakAffirmationViewModel: Adding 0.8 second delay before stopping recording to ensure all speech is captured"
        )
        try? await Task.sleep(nanoseconds: 800_000_000)  // 0.8 second delay

        // Capture the current spoken text in case it changes during async operations
        let capturedSpokenText = spokenText
        print("FixedSpeakAffirmationViewModel: Captured spoken text: \"\(capturedSpokenText)\"")

        // Stop speech recognition
        await stopSpeechRecognition()

        // Stop audio recording
        do {
            // Try to stop recording, but handle errors gracefully
            let result = try await audioService.stopRecording()
            print(
                "FixedSpeakAffirmationViewModel: Recording stopped successfully, result: \(result)")

            // Update UI
            isRecording = false

            // In debug mode, always count the repetition
            if debugBypassSpeechRecognition {
                print(
                    "FixedSpeakAffirmationViewModel: Debug mode enabled, automatically counting repetition"
                )
                // Add a small delay before recording repetition to ensure UI is ready
                try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
                print("FixedSpeakAffirmationViewModel: Delay complete, now recording repetition")
                await recordRepetition()
            } else if !capturedSpokenText.isEmpty {
                // Verify the spoken text matches the affirmation
                print(
                    "FixedSpeakAffirmationViewModel: Verifying spoken text against target affirmation"
                )
                let verificationResult = verifyAffirmation(
                    spokenText: capturedSpokenText,
                    targetText: affirmation.text
                )

                if verificationResult.success {
                    print(
                        "FixedSpeakAffirmationViewModel: Verification successful, recording repetition"
                    )
                    // Add a small delay before recording repetition to ensure UI is ready
                    try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
                    print(
                        "FixedSpeakAffirmationViewModel: Delay complete, now recording repetition")
                    await recordRepetition()
                } else {
                    print(
                        "FixedSpeakAffirmationViewModel: Verification failed, not counting repetition"
                    )
                    print(
                        "FixedSpeakAffirmationViewModel: Similarity: \(verificationResult.similarity * 100)%"
                    )

                    // Show a more helpful error message with the similarity percentage
                    let similarityPercent = Int(verificationResult.similarity * 100)
                    setAlert(AlertItem(
                        title: "Not Quite Right",
                        message:
                            "Your spoken text was \(similarityPercent)% similar to the affirmation. Please try again and speak the complete affirmation clearly."
                    ))
                }
            } else {
                print("FixedSpeakAffirmationViewModel: No spoken text detected")
                setAlert(AlertItem(
                    title: "No Speech Detected",
                    message: "Please speak the affirmation clearly when recording."
                ))
            }
        } catch {
            print(
                "FixedSpeakAffirmationViewModel: Error stopping recording: \(error.localizedDescription)"
            )

            // Update UI state
            isRecording = false

            // Log detailed error information for debugging
            print("❌ Error type: \(type(of: error))")
            print("❌ Error details: \(error)")

            // Try to get more information about the error
            if let nsError = error as NSError? {
                print("❌ NSError domain: \(nsError.domain)")
                print("❌ NSError code: \(nsError.code)")
                print("❌ NSError userInfo: \(nsError.userInfo)")

                // Don't show error alerts for benign "operation couldn't be completed" errors
                // These often happen when stopping recording that's already stopped
                if nsError.domain == "NeuroLoopCore.AudioRecordingError" && nsError.code == 7 {
                    print("FixedSpeakAffirmationViewModel: Ignoring benign recording stop error")
                    return
                }
            }

            // Only show error alert for serious errors
            setAlert(AlertItem(
                title: "Recording Error",
                message: "Error stopping recording: \(error.localizedDescription)"
            ))
            print(
                "FixedSpeakAffirmationViewModel: Showing error alert: \(error.localizedDescription)"
            )

            // In debug mode, still count the repetition even if there was an error
            if debugBypassSpeechRecognition {
                print(
                    "FixedSpeakAffirmationViewModel: Debug mode enabled, counting repetition despite error"
                )
                Task {
                    // Add a small delay before recording repetition
                    try? await Task.sleep(nanoseconds: 300_000_000)  // 0.3 second delay
                    await recordRepetition()
                }
            }
        }
    }

    /// Plays the recorded affirmation
    public func playRecording() async {
        print("FixedSpeakAffirmationViewModel: playRecording called")

        do {
            isPlaying = true
            try await audioService.startPlayback()
            isPlaying = false
        } catch {
            print(
                "FixedSpeakAffirmationViewModel: Error playing recording: \(error.localizedDescription)"
            )
            isPlaying = false
            setAlert(AlertItem(
                title: "Playback Error",
                message: "Could not play recording: \(error.localizedDescription)"
            ))
        }
    }

    /// Resets the repetition count for the current affirmation
    public func resetRepetitionCount() async {
        print("FixedSpeakAffirmationViewModel: resetRepetitionCount called")

        do {
            // Start a new cycle for the affirmation
            let result = try await repetitionService.startCycle(for: affirmation)

            // Update the affirmation
            affirmation = result.updatedAffirmation

            // Update the UI
            todayRepetitions = affirmation.currentRepetitions

            // Update the tracking view model if available
            if let trackingVM = trackingViewModel {
                // Reload data in the tracking view model to ensure it has the latest state
                await trackingVM.loadData()
                print(
                    "FixedSpeakAffirmationViewModel: Called loadData on trackingViewModel after reset"
                )

                // Make sure the counts are in sync
                if trackingVM.todayRepetitions != todayRepetitions {
                    trackingVM.updateRepetitionCount(todayRepetitions)
                    print(
                        "FixedSpeakAffirmationViewModel: Updated trackingViewModel with count \(todayRepetitions) after reset"
                    )
                }
            } else {
                print(
                    "FixedSpeakAffirmationViewModel: No trackingViewModel available to update after reset"
                )
            }

            objectWillChange.send()

            // Show success message
            alertItem = AlertItem(
                title: "Reset Complete",
                message: "Repetition count has been reset to 0."
            )
        } catch {
            print(
                "FixedSpeakAffirmationViewModel: Error resetting repetition count: \(error.localizedDescription)"
            )
            alertItem = AlertItem(
                title: "Reset Error",
                message: "Could not reset repetition count: \(error.localizedDescription)"
            )
        }
    }

    /// Forces an increment of the repetition count for testing
    public func forceIncrementRepetition() async {
        print("⚡️ FORCE INCREMENT: Manual repetition increment triggered")
        print("⚡️ Current count before increment: \(todayRepetitions)")
        print("⚡️ Current tracking count: \(trackingViewModel?.todayRepetitions ?? -1)")
        print("⚡️ Current affirmation: \"\(affirmation.text)\"")
        print("⚡️ Affirmation ID: \(affirmation.id)")

        // Use the debug repetition service directly
        do {
            if let factory = try? ServiceFactory.shared {
                let debugService = try factory.getRepetitionServiceForMode(debugMode: true)
                print("⚡️ Got debug service: \(type(of: debugService))")

                // Record repetition using the debug service
                let result = try await debugService.recordRepetition(for: affirmation)
                let newCount = result.updatedAffirmation.currentRepetitions
                print("⚡️ Incremented count to \(newCount) using debug service")

                // Update the affirmation with the result
                affirmation = result.updatedAffirmation
                print("⚡️ Updated affirmation with result")

                // Add a small delay to ensure UI is ready
                try? await Task.sleep(nanoseconds: 500_000_000)  // 0.5 second delay
                print("⚡️ Delay complete, now updating UI")

                await MainActor.run {
                    // Verify the count is correct by checking the affirmation object directly
                    let verifiedCount = affirmation.currentRepetitions

                    // Check for count discrepancy
                    if newCount != verifiedCount {
                        print(
                            "⚠️ Count discrepancy detected in forceIncrementRepetition - newCount: \(newCount), affirmation.currentRepetitions: \(verifiedCount)"
                        )
                        // Use the verified count from the affirmation object
                        todayRepetitions = verifiedCount
                        print("⚠️ Using verified count from affirmation: \(verifiedCount)")
                    } else {
                        // Update the repetition count
                        todayRepetitions = newCount
                        print("⚡️ Updated todayRepetitions to \(todayRepetitions)")
                    }

                    // Update the tracking view model if available
                    if let trackingVM = trackingViewModel {
                        // Update with the new affirmation object to ensure consistency
                        trackingVM.updateAffirmation(affirmation)
                        print("⚡️ Updated trackingViewModel with new affirmation object")
                    } else {
                        print("⚠️ No trackingViewModel available to update")
                    }

                    // Force UI update
                    objectWillChange.send()
                    print("⚡️ Sent objectWillChange notification")

                    // Post a notification to ensure all observers are updated
                    NotificationCenter.default.post(
                        name: Notification.Name("RepetitionCountChanged"),
                        object: nil,
                        userInfo: [
                            "affirmationId": affirmation.id,
                            "count": newCount,
                        ]
                    )
                    print("⚡️ Posted RepetitionCountChanged notification")

                    // Send another objectWillChange after a small delay to ensure UI updates
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        self.objectWillChange.send()
                        print("⚡️ Sent delayed objectWillChange notification")

                        // Post another notification after a delay to ensure all observers are updated
                        NotificationCenter.default.post(
                            name: Notification.Name("RepetitionCountChanged"),
                            object: nil,
                            userInfo: [
                                "affirmationId": self.affirmation.id,
                                "count": newCount,
                            ]
                        )
                        print("⚡️ Posted delayed RepetitionCountChanged notification")
                    }
                }
            }
        } catch {
            print("⚡️ Error during force increment: \(error)")

            // Fallback to the original method if the direct approach fails
            print("⚡️ Falling back to original method")

            // Record the repetition using the standard method
            await recordRepetition()
            print("⚡️ Recorded repetition using standard method")
        }
    }

    // MARK: - Private Methods

    /// Records a repetition for the current affirmation
    private func recordRepetition() async {
        print("⭐️ RECORDING REPETITION: Starting process")
        print("⭐️ Current affirmation: \"\(affirmation.text)\"")
        print("⭐️ Affirmation ID: \(affirmation.id)")

        do {
            // Store the current repetition count for verification
            let beforeCount = affirmation.currentRepetitions
            print("⭐️ Before repetition - count: \(beforeCount)")

            // Record the repetition
            print("⭐️ Calling repetitionService.recordRepetition")
            print("⭐️ Using service: \(type(of: repetitionService))")
            let result = try await repetitionService.recordRepetition(for: affirmation)
            print("⭐️ Repetition recorded successfully")

            // Update the affirmation with the result
            affirmation = result.updatedAffirmation
            print("⭐️ Updated affirmation with result")
            print("⭐️ Updated affirmation ID: \(affirmation.id)")

            // Get the updated repetition count
            let afterCount = affirmation.currentRepetitions
            print(
                "⭐️ After repetition - count: \(afterCount), difference: \(afterCount - beforeCount)"
            )

            // Verify the count actually changed
            if afterCount <= beforeCount {
                print(
                    "⚠️ WARNING: Repetition count did not increase! Before: \(beforeCount), After: \(afterCount)"
                )
                print("⚠️ This indicates a problem with the repetition service")
            } else {
                print("⭐️ Repetition count increased successfully")
            }

            // Add a small delay to ensure UI is ready
            try? await Task.sleep(nanoseconds: 500_000_000)  // 0.5 second delay
            print("⭐️ Delay complete, now updating UI")

            // Update the UI on the main thread
            await MainActor.run {
                print("⭐️ Updating UI on main thread")

                // Update the repetition count
                todayRepetitions = afterCount
                print("⭐️ Updated todayRepetitions to \(afterCount)")

                // Update the tracking view model if available
                if let trackingVM = trackingViewModel {
                    // Update with the new affirmation object to ensure consistency
                    trackingVM.updateAffirmation(affirmation)
                    print("⭐️ Updated trackingViewModel with new affirmation object")
                } else {
                    print("⚠️ No trackingViewModel available to update")
                }

                // Force UI update
                objectWillChange.send()
                print("⭐️ Sent objectWillChange notification")

                // Post a notification to ensure all observers are updated
                NotificationCenter.default.post(
                    name: Notification.Name("RepetitionCountChanged"),
                    object: nil,
                    userInfo: [
                        "affirmationId": affirmation.id,
                        "count": afterCount,
                        "timestamp": Date().timeIntervalSince1970,
                        "uuid": UUID().uuidString,
                    ]
                )
                print("⭐️ Posted RepetitionCountChanged notification")

                // Send another objectWillChange after a small delay to ensure UI updates
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    self.objectWillChange.send()
                    print("⭐️ Sent delayed objectWillChange notification")

                    // Post another notification after a delay to ensure all observers are updated
                    NotificationCenter.default.post(
                        name: Notification.Name("RepetitionCountChanged"),
                        object: nil,
                        userInfo: [
                            "affirmationId": self.affirmation.id,
                            "count": afterCount,
                            "timestamp": Date().timeIntervalSince1970,
                            "uuid": UUID().uuidString,
                        ]
                    )
                    print("⭐️ Posted delayed RepetitionCountChanged notification")
                }
            }

            // Show success message after a small delay to ensure UI has updated
            try? await Task.sleep(nanoseconds: 200_000_000)  // 0.2 second delay
            await MainActor.run {
                // Verify the count is correct by checking the affirmation object directly
                let verifiedCount = affirmation.currentRepetitions

                // Make sure todayRepetitions and afterCount are in sync with the affirmation
                if todayRepetitions != verifiedCount || afterCount != verifiedCount {
                    print(
                        "⚠️ Count discrepancy detected - todayRepetitions: \(todayRepetitions), afterCount: \(afterCount), affirmation.currentRepetitions: \(verifiedCount)"
                    )
                    todayRepetitions = verifiedCount
                    print("⚠️ Fixed count discrepancy - todayRepetitions now: \(todayRepetitions)")
                }

                // Create a success message with the verified count
                let successMessage =
                    "Repetition count increased to \(verifiedCount) out of \(totalRepetitions)."

                // Show the alert using the safe method
                setAlert(AlertItem(
                    title: "Success!",
                    message: successMessage
                ))
                print("🎉 SUCCESS: Displayed success alert")
                print("🎉 Message: \(successMessage)")

                // Log the final state for debugging
                print(
                    "🎉 FINAL STATE: todayRepetitions=\(todayRepetitions), totalRepetitions=\(totalRepetitions)"
                )
                print(
                    "🎉 FINAL STATE: affirmation.currentRepetitions=\(affirmation.currentRepetitions)"
                )
            }
        } catch {
            print("❌ ERROR: Failed to record repetition")
            print("❌ Error description: \(error.localizedDescription)")

            // Log detailed error information for debugging
            print("❌ Error type: \(type(of: error))")
            print("❌ Error details: \(error)")

            // Try to get more information about the error
            if let nsError = error as NSError? {
                print("❌ NSError domain: \(nsError.domain)")
                print("❌ NSError code: \(nsError.code)")
                print("❌ NSError userInfo: \(nsError.userInfo)")
            }

            // Show error alert on main thread
            await MainActor.run {
                // Create a user-friendly error message
                let errorMessage = "Could not record repetition: \(error.localizedDescription)"

                // Show the alert
                alertItem = AlertItem(
                    title: "Error Recording Repetition",
                    message: errorMessage
                )
                print("❌ Displayed error alert: \(errorMessage)")

                // Force UI update even on error
                objectWillChange.send()
                print("❌ Sent objectWillChange notification after error")

                // Post an error notification to ensure views are updated
                let notificationName = Notification.Name("RepetitionCountChanged")
                let userInfo: [String: Any] = [
                    "count": affirmation.currentRepetitions,
                    "affirmationId": affirmation.id,
                    "currentDay": currentDay,
                    "timestamp": Date().timeIntervalSince1970,
                    "uuid": UUID().uuidString,
                    "error": true,
                    "errorMessage": error.localizedDescription,
                ]

                NotificationCenter.default.post(
                    name: notificationName,
                    object: nil,
                    userInfo: userInfo
                )
                print("❌ Posted error notification to update views")
            }
        }
    }

    /// Loads the current progress for the affirmation
    private func loadProgress() async {
        print("FixedSpeakAffirmationViewModel: loadProgress called")

        // Update the repetition count
        todayRepetitions = affirmation.currentRepetitions

        // Get the progress info
        let progressInfo = repetitionService.getProgress(for: affirmation)

        // Update the UI
        totalRepetitions = progressInfo.totalRepetitions
        currentDay = progressInfo.currentDay

        // Update the tracking view model if available
        if let trackingVM = trackingViewModel {
            // Load data in the tracking view model to ensure it has the latest state
            await trackingVM.loadData()
            print("FixedSpeakAffirmationViewModel: Called loadData on trackingViewModel")

            // Make sure the counts are in sync
            if trackingVM.todayRepetitions != todayRepetitions {
                trackingVM.updateRepetitionCount(todayRepetitions)
                print(
                    "FixedSpeakAffirmationViewModel: Updated trackingViewModel with count \(todayRepetitions)"
                )
            }
        } else {
            print("FixedSpeakAffirmationViewModel: No trackingViewModel available to update")
        }

        // Force UI update
        objectWillChange.send()
    }

    /// Starts monitoring audio levels
    private func startMonitoringAudioLevels() {
        print("FixedSpeakAffirmationViewModel: startMonitoringAudioLevels called")

        // Create a timer to update the audio level
        Timer.publish(every: 0.1, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                guard let self = self, self.isRecording else { return }
                self.updateAudioLevel()
            }
            .store(in: &cancellables)
    }

    /// Updates the audio level
    private func updateAudioLevel() {
        // Get the current audio level from the audio service
        currentAudioLevel = audioService.recordingPower
    }

    /// Stops monitoring audio levels
    private func stopMonitoringAudioLevels() {
        print("FixedSpeakAffirmationViewModel: stopMonitoringAudioLevels called")

        // Cancel all audio monitoring tasks
        cancellables.forEach { $0.cancel() }
        cancellables.removeAll()

        // Reset audio level
        currentAudioLevel = 0.0

        print("FixedSpeakAffirmationViewModel: Audio monitoring stopped")
    }

    /// Verifies that the spoken text matches the target text
    private func verifyAffirmation(spokenText: String, targetText: String) -> (
        success: Bool, similarity: Double
    ) {
        print("🔍 VERIFICATION START: Checking if spoken text matches target")
        print("🔍 Spoken text: \"\(spokenText)\"")
        print("🔍 Target text: \"\(targetText)\"")

        // If debug mode is enabled, always return success
        if debugBypassSpeechRecognition {
            print("🔍 DEBUG MODE ENABLED: Forcing verification success")
            return (true, 1.0)
        }

        // Normalize both texts for comparison
        let normalizedSpoken = spokenText.lowercased().trimmingCharacters(
            in: .whitespacesAndNewlines)
        let normalizedTarget = targetText.lowercased().trimmingCharacters(
            in: .whitespacesAndNewlines)
        print("🔍 Normalized spoken: \"\(normalizedSpoken)\"")
        print("🔍 Normalized target: \"\(normalizedTarget)\"")

        // Safety check for empty strings
        if normalizedSpoken.isEmpty || normalizedTarget.isEmpty {
            print("🔍 Empty string detected, returning no match")
            return (false, 0.0)
        }

        // Calculate similarity using word overlap for better accuracy with natural speech
        let similarity = calculateWordOverlapSimilarity(normalizedSpoken, normalizedTarget)
        print("🔍 Word overlap similarity: \(Int(similarity * 100))%")

        // For very short affirmations (1-3 words), use a higher threshold
        // For longer affirmations, use a lower threshold to be more forgiving
        let words = normalizedTarget.components(separatedBy: .whitespacesAndNewlines).filter {
            !$0.isEmpty
        }
        let wordCount = words.count

        // Adjust threshold based on word count
        var threshold = 0.6  // Default threshold (60%)
        if wordCount <= 3 {
            threshold = 0.7  // Higher threshold (70%) for short affirmations
        } else if wordCount >= 8 {
            threshold = 0.5  // Lower threshold (50%) for long affirmations
        }
        print("🔍 Word count: \(wordCount), Threshold: \(Int(threshold * 100))%")

        // Consider it a match if similarity is above the threshold
        let isMatch = similarity >= threshold
        print("🔍 VERIFICATION RESULT: \(isMatch ? "✅ SUCCESS" : "❌ FAILED")")
        print("🔍 Similarity: \(Int(similarity * 100))%, Threshold: \(Int(threshold * 100))%")

        // For debugging, print the words that were matched and missed
        let spokenWords = normalizedSpoken.components(separatedBy: .whitespacesAndNewlines).filter {
            !$0.isEmpty
        }
        let targetWords = normalizedTarget.components(separatedBy: .whitespacesAndNewlines).filter {
            !$0.isEmpty
        }

        print("🔍 Spoken words: \(spokenWords.count) - \(spokenWords.joined(separator: ", "))")
        print("🔍 Target words: \(targetWords.count) - \(targetWords.joined(separator: ", "))")

        // If we're close but not quite there, lower the threshold slightly to be more forgiving
        if !isMatch && similarity >= (threshold - 0.1) {
            print("🔍 Very close match! Lowering threshold slightly")
            return (true, similarity)
        }

        return (isMatch, similarity)
    }

    /// Calculates the similarity between two strings using word overlap
    /// This is more appropriate for speech recognition as it focuses on matching words
    /// rather than exact character sequences
    private func calculateWordOverlapSimilarity(_ s1: String, _ s2: String) -> Double {
        // Safety check
        if s1.isEmpty || s2.isEmpty {
            return 0.0
        }

        // If strings are identical, return 1.0
        if s1 == s2 {
            return 1.0
        }

        // Split into words and create sets
        let words1 = Set(s1.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty })
        let words2 = Set(s2.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty })

        // Safety check for empty sets
        if words1.isEmpty || words2.isEmpty {
            return 0.0
        }

        // Calculate Jaccard similarity (intersection over union)
        let intersection = words1.intersection(words2).count
        let union = words1.union(words2).count

        let similarity = Double(intersection) / Double(union)
        print("- Words in spoken text: \(words1.count)")
        print("- Words in target text: \(words2.count)")
        print("- Matching words: \(intersection)")
        print("- Total unique words: \(union)")

        return similarity
    }

    /// Legacy Levenshtein distance calculation - kept for reference
    private func calculateLevenshteinSimilarity(between first: String, and second: String) -> Double
    {
        // If either string is empty, return 0
        if first.isEmpty || second.isEmpty {
            return 0.0
        }

        // If the strings are identical, return 1
        if first == second {
            return 1.0
        }

        // Calculate Levenshtein distance
        let firstCount = first.count
        let secondCount = second.count
        var distance = Array(
            repeating: Array(repeating: 0, count: secondCount + 1), count: firstCount + 1)

        for i in 0...firstCount {
            distance[i][0] = i
        }

        for j in 0...secondCount {
            distance[0][j] = j
        }

        for i in 1...firstCount {
            for j in 1...secondCount {
                let firstIndex = first.index(first.startIndex, offsetBy: i - 1)
                let secondIndex = second.index(second.startIndex, offsetBy: j - 1)

                if first[firstIndex] == second[secondIndex] {
                    distance[i][j] = distance[i - 1][j - 1]
                } else {
                    distance[i][j] = min(
                        distance[i - 1][j] + 1,  // Deletion
                        distance[i][j - 1] + 1,  // Insertion
                        distance[i - 1][j - 1] + 1  // Substitution
                    )
                }
            }
        }

        // Calculate similarity as 1 - normalized distance
        let maxLength = max(firstCount, secondCount)
        let normalizedDistance = Double(distance[firstCount][secondCount]) / Double(maxLength)
        return 1.0 - normalizedDistance
    }

    /// Starts speech recognition
    private func startSpeechRecognition() async {
        print("FixedSpeakAffirmationViewModel: Starting speech recognition")

        // Initialize speech recognizer with user's locale or fall back to US English
        let preferredLocale = Locale.current
        let localeIdentifier = preferredLocale.language.languageCode?.identifier ?? "en"
        let recognizerLocale = Locale(identifier: "\(localeIdentifier)-US")

        print("FixedSpeakAffirmationViewModel: Using locale: \(recognizerLocale.identifier)")
        speechRecognizer = SFSpeechRecognizer(locale: recognizerLocale)

        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("FixedSpeakAffirmationViewModel: Speech recognizer not available")
            // Don't show alert, just log the error
            return
        }

        // Configure audio session
        let audioSessionSuccess = await configureAudioSession(active: true)
        if !audioSessionSuccess {
            print("FixedSpeakAffirmationViewModel: Failed to configure audio session")
            // Don't show alert, just log the error
            return
        }

        // Create recognition request
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            print("FixedSpeakAffirmationViewModel: Failed to create recognition request")
            return
        }

        // Configure recognition request
        recognitionRequest.shouldReportPartialResults = true

        // Set contextual strings to help with recognition
        recognitionRequest.contextualStrings = [affirmation.text]

        // Install tap on audio engine input
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) {
            [weak self] buffer, _ in
            self?.recognitionRequest?.append(buffer)
        }

        // Start recognition task
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) {
            [weak self] result, error in
            guard let self = self else { return }

            if let error = error {
                print(
                    "FixedSpeakAffirmationViewModel: Speech recognition error: \(error.localizedDescription)"
                )
                // Only show critical errors that affect functionality
                let speechError = error as NSError
                let errorCode = speechError.code
                // SFSpeechRecognizerError codes: 203 = taskNotValid, 204 = invalidRecognitionMetadata
                if errorCode == 203 || errorCode == 204 {
                    setAlert(AlertItem(
                        title: "Speech Recognition Error",
                        message: "Please try again. If the problem persists, restart the app."
                    ))
                }
                return
            }

            if let result = result {
                let recognizedText = result.bestTranscription.formattedString
                print("FixedSpeakAffirmationViewModel: Recognized text: \(recognizedText)")

                Task { @MainActor in
                    self.spokenText = recognizedText
                    self.partialRecognitionText = recognizedText
                }
            }
        }

        // Start audio engine
        do {
            try audioEngine.start()
            print("FixedSpeakAffirmationViewModel: Audio engine started")
        } catch {
            print(
                "FixedSpeakAffirmationViewModel: Failed to start audio engine: \(error.localizedDescription)"
            )
            // Don't show alert for non-critical errors
        }
    }

    /// Stops speech recognition
    private func stopSpeechRecognition() async {
        print("FixedSpeakAffirmationViewModel: Stopping speech recognition")

        // Stop audio engine
        if audioEngine.isRunning {
            audioEngine.stop()
            audioEngine.inputNode.removeTap(onBus: 0)
            print("FixedSpeakAffirmationViewModel: Audio engine stopped")
        }

        // Cancel recognition task
        if let recognitionTask = recognitionTask {
            recognitionTask.cancel()
            self.recognitionTask = nil
            print("FixedSpeakAffirmationViewModel: Recognition task cancelled")
        }

        // Reset recognition request
        recognitionRequest = nil

        // Deactivate audio session
        _ = await configureAudioSession(active: false)
    }

    /// Configures the audio session
    private func configureAudioSession(active: Bool) async -> Bool {
        #if os(iOS)
            if active {
                return await audioSessionManager.activateSession(
                    category: .playAndRecord,
                    mode: .spokenAudio,
                    options: [.allowBluetooth, .defaultToSpeaker]
                )
            } else {
                return await audioSessionManager.deactivateSession()
            }
        #else
            // On macOS, just return true
            return true
        #endif
    }

    // MARK: - Alert Management

    /// Safely sets an alert item with debouncing to prevent conflicts
    private func setAlert(_ alert: AlertItem) {
        // Cancel any pending alert task
        alertDebounceTask?.cancel()

        // Create a new debounced task
        alertDebounceTask = Task { @MainActor in
            // Clear any existing alert first
            self.alertItem = nil

            // Small delay to ensure clean state
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

            // Check if task was cancelled
            guard !Task.isCancelled else { return }

            // Set the new alert
            self.alertItem = alert
        }
    }

    // MARK: - Cleanup

    deinit {
        print("FixedSpeakAffirmationViewModel: deinit called")
        alertDebounceTask?.cancel()
    }
}

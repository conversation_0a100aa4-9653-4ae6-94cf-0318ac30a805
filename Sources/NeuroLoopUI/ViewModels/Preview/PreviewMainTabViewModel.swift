import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public class PreviewMainTabViewModel: ObservableObject {
    @Published public var selectedTab: MainTab = .home

    public init() {
        // Initialize with default values
    }
    @MainActor
    public static func createViewModel() async -> MainTabViewModel {
        // Create the view model with default constructor
        // This uses the MainTabViewModel from NeuroLoopUI, not the one from NeuroLoopApp
        return await MainTabViewModel()
    }

    @MainActor
    public static var shared: MainTabViewModel?

    @MainActor
    public static func getShared() async -> MainTabViewModel {
        if shared == nil {
            shared = await createViewModel()
        }
        return shared!
    }

    @MainActor
    public static func loadData() async {
        // No async operations needed for preview
        // If MainTabViewModel doesn't have a loadData method, we'll just do nothing
        // This is a placeholder for any initialization that might be needed
        _ = await getShared()
    }
}

import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

@available(iOS 17.0, macOS 14.0, *)
public final class PreviewAffirmationListViewModel: ObservableObject {
    @Published public private(set) var affirmations: [any AffirmationProtocol] = []
    @Published public private(set) var selectedAffirmation: (any AffirmationProtocol)?
    @Published public private(set) var isLoading: Bool = false
    @Published public private(set) var error: Error?

    let repository: AffirmationRepositoryProtocol

    public init(repository: AffirmationRepositoryProtocol) {
        self.repository = repository
    }

    @MainActor
    public func loadAffirmations() async {
        isLoading = true
        error = nil

        do {
            affirmations = try await repository.fetchAffirmations()
        } catch {
            self.error = error
        }

        isLoading = false
    }

    @MainActor
    public func loadAffirmations(category: AffirmationCategory) async {
        isLoading = true
        error = nil

        do {
            affirmations = try await repository.fetchAffirmations(category: category)
        } catch {
            self.error = error
        }

        isLoading = false
    }

    @MainActor
    public func loadFavoriteAffirmations() async {
        isLoading = true
        error = nil

        do {
            affirmations = try await repository.fetchFavoriteAffirmations()
        } catch {
            self.error = error
        }

        isLoading = false
    }

    @MainActor
    public func deleteAffirmation(id: UUID) async {
        do {
            try await repository.deleteAffirmation(id: id)
            await loadAffirmations()
        } catch {
            self.error = error
        }
    }

    @MainActor
    public func toggleFavorite(for affirmation: any AffirmationProtocol) async {
        do {
            try await repository.updateAffirmation(affirmation)
            await loadAffirmations()
        } catch {
            self.error = error
        }
    }
}

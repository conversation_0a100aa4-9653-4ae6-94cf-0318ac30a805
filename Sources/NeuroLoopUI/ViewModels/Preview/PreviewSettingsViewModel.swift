import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewSettingsViewModel: ObservableObject {
    @Published public private(set) var isPremium = false
    @Published public private(set) var appVersion = "1.0.0"
    @Published public private(set) var isExporting = false
    @Published public private(set) var isSyncing = false
    @Published public private(set) var error: Error?

    private let userDefaults: UserDefaults
    private let purchaseManager: PurchaseManagerSendable
    private let dataExportService: DataExportServiceProtocol
    private let themeManager: NeuroLoopInterfaces.ThemeManaging
    private let hapticManager: HapticGenerating
    private let syncService: SyncServiceProtocol

    public init(
        userDefaults: UserDefaults = PreviewUserDefaults(),
        purchaseManager: PurchaseManagerSendable,
        dataExportService: DataExportServiceProtocol,
        themeManager: NeuroLoopInterfaces.ThemeManaging,
        hapticManager: HapticGenerating,
        syncService: SyncServiceProtocol
    ) {
        self.userDefaults = userDefaults
        self.purchaseManager = purchaseManager
        self.dataExportService = dataExportService
        self.themeManager = themeManager
        self.hapticManager = hapticManager
        self.syncService = syncService
    }

    public func exportData() async {
        isExporting = true
        error = nil

        do {
            _ = try await dataExportService.exportData()
        } catch {
            self.error = error
        }

        isExporting = false
    }

    public func syncData() async {
        isSyncing = true
        error = nil

        do {
            try await syncService.syncNow()
        } catch {
            self.error = error
        }

        isSyncing = false
    }

    public func purchasePremium() async {
        do {
            try await purchaseManager.purchasePremium()
            isPremium = true
        } catch {
            self.error = error
        }
    }

    public func restorePurchases() async {
        do {
            try await purchaseManager.restorePurchases()
            isPremium = true
        } catch {
            self.error = error
        }
    }

    public func clearAllData() async {
        do {
            try await dataExportService.deleteAllData()
        } catch {
            self.error = error
        }
    }
}

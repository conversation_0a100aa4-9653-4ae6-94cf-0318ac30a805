import Foundation
import NeuroLoopInterfaces

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewSyncOptionsViewModel: ObservableObject {
    @Published public private(set) var isSyncing = false
    @Published public private(set) var error: Error?
    @Published public private(set) var syncStatus: SyncStatus = .idle
    @Published public private(set) var isAutomaticSyncEnabled = false

    private let syncService: SyncServiceProtocol

    public init(syncService: SyncServiceProtocol) {
        self.syncService = syncService
    }

    public func syncData() async {
        isSyncing = true
        error = nil

        do {
            try await syncService.syncNow()
        } catch {
            self.error = error
        }

        isSyncing = false
    }

    public func toggleAutomaticSync() async {
        isAutomaticSyncEnabled.toggle()
        await syncService.setAutomaticSyncEnabled(isAutomaticSyncEnabled)
    }
}

import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/// A mutable preview implementation of AffirmationProtocol for testing
private struct MutablePreviewAffirmation: AffirmationProtocol {
    var id: UUID
    var text: String
    var category: AffirmationCategory
    var recordingURL: URL?
    var createdAt: Date
    var updatedAt: Date
    var currentCycleDay: Int
    var cycleStartDate: Date?
    var completedCycles: Int
    var currentRepetitions: Int
    var dailyProgress: [Date: Int]
    var lastRepetitionDate: Date?
    var energyLevel: Double
    var moodRating: Int?
    var notes: String?
    var isFavorite: Bool
    var playCount: Int
    var hasActiveCycle: Bool
    var isCurrentCycleComplete: Bool
    var todayProgress: Double
    var cycleProgress: Double
    var hasTodayQuotaMet: Bool
    var canPerformRepetition: Bool
    var hasRecording: Bool
    var longestStreak: Int

    init(
        id: UUID = UUID(),
        text: String,
        category: AffirmationCategory,
        recordingURL: URL? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        currentCycleDay: Int = 1,
        cycleStartDate: Date? = nil,
        completedCycles: Int = 0,
        currentRepetitions: Int = 0,
        dailyProgress: [Date: Int] = [:],
        lastRepetitionDate: Date? = nil,
        energyLevel: Double = 0.5,
        moodRating: Int? = nil,
        notes: String? = nil,
        isFavorite: Bool = false,
        playCount: Int = 0,
        hasActiveCycle: Bool = false,
        isCurrentCycleComplete: Bool = false,
        todayProgress: Double = 0.0,
        cycleProgress: Double = 0.0,
        hasTodayQuotaMet: Bool = false,
        canPerformRepetition: Bool = true,
        hasRecording: Bool = false,
        longestStreak: Int = 0
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.recordingURL = recordingURL
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.currentCycleDay = currentCycleDay
        self.cycleStartDate = cycleStartDate
        self.completedCycles = completedCycles
        self.currentRepetitions = currentRepetitions
        self.dailyProgress = dailyProgress
        self.lastRepetitionDate = lastRepetitionDate
        self.energyLevel = energyLevel
        self.moodRating = moodRating
        self.notes = notes
        self.isFavorite = isFavorite
        self.playCount = playCount
        self.hasActiveCycle = hasActiveCycle
        self.isCurrentCycleComplete = isCurrentCycleComplete
        self.todayProgress = todayProgress
        self.cycleProgress = cycleProgress
        self.hasTodayQuotaMet = hasTodayQuotaMet
        self.canPerformRepetition = canPerformRepetition
        self.hasRecording = hasRecording
        self.longestStreak = longestStreak
    }

    func recordRepetition() throws {}
    func updateEnergyLevel(_ level: Double) {}
    func recordMood(_ rating: Int, notes: String?) {}
}

/// A consolidated preview system for affirmations that provides both repository and view model functionality
@MainActor
public final class PreviewAffirmationSystem {
    // MARK: - Properties

    private var affirmations: [MutablePreviewAffirmation] = []
    private let queue = DispatchQueue(label: "com.neuroloop.preview.affirmation")

    // MARK: - Repository Methods

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        queue.sync { affirmations }
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        queue.sync { affirmations.first { $0.id == id } }
    }

    public func createAffirmation(
        text: String,
        category: AffirmationCategory,
        recordingURL: URL?
    ) async throws -> any AffirmationProtocol {
        let affirmation = MutablePreviewAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL,
            hasRecording: recordingURL != nil
        )
        queue.sync { affirmations.append(affirmation) }
        return affirmation
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        guard let previewAffirmation = affirmation as? MutablePreviewAffirmation else { return }
        queue.sync {
            if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                affirmations[index] = previewAffirmation
            }
        }
    }

    public func deleteAffirmation(id: UUID) async throws {
        queue.sync {
            affirmations.removeAll { $0.id == id }
        }
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        queue.sync { affirmations.filter { $0.category == category } }
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        queue.sync { affirmations.filter { $0.isFavorite } }
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        queue.sync { affirmations.first }
    }

    // MARK: - Service Methods

    public func toggleFavorite(_ affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        guard var previewAffirmation = affirmation as? MutablePreviewAffirmation else {
            return affirmation
        }
        previewAffirmation.isFavorite.toggle()
        try await updateAffirmation(previewAffirmation)
        return previewAffirmation
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
        guard var previewAffirmation = affirmation as? MutablePreviewAffirmation else { return }
        previewAffirmation.cycleProgress = 0.0
        previewAffirmation.hasActiveCycle = true
        try await updateAffirmation(previewAffirmation)
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        guard var previewAffirmation = affirmation as? MutablePreviewAffirmation else {
            return affirmation
        }
        previewAffirmation.todayProgress += 0.1
        previewAffirmation.cycleProgress += 0.1
        previewAffirmation.currentRepetitions += 1
        previewAffirmation.lastRepetitionDate = Date()
        try await updateAffirmation(previewAffirmation)
        return previewAffirmation
    }

    public func updateEnergyLevel(_ level: Double, for affirmation: any AffirmationProtocol)
        async throws -> any AffirmationProtocol
    {
        guard var previewAffirmation = affirmation as? MutablePreviewAffirmation else {
            return affirmation
        }
        previewAffirmation.energyLevel = level
        try await updateAffirmation(previewAffirmation)
        return previewAffirmation
    }

    public func recordMood(_ rating: Int, notes: String?, for affirmation: any AffirmationProtocol)
        async throws -> any AffirmationProtocol
    {
        guard var previewAffirmation = affirmation as? MutablePreviewAffirmation else {
            return affirmation
        }
        previewAffirmation.moodRating = rating
        previewAffirmation.notes = notes
        try await updateAffirmation(previewAffirmation)
        return previewAffirmation
    }

    public func getStatistics() async throws -> AffirmationStatistics {
        var statistics = AffirmationStatistics()
        statistics.totalAffirmations = affirmations.count
        statistics.activeAffirmations = affirmations.filter { $0.cycleProgress > 0 }.count
        statistics.favoriteAffirmations = affirmations.filter { $0.isFavorite }.count
        statistics.totalCompletedCycles = affirmations.reduce(0) { $0 + Int($1.cycleProgress) }
        statistics.totalRepetitions = affirmations.reduce(0) { $0 + Int($1.todayProgress * 10) }
        statistics.categoryDistribution = Dictionary(grouping: affirmations, by: { $0.category })
            .mapValues { $0.count }
        return statistics
    }
}

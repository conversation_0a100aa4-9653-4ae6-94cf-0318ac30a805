import Foundation
import NeuroLoopTypes

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewHapticManager: HapticGenerating {
    public func playSuccess() async {
        // Preview implementation
    }

    public func playError() async {
        // Preview implementation
    }

    public func playWarning() async {
        // Preview implementation
    }

    public func playSelection() async {
        // Preview implementation
    }

    public func playImpact(style: ImpactStyle) async {
        // Preview implementation
    }

    // Additional methods
    public func playCelebrationPattern() async {
        // Preview implementation
    }

    public func playAffirmationCompletionPattern() async {
        // Preview implementation
    }

    public func mediumImpact() async {
        // Preview implementation
    }
}

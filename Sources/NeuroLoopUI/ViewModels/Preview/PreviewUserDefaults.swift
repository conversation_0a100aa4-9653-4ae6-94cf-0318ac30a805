import Foundation
import Neuro<PERSON><PERSON><PERSON><PERSON>

@available(iOS 17.0, macOS 14.0, *)
public final class PreviewUserDefaults: UserDefaults {
    private var storage: [String: Any] = [:]

    public override func set(_ value: Any?, forKey defaultName: String) {
        storage[defaultName] = value
    }

    public override func object(forKey defaultName: String) -> Any? {
        return storage[defaultName]
    }

    public override func removeObject(forKey defaultName: String) {
        storage.removeValue(forKey: defaultName)
    }

    public override func synchronize() -> Bool {
        return true
    }
}

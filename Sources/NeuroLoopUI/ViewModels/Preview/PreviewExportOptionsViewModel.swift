import Foundation
import NeuroLoopInterfaces

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewExportOptionsViewModel: ObservableObject {
    @Published public private(set) var isExporting = false
    @Published public private(set) var error: Error?

    private let dataExportService: DataExportServiceProtocol

    public init(
        dataExportService: DataExportServiceProtocol
    ) {
        self.dataExportService = dataExportService
    }

    public static func create() -> PreviewExportOptionsViewModel {
        let service = PreviewServiceFactory.shared.getDataExportService()
        return PreviewExportOptionsViewModel(dataExportService: service)
    }

    public func exportAllData() async {
        isExporting = true
        error = nil

        do {
            _ = try await dataExportService.exportData()
        } catch {
            self.error = error
        }

        isExporting = false
    }
}

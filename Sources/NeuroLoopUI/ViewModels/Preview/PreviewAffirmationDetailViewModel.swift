import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI
import Combine

@available(iOS 17.0, macOS 14.0, *)
public class PreviewAffirmationDetailViewModel: ObservableObject {
    @Published public var affirmation: any AffirmationProtocol

    public init(affirmation: any AffirmationProtocol) {
        self.affirmation = affirmation
    }

    @MainActor
    public func recordRepetition() async {
        // Simulate recording a repetition
        print("Recording repetition for: \(affirmation.text)")
    }

    @MainActor
    public func startCycle() async {
        // Simulate starting a new cycle
        print("Starting new cycle for: \(affirmation.text)")
    }

    @MainActor
    public func toggleFavorite() async {
        // Simulate toggling favorite status
        print("Toggling favorite status for: \(affirmation.text)")
    }
    @MainActor
    public static func createViewModel() -> AffirmationDetailViewModel {
        // Create a preview affirmation
        let affirmation = createPreviewAffirmation()

        // Create the view model with just the affirmation
        return AffirmationDetailViewModel(affirmation: affirmation)
    }

    @MainActor
    private static func createPreviewAffirmation() -> any AffirmationProtocol {
        // Create a simple stub for preview
        return PreviewAffirmationStub(
            text: "I am capable of achieving my goals",
            category: .confidence
        )
    }

    @MainActor
    public static var shared: AffirmationDetailViewModel = {
        createViewModel()
    }()
}

// Simple stub for preview
@available(iOS 17.0, macOS 14.0, *)
private struct PreviewAffirmationStub: AffirmationProtocol {
    let id = UUID()
    let text: String
    let category: AffirmationCategory
    let recordingURL: URL? = nil
    let dailyProgress: [Date: Int] = [:]
    let createdAt = Date()
    let updatedAt = Date()
    let isFavorite = false
    let hasRecording = false
    let todayProgress: Double = 0
    let cycleProgress: Double = 0
    let completedCycles: Int = 0
    let currentRepetitions: Int = 0
    let lastRepetitionDate: Date? = nil
    let energyLevel: Double = 0.5
    let moodRating: Int? = nil
    let notes: String? = nil
    let playCount: Int = 0
    let hasActiveCycle = false
    let currentCycleDay: Int = 1
    let cycleStartDate: Date? = nil
    let canPerformRepetition = true
    let isCurrentCycleComplete = false
    let hasTodayQuotaMet = false
    var longestStreak: Int = 0

    func recordRepetition() throws {}
    func updateEnergyLevel(_ level: Double) {}
    func recordMood(_ rating: Int, notes: String?) {}

    // Equatable conformance
    static func == (lhs: PreviewAffirmationStub, rhs: PreviewAffirmationStub) -> Bool {
        lhs.id == rhs.id
    }
}

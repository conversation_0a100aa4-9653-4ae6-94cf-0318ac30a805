import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes
import Swift<PERSON>

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewHomeViewModel: HomeViewModelProtocol, @unchecked Sendable {
    @Published public private(set) var currentAffirmation: (any AffirmationProtocol)?
    @Published public private(set) var affirmations: [any AffirmationProtocol] = []
    @Published public private(set) var isLoading = false
    @Published public private(set) var error: Error?

    public init() {}

    public func loadData() async {
        // Preview implementation - no-op
    }

    public func refreshData() async {
        // Preview implementation - no-op
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async {
        // Preview implementation - no-op
    }
}

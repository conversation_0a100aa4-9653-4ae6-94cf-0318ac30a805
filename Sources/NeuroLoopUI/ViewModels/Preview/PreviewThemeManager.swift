import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

@available(iOS 17.0, macOS 14.0, *)
public final class PreviewThemeManager: NeuroLoopInterfaces.ThemeManaging {
    public var currentTheme: NeuroLoopTypes.Theme = .light

    public init() {}

    public func setTheme(_ theme: NeuroLoopTypes.Theme) {
        // Preview implementation - just update the current theme
        currentTheme = theme
    }
}

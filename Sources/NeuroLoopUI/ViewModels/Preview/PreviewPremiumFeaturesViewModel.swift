import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public class PreviewPremiumFeaturesViewModel {
    @MainActor
    public static func createViewModel() -> SubscriptionViewModel {
        // Use the mock premium service
        let premiumService = MockPremiumService()

        return SubscriptionViewModel(premiumService: premiumService)
    }

    @MainActor
    public static var shared: SubscriptionViewModel = {
        createViewModel()
    }()

    @MainActor
    public static func loadProducts() {
        // No async operations needed for preview
        Task {
            // Call loadProducts without try since it might not throw
            await shared.loadProducts()
        }
    }
}

// Note: Using MockPremiumService from SubscriptionViewModel.swift

import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/// View model for the AddAffirmationView
@available(iOS 17.0, macOS 14.0, *)
public class AddAffirmationViewModel: ObservableObject {
    // MARK: - Published Properties

    @Published var text: String = ""
    @Published var selectedCategory: AffirmationCategory = .confidence
    @Published var startImmediately: Bool = true

    // MARK: - Properties

    private let repository: AffirmationRepositoryProtocol

    // MARK: - Computed Properties

    public var isValid: Bool {
        !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    // MARK: - Initialization

    public init(repository: AffirmationRepositoryProtocol) {
        self.repository = repository
    }

    // MARK: - Public Methods

    @MainActor
    public func save() async {
        guard isValid else { return }

        do {
            _ = try await repository.createAffirmation(
                text: text.trimmingCharacters(in: .whitespacesAndNewlines),
                category: selectedCategory,
                recordingURL: nil
            )
        } catch {
            print("Error saving affirmation: \(error)")
        }
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    extension AddAffirmationViewModel {
        static var preview: AddAffirmationViewModel {
            let viewModel = AddAffirmationViewModel(repository: MockAffirmationRepository())
            viewModel.text = "I am confident and capable"
            viewModel.selectedCategory = .confidence
            viewModel.startImmediately = true
            return viewModel
        }
    }

    @available(iOS 17.0, macOS 14.0, *)
    // UI-only build: Mark as @unchecked Sendable to suppress Sendable warnings
    private class AddAffirmationViewModelMockRepository: AffirmationRepositoryProtocol, @unchecked Sendable {
        func fetchAffirmations() async throws -> [any AffirmationProtocol] {
            [
                try await createAffirmation(
                    text: "I am confident and capable", category: .confidence, recordingURL: nil)
            ]
        }

        func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
            try await createAffirmation(
                text: "I am confident and capable", category: .confidence, recordingURL: nil)
        }

        func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
            async throws -> any AffirmationProtocol
        {
            return createPreviewAffirmation(
                text: text,
                category: category,
                recordingURL: recordingURL
            )
        }

        func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
        func deleteAffirmation(id: UUID) async throws {}
        func fetchAffirmations(category: AffirmationCategory) async throws
            -> [any AffirmationProtocol]
        { [] }
        func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] { [] }
        func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? { nil }
        func recordRepetition(for affirmation: any AffirmationProtocol) async throws {}
        func startCycle(for affirmation: any AffirmationProtocol) async throws {}
    }
#endif

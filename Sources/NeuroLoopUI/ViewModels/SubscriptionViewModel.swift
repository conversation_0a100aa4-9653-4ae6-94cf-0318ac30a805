import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import StoreKit
import SwiftUI

@MainActor
public final class SubscriptionViewModel: ObservableObject {
    private let premiumService: PremiumServiceProtocol

    @Published public private(set) var products: [Product] = []
    @Published public private(set) var selectedProductId: String? = nil
    @Published public private(set) var isLoading: Bool = false
    @Published public private(set) var isPurchasing: Bool = false
    @Published public private(set) var error: Error? = nil
    @Published public private(set) var purchaseSuccess: Bool = false
    @Published public private(set) var restoreSuccess: Bool = false
    @Published public private(set) var didBecomePremium: Bool = false

    public init(premiumService: PremiumServiceProtocol) {
        self.premiumService = premiumService
    }

    public func loadProducts() async {
        isLoading = true
        error = nil

        do {
            products = try await Product.products(for: ["com.neuroloop.premium.monthly"])
            if let firstProduct = products.first {
                selectedProductId = firstProduct.id
            }
        } catch {
            self.error = error
        }

        isLoading = false
    }

    public func selectProduct(_ product: Product) {
        selectedProductId = product.id
    }

    public func purchase() async {
        guard let _ = products.first(where: { $0.id == selectedProductId }) else {
            return
        }

        isPurchasing = true
        error = nil

        do {
            let wasPremium = premiumService.isPremium
            try await premiumService.purchaseSubscription()
            purchaseSuccess = true
            if !wasPremium && premiumService.isPremium {
                didBecomePremium = true
            }
        } catch {
            self.error = error
        }

        isPurchasing = false
    }

    public func restorePurchases() async {
        isLoading = true
        error = nil

        do {
            let wasPremium = premiumService.isPremium
            try await premiumService.restorePurchases()
            restoreSuccess = true
            if !wasPremium && premiumService.isPremium {
                didBecomePremium = true
            }
        } catch {
            self.error = error
        }

        isLoading = false
    }

    public func dismissError() {
        error = nil
    }

    public func dismissSuccess() {
        purchaseSuccess = false
        restoreSuccess = false
        didBecomePremium = false
    }
}

// MARK: - Mock

@available(iOS 17.0, macOS 14.0, *)
public final class MockPremiumService: PremiumServiceProtocol {
    public var isPremium: Bool = false
    public var isPremiumPublisher: AnyPublisher<Bool, Never> = Just(false).eraseToAnyPublisher()

    public init() {}

    public func checkFeatureAvailability(_ feature: PremiumFeature) -> Bool {
        return isPremium
    }

    public func purchaseSubscription() async throws {
        isPremium = true
    }

    public func restorePurchases() async throws {
        isPremium = true
    }

    public func getSubscriptionStatus() async throws -> NeuroLoopInterfaces.SubscriptionStatus {
        return NeuroLoopInterfaces.SubscriptionStatus(
            isActive: isPremium,
            expirationDate: isPremium ? Date().addingTimeInterval(30 * 24 * 60 * 60) : nil,
            productId: isPremium ? "com.neuroloop.premium.monthly" : nil
        )
    }
}

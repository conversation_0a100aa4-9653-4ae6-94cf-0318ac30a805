import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import OSLog
import Swift<PERSON>

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class RepetitionTrackingViewModel: ObservableObject, Equatable {
    // MARK: - Types

    public struct RepetitionHistoryEntry: Identifiable {
        public let id: UUID
        public let date: Date
        public let count: Int

        public init(id: UUID = UUID(), date: Date, count: Int) {
            self.id = id
            self.date = date
            self.count = count
        }
    }

    // MARK: - Properties

    private let id: UUID
    private let affirmationService: AffirmationServiceProtocol
    @Published public var repetitionService: RepetitionServiceProtocol
    private let streakService: StreakServiceProtocol
    private let audioService: AudioRecordingServiceProtocol

    @Published public private(set) var affirmation: any AffirmationProtocol
    @Published public private(set) var todayProgress: Double = 0
    @Published public private(set) var cycleProgress: Double = 0
    @Published public private(set) var todayRepetitions: Int = 0
    @Published public private(set) var dailyGoal: Int = 15
    @Published public private(set) var currentDay: Int = 1
    @Published public private(set) var streakDays: [StreakCalendarDay] = []
    @Published public private(set) var isLoading: Bool = false
    @Published public private(set) var error: Error? = nil
    @Published public private(set) var canPerformRepetition: Bool = true
    @Published public private(set) var isMilestoneReached: Bool = false
    @Published public private(set) var hasRecording: Bool = false
    @Published public private(set) var isPlaying: Bool = false
    @Published public private(set) var playbackProgress: Double = 0
    @Published public var showRestorationFlow: Bool = false
    @Published public private(set) var recentHistory: [RepetitionHistoryEntry] = []

    // MARK: - Initialization

    @available(iOS 17.0, macOS 14.0, *)
    public init(
        affirmationService: AffirmationServiceProtocol,
        repetitionService: RepetitionServiceProtocol,
        streakService: StreakServiceProtocol,
        audioService: AudioRecordingServiceProtocol,
        affirmation: any AffirmationProtocol
    ) {
        self.id = UUID()
        self.affirmationService = affirmationService
        self.repetitionService = repetitionService
        self.streakService = streakService
        self.audioService = audioService
        self.affirmation = affirmation
        // Detect broken cycle on init
        self.showRestorationFlow = repetitionService.isCycleBroken(for: affirmation)
    }

    // MARK: - Public Methods

    @MainActor
    public func loadData() async {
        isLoading = true
        error = nil

        do {
            // Load affirmation data
            guard
                let updatedAffirmation = try await affirmationService.fetchAffirmation(
                    id: affirmation.id)
            else {
                throw NSError(
                    domain: "RepetitionTrackingViewModel", code: -1,
                    userInfo: [NSLocalizedDescriptionKey: "Affirmation not found"])
            }
            affirmation = updatedAffirmation

            // Load progress data
            let progress = repetitionService.getProgress(for: affirmation)
            todayProgress = progress.todayProgress
            cycleProgress = progress.cycleProgress
            todayRepetitions = progress.currentRepetitions
            currentDay = progress.currentDay
            canPerformRepetition = repetitionService.canPerformRepetition(for: affirmation)

            // Load streak data
            streakDays = await streakService.getStreakCalendarData(
                for: affirmation, numberOfDays: 7
            ).map {
                StreakCalendarDay(
                    id: $0.id, date: $0.date, repetitions: $0.repetitions,
                    isComplete: $0.isComplete, progress: $0.progress)
            }

            // Load audio data
            hasRecording = affirmation.recordingURL != nil

            // Load recent history
            let calendar = Calendar.current
            let today = calendar.startOfDay(for: Date())
            _ = calendar.date(byAdding: .day, value: -7, to: today)!

            var history: [RepetitionHistoryEntry] = []
            for day in stride(from: 0, to: 7, by: 1) {
                if let date = calendar.date(byAdding: .day, value: -day, to: today) {
                    let count = affirmation.dailyProgress[date] ?? 0
                    history.append(RepetitionHistoryEntry(date: date, count: count))
                }
            }
            recentHistory = history

        } catch {
            self.error = error
        }

        isLoading = false
    }

    public func performRepetition() {
        Task {
            do {
                let result = try await repetitionService.recordRepetition(for: affirmation)
                affirmation = result.updatedAffirmation

                // Update progress
                let progress = repetitionService.getProgress(for: affirmation)
                todayRepetitions = progress.currentRepetitions
                todayProgress = progress.todayProgress
                cycleProgress = progress.cycleProgress
                currentDay = progress.currentDay

                // Force UI update
                objectWillChange.send()

                // Check for milestone
                checkMilestone()
            } catch {
                self.error = error
            }
        }
    }

    public func playRecording() {
        Task {
            do {
                try await audioService.startPlayback()
            } catch {
                self.error = error
            }
        }
    }

    public func pausePlayback() {
        audioService.pausePlayback()
    }

    public func retry() {
        Task {
            await loadData()
        }
    }

    public func dismissError() {
        error = nil
    }

    /// Updates the repetition count directly - used for syncing with other view models
    public func updateRepetitionCount(_ count: Int) {
        // Only update if the count has actually changed
        if todayRepetitions != count {
            // Update on the main thread to ensure UI updates properly
            DispatchQueue.main.async {
                // Update the count
                self.todayRepetitions = count

                // Update related progress values
                let progress = self.repetitionService.getProgress(for: self.affirmation)
                self.todayProgress = progress.todayProgress
                self.cycleProgress = progress.cycleProgress

                // Check for milestone
                self.checkMilestone()

                // Notify observers of the change
                self.objectWillChange.send()

                // Post a notification to ensure all observers are updated
                // This is crucial for components that might not be directly observing this view model
                NotificationCenter.default.post(
                    name: Notification.Name("RepetitionCountChanged"),
                    object: nil,
                    userInfo: [
                        "count": count,
                        "affirmationId": self.affirmation.id,
                        "timestamp": Date().timeIntervalSince1970,  // Add timestamp to make each notification unique
                        "uuid": UUID().uuidString,  // Add UUID to make each notification unique
                    ]
                )

                // Add a small delay and send another objectWillChange to ensure UI updates
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    self.objectWillChange.send()

                    // Post another notification after a delay to ensure all observers are updated
                    NotificationCenter.default.post(
                        name: Notification.Name("RepetitionCountChanged"),
                        object: nil,
                        userInfo: [
                            "count": count,
                            "affirmationId": self.affirmation.id,
                            "timestamp": Date().timeIntervalSince1970,
                            "uuid": UUID().uuidString,
                        ]
                    )
                }
            }
        } else {
            // Even if the count hasn't changed, send an objectWillChange to ensure the UI updates
            DispatchQueue.main.async {
                self.objectWillChange.send()
            }
        }
    }

    /// Updates the repetition service - used when switching between debug and normal modes
    public func updateRepetitionService(_ newService: RepetitionServiceProtocol) {
        repetitionService = newService

        // Force UI update
        objectWillChange.send()

        // Reload data to ensure we have the latest state
        Task {
            await loadData()
        }
    }

    /// Updates the affirmation object - used when the affirmation is updated elsewhere
    public func updateAffirmation(_ updatedAffirmation: any AffirmationProtocol) {
        affirmation = updatedAffirmation

        // Update progress data based on the new affirmation
        let progress = repetitionService.getProgress(for: affirmation)
        todayProgress = progress.todayProgress
        cycleProgress = progress.cycleProgress
        todayRepetitions = progress.currentRepetitions
        currentDay = progress.currentDay
        canPerformRepetition = repetitionService.canPerformRepetition(for: affirmation)

        // Force UI update
        objectWillChange.send()

        print("RepetitionTrackingViewModel: Updated affirmation and progress data")
        print("RepetitionTrackingViewModel: New repetition count: \(todayRepetitions)")
    }

    private func checkMilestone() {
        let totalRepetitions = Int(cycleProgress * 100)
        isMilestoneReached = [25, 50, 75, 100].contains(totalRepetitions)
    }

    /// Call this when the restoration flow is completed (cycle restarted)
    public func onRestorationComplete() {
        Task {
            await loadData()
            showRestorationFlow = false
        }
    }

    // MARK: - Equatable

    // Make the Equatable implementation nonisolated to satisfy protocol requirements
    nonisolated public static func == (
        lhs: RepetitionTrackingViewModel, rhs: RepetitionTrackingViewModel
    ) -> Bool {
        // Since we can't access actor-isolated properties in a nonisolated context,
        // we'll use the object identifiers for comparison
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    // MARK: - Shared Instance

    @MainActor
    public static let shared: RepetitionTrackingViewModel = {
        let factory = PreviewServiceFactory.shared
        let affirmationService = factory.getAffirmationService()
        let repetitionService = factory.getRepetitionService()
        let streakService = factory.getStreakService()
        let audioService = factory.getAudioRecordingService()

        // Create a simple stub affirmation for preview
        let affirmation = RepetitionAffirmationStub(
            text: "Preview Affirmation",
            category: .confidence
        )

        return RepetitionTrackingViewModel(
            affirmationService: affirmationService,
            repetitionService: repetitionService,
            streakService: streakService,
            audioService: audioService,
            affirmation: affirmation
        )
    }()

    // MARK: - Preview

    public static var preview: RepetitionTrackingViewModel {
        shared
    }
}

// Simple stub for preview
@available(iOS 17.0, macOS 14.0, *)
private struct RepetitionAffirmationStub: AffirmationProtocol {
    let id = UUID()
    let text: String
    let category: AffirmationCategory
    let recordingURL: URL? = nil
    let dailyProgress: [Date: Int] = [:]
    let createdAt = Date()
    let updatedAt = Date()
    let isFavorite = false
    let hasRecording = false
    let todayProgress: Double = 0
    let cycleProgress: Double = 0
    let completedCycles: Int = 0
    let currentRepetitions: Int = 0
    let lastRepetitionDate: Date? = nil
    let energyLevel: Double = 0.5
    let moodRating: Int? = nil
    let notes: String? = nil
    let playCount: Int = 0
    let hasActiveCycle = false
    let currentCycleDay: Int = 1
    let cycleStartDate: Date? = nil
    let canPerformRepetition = true
    let isCurrentCycleComplete = false
    let hasTodayQuotaMet = false

    // Add longestStreak property for AffirmationProtocol conformance
    var longestStreak: Int = 0

    func recordRepetition() throws {}
    func updateEnergyLevel(_ level: Double) {}
    func recordMood(_ rating: Int, notes: String?) {}

    // Equatable conformance
    static func == (lhs: RepetitionAffirmationStub, rhs: RepetitionAffirmationStub) -> Bool {
        lhs.id == rhs.id
    }
}

import Foundation
import SwiftUI

public class GuidedSessionViewModel: ObservableObject {
    public enum StepType { case preparation, breathing, affirmation, reflection, complete }
    public enum SessionLength: String, CaseIterable { case quick, standard, extended }
    public enum GuidanceLevel: String, CaseIterable { case detailed, minimal }
    public enum VoiceType: String, CaseIterable { case defaultVoice, alternate }
    public enum BackgroundSound: String, CaseIterable { case none, ocean, rain, music }

    @Published public var currentStep: Int = 0
    @Published public var timeRemaining: String = "00:00"
    @Published public var isPlaying: Bool = false
    
    public var totalSteps: Int { 5 }
    public var currentStepType: StepType { .preparation }
    public var preparationText: String = "Prepare for your session."
    public var breathingDuration: TimeInterval = 10
    public var affirmationText: String = "I am calm and focused."
    public var repetitions: Int = 3
    public var reflectionText: String = "Reflect on your experience."
    public var isAudioEnabled: Bool = false
    public var isAudioPlaying: Bool = false
    @Published public var audioVolume: Float = 0.5
    @Published public var showCustomization: Bool = false
    @Published public var sessionLength: SessionLength = .standard
    @Published public var guidanceLevel: GuidanceLevel = .detailed
    @Published public var voiceType: VoiceType = .defaultVoice
    @Published public var backgroundSound: BackgroundSound = .none
    @Published public var showLearnMore: Bool = false
    public var learnMoreContent: String = "Learn more about affirmations."

    private var timer: Timer?
    private var remainingTime: TimeInterval = 0

    public func nextStep() {
        if currentStep < totalSteps - 1 {
            currentStep += 1
            updateTimeRemaining()
        }
    }
    
    public func finishSession() {
        timer?.invalidate()
        timer = nil
        isPlaying = false
    }
    
    public func togglePlayback() {
        isPlaying.toggle()
        if isPlaying {
            startTimer()
        } else {
            timer?.invalidate()
            timer = nil
        }
    }
    
    public func repeatExactly() {
        // Implementation for repeating the current step
    }
    
    public func playAudio() {
        isAudioPlaying = true
    }
    
    public func pauseAudio() {
        isAudioPlaying = false
    }
    
    public func startSession() {
        remainingTime = calculateTotalTime()
        updateTimeRemaining()
    }
    
    public func exitSession() {
        timer?.invalidate()
        timer = nil
        isPlaying = false
    }
    
    public func endSession() {
        timer?.invalidate()
        timer = nil
        isPlaying = false
    }
    
    private func startTimer() {
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTimeRemaining()
        }
    }
    
    private func updateTimeRemaining() {
        let minutes = Int(remainingTime) / 60
        let seconds = Int(remainingTime) % 60
        timeRemaining = String(format: "%02d:%02d", minutes, seconds)
        
        if remainingTime > 0 {
            remainingTime -= 1
        } else {
            timer?.invalidate()
            timer = nil
            isPlaying = false
        }
    }
    
    private func calculateTotalTime() -> TimeInterval {
        // Calculate total time based on session length and steps
        let baseTime: TimeInterval = 300 // 5 minutes
        switch sessionLength {
        case .quick:
            return baseTime * 0.5
        case .standard:
            return baseTime
        case .extended:
            return baseTime * 2
        }
    }
} 
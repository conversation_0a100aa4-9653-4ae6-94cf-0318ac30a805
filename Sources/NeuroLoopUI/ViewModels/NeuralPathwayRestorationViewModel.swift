import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

/// Tiered educational content for neural pathway restoration
public struct NeuralEducationTieredContent {
    public struct Tier {
        /// Main educational statements for this tier
        public let main: [String]
        /// Expandable 'Learn More' facts for this tier
        public let learnMore: [String]
    }
    public let basic: Tier
    public let intermediate: Tier
    public let advanced: Tier
}

/// Brief scientific reference for the "Science Behind This" section
public struct ScienceReference {
    public let title: String
    public let summary: String
}

/// ViewModel for the Neural Pathway Restoration flow.
/// Manages tiered educational content, neural strength progress, and restoration logic.
@MainActor
public final class NeuralPathwayRestorationViewModel: ObservableObject {
    // MARK: - Dependencies
    private let affirmation: any AffirmationProtocol
    public let repetitionService: RepetitionServiceProtocol
    private let onCycleRestarted: (() -> Void)?

    // MARK: - Published Properties
    @Published public var neuralStrength: Double = 0.0
    @Published public var isLoading: Bool = false
    @Published public var error: Error? = nil
    /// The currently selected content tier (basic/intermediate/advanced)
    @Published public var selectedTier: ContentTier = .basic

    // MARK: - Statistics
    @Published public var totalSessions: Int = 0
    @Published public var averageDuration: Int = 0
    @Published public var consistencyRate: Double = 0.0
    @Published public var nextMilestone: String = ""

    // MARK: - Content Types
    public enum ContentTier: String, CaseIterable, Identifiable {
        case basic, intermediate, advanced
        public var id: String { rawValue }
        public var displayName: String {
            switch self {
            case .basic: return NSLocalizedString("Basic", comment: "Tier: Basic")
            case .intermediate:
                return NSLocalizedString("Intermediate", comment: "Tier: Intermediate")
            case .advanced: return NSLocalizedString("Advanced", comment: "Tier: Advanced")
            }
        }
    }

    // MARK: - Tiered Content
    public let education: NeuralEducationTieredContent
    public let scienceReferences: [ScienceReference]

    // MARK: - Initialization
    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: RepetitionServiceProtocol,
        onCycleRestarted: (() -> Void)? = nil
    ) {
        self.affirmation = affirmation
        self.repetitionService = repetitionService
        self.onCycleRestarted = onCycleRestarted
        self.neuralStrength = Self.calculateNeuralStrength(for: affirmation)
        self.education = Self.defaultEducationContent()
        self.scienceReferences = Self.defaultScienceReferences()

        // Initialize statistics
        self.totalSessions = affirmation.completedCycles
        self.averageDuration = 5  // Default value, should be calculated based on actual data
        self.consistencyRate = affirmation.cycleProgress
        self.nextMilestone = "Day \(affirmation.currentCycleDay + 1)"
    }

    // MARK: - Actions
    public func restartCycle() {
        isLoading = true
        error = nil
        Task {
            do {
                _ = try await repetitionService.restartBrokenCycle(for: affirmation)
                isLoading = false
                onCycleRestarted?()
            } catch {
                self.error = error
                isLoading = false
            }
        }
    }

    public func startSession() {
        isLoading = true
        error = nil
        Task {
            do {
                _ = try await repetitionService.startSession(for: affirmation)
                isLoading = false
                onCycleRestarted?()
            } catch {
                self.error = error
                isLoading = false
            }
        }
    }

    // MARK: - Content Accessors
    public func mainContent(for tier: ContentTier? = nil) -> [String] {
        switch tier ?? selectedTier {
        case .basic: return education.basic.main
        case .intermediate: return education.intermediate.main
        case .advanced: return education.advanced.main
        }
    }
    public func learnMoreContent(for tier: ContentTier? = nil) -> [String] {
        switch tier ?? selectedTier {
        case .basic: return education.basic.learnMore
        case .intermediate: return education.intermediate.learnMore
        case .advanced: return education.advanced.learnMore
        }
    }

    // MARK: - Helpers
    private static func calculateNeuralStrength(for affirmation: any AffirmationProtocol) -> Double
    {
        if let progress = affirmation.cycleProgress as Double? {
            return min(max(progress, 0.0), 1.0)
        }
        return 0.0
    }

    // MARK: - Default Content
    private static func defaultEducationContent() -> NeuralEducationTieredContent {
        NeuralEducationTieredContent(
            basic: .init(
                main: [
                    NSLocalizedString(
                        "Repetition is how your brain forms new habits.", comment: "Basic 1"),
                    NSLocalizedString(
                        "Each time you repeat, you strengthen a neural pathway.", comment: "Basic 2"
                    ),
                    NSLocalizedString(
                        "100 repetitions over 7 days helps your brain create lasting change.",
                        comment: "Basic 3"),
                    NSLocalizedString(
                        "If you miss a day, you can always restart and keep building.",
                        comment: "Basic 4"),
                ],
                learnMore: [
                    NSLocalizedString(
                        "Neural pathways are like trails in your brain—the more you use them, the stronger they get.",
                        comment: "Basic Learn More 1"),
                    NSLocalizedString(
                        "Missing a day doesn't erase your progress. Restarting helps reinforce the pathway.",
                        comment: "Basic Learn More 2"),
                    NSLocalizedString(
                        "Most people need several attempts to build a new habit. That's normal!",
                        comment: "Basic Learn More 3"),
                ]
            ),
            intermediate: .init(
                main: [
                    NSLocalizedString(
                        "Your brain is constantly changing—a process called neuroplasticity.",
                        comment: "Intermediate 1"),
                    NSLocalizedString(
                        "Repeating an action sends signals along neural circuits, making them more efficient.",
                        comment: "Intermediate 2"),
                    NSLocalizedString(
                        "When a cycle is broken, the pathway weakens, but restarting quickly helps recover lost ground.",
                        comment: "Intermediate 3"),
                ],
                learnMore: [
                    NSLocalizedString(
                        "Neuroplasticity means your brain can adapt and rewire itself at any age.",
                        comment: "Intermediate Learn More 1"),
                    NSLocalizedString(
                        "The 7-day cycle is based on research showing that consistent repetition over a week is optimal for habit formation.",
                        comment: "Intermediate Learn More 2"),
                    NSLocalizedString(
                        "Even after a break, your brain remembers the pathway and can rebuild it faster next time.",
                        comment: "Intermediate Learn More 3"),
                ]
            ),
            advanced: .init(
                main: [
                    NSLocalizedString(
                        "Synaptic connections between neurons are strengthened by repeated activation.",
                        comment: "Advanced 1"),
                    NSLocalizedString(
                        "Long-term potentiation (LTP) is a key mechanism in memory and learning.",
                        comment: "Advanced 2"),
                    NSLocalizedString(
                        "Interruptions in repetition reduce LTP, but resuming practice can restore it.",
                        comment: "Advanced 3"),
                ],
                learnMore: [
                    NSLocalizedString(
                        "Studies show that 100 repetitions is a threshold for measurable synaptic change.",
                        comment: "Advanced Learn More 1"),
                    NSLocalizedString(
                        "The brain's ability to recover after a break is called 'savings' in neuroscience.",
                        comment: "Advanced Learn More 2"),
                    NSLocalizedString(
                        "Reference: Doidge, N. (2007). The Brain That Changes Itself. Penguin Books.",
                        comment: "Advanced Learn More 3"),
                ]
            )
        )
    }
    private static func defaultScienceReferences() -> [ScienceReference] {
        [
            ScienceReference(
                title: NSLocalizedString(
                    "Neuroplasticity & Habit Formation", comment: "Science Ref 1 Title"),
                summary: NSLocalizedString(
                    "Research shows that repeated practice strengthens neural circuits, making new habits easier to form.",
                    comment: "Science Ref 1 Summary")
            ),
            ScienceReference(
                title: NSLocalizedString("The Power of Repetition", comment: "Science Ref 2 Title"),
                summary: NSLocalizedString(
                    "Consistent repetition over days or weeks is critical for long-term memory and behavioral change.",
                    comment: "Science Ref 2 Summary")
            ),
            ScienceReference(
                title: NSLocalizedString("Why 7 Days?", comment: "Science Ref 3 Title"),
                summary: NSLocalizedString(
                    "Studies suggest that a week-long cycle is optimal for consolidating new neural pathways.",
                    comment: "Science Ref 3 Summary")
            ),
        ]
    }

    // MARK: - Preview
    #if DEBUG
        public static var preview: NeuralPathwayRestorationViewModel {
            let mockAffirmation = MockAffirmation()
            let mockService = NeuralPathwayMockRepetitionService()
            return NeuralPathwayRestorationViewModel(
                affirmation: mockAffirmation,
                repetitionService: mockService
            )
        }
    #endif
}

// MARK: - Mocks for Preview
#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    private class MockAffirmation: AffirmationProtocol, Equatable {
        var id: UUID = UUID()
        var text: String = "Mock Affirmation"
        var category: AffirmationCategory = .confidence
        var recordingURL: URL? = nil
        var createdAt: Date = Date()
        var updatedAt: Date = Date()
        var currentCycleDay: Int = 1
        var cycleStartDate: Date? = nil
        var completedCycles: Int = 0
        var currentRepetitions: Int = 0
        var dailyProgress: [Date: Int] = [:]
        var lastRepetitionDate: Date? = nil
        var energyLevel: Double = 0.0
        var moodRating: Int? = nil
        var notes: String? = nil
        var isFavorite: Bool = false
        var playCount: Int = 0
        var hasActiveCycle: Bool = false
        var isCurrentCycleComplete: Bool = false
        var todayProgress: Double = 0.0
        var cycleProgress: Double = 0.0
        var hasTodayQuotaMet: Bool = false
        var canPerformRepetition: Bool = true
        var hasRecording: Bool { recordingURL != nil }
        var longestStreak: Int = 0
        func recordRepetition() throws {}
        func updateEnergyLevel(_ level: Double) {}
        func recordMood(_ rating: Int, notes: String?) {}
        static func == (lhs: MockAffirmation, rhs: MockAffirmation) -> Bool {
            lhs.id == rhs.id
        }
    }

    @available(iOS 17.0, macOS 14.0, *)
    private class NeuralPathwayMockRepetitionService: RepetitionServiceProtocol, @unchecked Sendable {
        func recordRepetition(for affirmation: any AffirmationProtocol) async throws
            -> RepetitionResult
        {
            RepetitionResult(
                success: true,
                updatedAffirmation: affirmation,
                isQuotaMet: false,
                isCycleComplete: false
            )
        }

        func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
            CycleResult(
                success: true,
                updatedAffirmation: affirmation
            )
        }

        func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
            CycleResult(
                success: true,
                updatedAffirmation: affirmation
            )
        }

        func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
            ProgressInfo(
                todayProgress: 0.5,
                cycleProgress: 0.5,
                currentDay: 3,
                totalDays: 7,
                currentRepetitions: 7,
                totalRepetitions: 15,
                hasTodayQuotaMet: false,
                isCycleComplete: false,
                hasActiveCycle: true
            )
        }

        func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
            StreakInfo(
                currentStreak: 0,
                longestStreak: 0,
                completedCycles: 0,
                hasActiveCycle: false,
                cycleStartDate: nil,
                lastRepetitionDate: nil
            )
        }

        func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool { true }

        func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval? {
            nil
        }

        func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws
            -> CycleResult
        {
            CycleResult(
                success: true,
                updatedAffirmation: affirmation
            )
        }

        func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool { false }
    }
#endif

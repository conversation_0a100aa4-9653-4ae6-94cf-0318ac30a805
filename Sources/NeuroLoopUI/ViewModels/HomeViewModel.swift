import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes
import SwiftUI

/// View model for the HomeView
@available(iOS 17.0, macOS 14.0, *)
@MainActor
public class HomeViewModel: ObservableObject {
    // MARK: - Published Properties

    @Published private(set) var activeAffirmations: [any AffirmationProtocol] = []
    @Published private(set) var todayProgress: Double = 0.0
    @Published private(set) var completedRepetitions: Int = 0
    @Published private(set) var totalRepetitions: Int = 0
    @Published private(set) var statistics: Statistics?
    @Published private(set) var completionRate: Double = 0.0
    @Published public var isLoading: Bool = false
    @Published public var error: Error?

    public var currentDay: Int {
        let weekday = Calendar.current.component(.weekday, from: Date())
        return weekday  // 1 = Sunday, 2 = Monday, ... 7 = Saturday
    }

    // Add mostRecentAffirmation property
    @Published public var mostRecentAffirmation: (any AffirmationProtocol)?
    private let mostRecentAffirmationKey = "mostRecentAffirmation"
    private let mostRecentAffirmationTimestampKey = "mostRecentAffirmationTimestamp"

    // MARK: - Dependencies

    private let repository: AffirmationRepositoryProtocol
    public let streakService: StreakServiceProtocol
    private let userDefaults: UserDefaults

    // MARK: - Initialization

    @MainActor
    public init(
        repository: AffirmationRepositoryProtocol,
        streakService: StreakServiceProtocol,
        userDefaults: UserDefaults = .standard
    ) {
        self.repository = repository
        self.streakService = streakService
        self.userDefaults = userDefaults
    }

    @MainActor
    private static func makeAudioRecordingService() -> AudioRecordingServiceProtocol {
        #if os(iOS)
        return AudioRecordingService(audioFileManager: AudioFileManager.shared)
        #else
        return AudioRecordingService()
        #endif
    }

    // MARK: - Public Methods

    @MainActor
    public func loadData() async {
        isLoading = true
        do {
            // Fetch all affirmations
            let affirmations = try await repository.fetchAffirmations()

            // Filter active affirmations
            activeAffirmations = affirmations.filter { $0.hasActiveCycle }

            // Calculate today's progress
            let today = Calendar.current.startOfDay(for: Date())
            var totalCompleted = 0
            var totalRequired = 0

            for affirmation in activeAffirmations {
                let repetitions = affirmation.dailyProgress[today] ?? 0
                totalCompleted += repetitions
                totalRequired += AffirmationConstants.DAILY_REPETITIONS
            }

            completedRepetitions = totalCompleted
            totalRepetitions = totalRequired
            todayProgress = totalRequired > 0 ? Double(totalCompleted) / Double(totalRequired) : 0.0
            completionRate = todayProgress

            // Calculate statistics
            let totalAffirmations = affirmations.count
            let activeStreaks = activeAffirmations.count
            let completedCycles = affirmations.filter { $0.completedCycles > 0 }.count
            let longestCurrentStreak = activeAffirmations.map { $0.completedCycles }.max() ?? 0

            statistics = Statistics(
                totalAffirmations: totalAffirmations,
                activeStreaks: activeStreaks,
                completedCycles: completedCycles,
                longestCurrentStreak: longestCurrentStreak
            )

            // Validate streaks
            do {
                _ = try await streakService.validateStreaks()
            } catch {
                print("Error validating streaks: \(error)")
                // Continue execution even if streak validation fails
            }

            // Load most recent affirmation
            await loadMostRecentAffirmation(from: affirmations)
        } catch {
            self.error = error
        }
        isLoading = false
    }

    @MainActor
    public func startSession() async {
        do {
            let affirmation = try await repository.fetchCurrentAffirmation()
            mostRecentAffirmation = affirmation

            // Navigate to the AffirmationDetailView if we have an affirmation
            if let affirmation = affirmation {
                // Post notification to navigate to the AffirmationDetailView
                NotificationCenter.default.post(
                    name: .navigateToSpeakAffirmation,
                    object: nil,
                    userInfo: ["affirmationId": affirmation.id]
                )
            } else {
                // If no current affirmation, create a mock one for demo purposes
                let mockAffirmation = createMockAffirmation()
                NotificationCenter.default.post(
                    name: .navigateToSpeakAffirmation,
                    object: nil,
                    userInfo: ["affirmationId": mockAffirmation.id]
                )
            }

            await loadData()
        } catch {
            print("Error starting session: \(error)")
        }
    }

    // Create a mock affirmation for demo purposes
    private func createMockAffirmation() -> any AffirmationProtocol {
        let mockAffirmation = MockAffirmation()
        return mockAffirmation
    }

    // Simple mock implementation
    private class MockAffirmation: AffirmationProtocol, @unchecked Sendable {
        let id = UUID()
        let text = "I am confident and capable in everything I do"
        let category: AffirmationCategory = .confidence
        let recordingURL: URL? = nil
        let createdAt = Date()
        let updatedAt = Date()
        let currentCycleDay = 1
        let cycleStartDate: Date? = Date()
        let completedCycles = 0
        let currentRepetitions = 0
        let dailyProgress: [Date: Int] = [:]
        let lastRepetitionDate: Date? = nil
        let energyLevel = 0.5
        let moodRating: Int? = nil
        let notes: String? = nil
        let isFavorite = false
        let playCount = 0
        let hasActiveCycle = true
        let isCurrentCycleComplete = false
        let todayProgress = 0.0
        let cycleProgress = 0.0
        let hasTodayQuotaMet = false
        let hasRecording = false
        let canPerformRepetition = true
        var longestStreak = 0

        // Required methods from AffirmationProtocol
        func recordRepetition() throws {
            // Mock implementation - does nothing
        }

        func updateEnergyLevel(_ level: Double) {
            // Mock implementation - does nothing
        }

        func recordMood(_ rating: Int, notes: String?) {
            // Mock implementation - does nothing
        }

        static func == (lhs: MockAffirmation, rhs: MockAffirmation) -> Bool {
            return lhs.id == rhs.id
        }
    }

    @MainActor
    public func viewProgress() {
        // Navigate to progress dashboard
        NotificationCenter.default.post(name: .navigateToProgress, object: nil)
    }

    // MARK: - Most Recent Affirmation Tracking
    @MainActor
    private func updateMostRecentAffirmation(_ affirmation: any AffirmationProtocol) {
        mostRecentAffirmation = affirmation
        let defaults = UserDefaults.standard
        defaults.set(affirmation.id.uuidString, forKey: mostRecentAffirmationKey)
        defaults.set(Date(), forKey: mostRecentAffirmationTimestampKey)
    }

    @MainActor
    private func loadMostRecentAffirmation(from affirmations: [any AffirmationProtocol]) async {
        let defaults = UserDefaults.standard
        guard let idString = defaults.string(forKey: mostRecentAffirmationKey),
            let id = UUID(uuidString: idString)
        else {
            mostRecentAffirmation = nil
            return
        }
        if let affirmation = affirmations.first(where: { $0.id == id }) {
            mostRecentAffirmation = affirmation
        } else if let fetched = try? await repository.fetchAffirmation(id: id) {
            mostRecentAffirmation = fetched
        } else {
            // Affirmation was deleted
            mostRecentAffirmation = nil
            defaults.removeObject(forKey: mostRecentAffirmationKey)
            defaults.removeObject(forKey: mostRecentAffirmationTimestampKey)
        }
    }

    @MainActor
    public func toggleFavorite(_ affirmation: any AffirmationProtocol) async {
        // Implementation for toggling favorite
        do {
            // Create a modified copy with toggled favorite status
            let updatedAffirmation = await AffirmationStub(
                id: affirmation.id,
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                isFavorite: !affirmation.isFavorite,
                todayProgress: affirmation.todayProgress,
                cycleProgress: affirmation.cycleProgress
            )

            try await repository.updateAffirmation(updatedAffirmation)

            // Update the local list
            if let index = activeAffirmations.firstIndex(where: { $0.id == affirmation.id }) {
                activeAffirmations[index] = updatedAffirmation
            }

            // Update most recent if needed
            if mostRecentAffirmation?.id == affirmation.id {
                mostRecentAffirmation = updatedAffirmation
            }
        } catch {
            self.error = error
        }
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    extension HomeViewModel {
        @MainActor
        static func preview() async -> HomeViewModel {
            let mockRepo = await PreviewAffirmationRepository()
            let viewModel = HomeViewModel(
                repository: mockRepo, streakService: StreakService(repository: mockRepo))

            viewModel.activeAffirmations = [
                try! await mockRepo.createAffirmation(
                    text: "I am confident and capable", category: .confidence, recordingURL: nil
                ),
                try! await mockRepo.createAffirmation(
                    text: "I attract positive energy", category: .confidence, recordingURL: nil),
            ]

            viewModel.todayProgress = 0.75
            viewModel.completedRepetitions = 15
            viewModel.totalRepetitions = 20
            return viewModel
        }
    }

    // MARK: - Local Mock for Preview
    @available(iOS 17.0, macOS 14.0, *)
    private class HomeViewModelMockRepository: AffirmationRepositoryProtocol, @unchecked Sendable {
        func fetchAffirmations() async throws -> [any AffirmationProtocol] {
            [
                try await createAffirmation(
                    text: "I am confident and capable", category: .confidence, recordingURL: nil),
                try await createAffirmation(
                    text: "I attract positive energy", category: .confidence, recordingURL: nil),
            ]
        }

        func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
            try await createAffirmation(
                text: "I am confident and capable", category: .confidence, recordingURL: nil)
        }

        func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
            async throws -> any AffirmationProtocol
        {
            // Create a stub affirmation instead of calling self recursively
            return await AffirmationStub(
                text: text,
                category: category,
                recordingURL: recordingURL
            )
        }

        func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
        func deleteAffirmation(id: UUID) async throws {}
        func fetchAffirmations(category: AffirmationCategory) async throws
            -> [any AffirmationProtocol]
        { [] }
        func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] { [] }
        func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? { nil }
        func recordRepetition(for affirmation: any AffirmationProtocol) async throws {}
        func startCycle(for affirmation: any AffirmationProtocol) async throws {}
    }
#endif

// MARK: - Statistics
public struct Statistics {
    public let totalAffirmations: Int
    public let activeStreaks: Int
    public let completedCycles: Int
    public let longestCurrentStreak: Int
}

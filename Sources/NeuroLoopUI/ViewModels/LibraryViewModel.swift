import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class LibraryViewModel: ObservableObject, Equatable {
    // MARK: - Published Properties

    @Published public var affirmations: [any AffirmationProtocol] = []
    @Published private(set) var isLoading: Bool = false
    @Published private(set) var errorMessage: String? = nil
    @Published public var searchText: String = ""
    @Published public var selectedCategory: AffirmationCategory? = nil
    @Published public var sortOption: AffirmationSortOption = .newest

    // MARK: - Properties

    private let affirmationService: AffirmationServiceProtocol

    // MARK: - Initialization

    public init(affirmationService: AffirmationServiceProtocol) {
        self.affirmationService = affirmationService
    }

    // MARK: - Computed Properties

    @MainActor
    public var filteredAffirmations: [any AffirmationProtocol] {
        var filtered = affirmations

        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { affirmation in
                affirmation.text.localizedCaseInsensitiveContains(searchText)
            }
        }

        // Filter by category
        if let category = selectedCategory {
            filtered = filtered.filter { affirmation in
                affirmation.category == category
            }
        }

        // Sort according to sortOption
        switch sortOption {
        case .newest:
            filtered.sort { $0.createdAt > $1.createdAt }
        case .oldest:
            filtered.sort { $0.createdAt < $1.createdAt }
        case .alphabetical:
            filtered.sort { $0.text < $1.text }
        case .favorite:
            filtered.sort { $0.isFavorite && !$1.isFavorite }
        }

        return filtered
    }

    // MARK: - Public Methods

    @MainActor
    public func loadAffirmations() async {
        isLoading = true
        errorMessage = nil

        do {
            affirmations = try await affirmationService.fetchAffirmations()
        } catch {
            errorMessage = error.localizedDescription
        }

        isLoading = false
    }

    @MainActor
    public func deleteAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        do {
            try await affirmationService.deleteAffirmation(id: affirmation.id)
            await loadAffirmations()  // Reload to update the list
        } catch {
            errorMessage = error.localizedDescription
            throw error
        }
    }

    @MainActor
    public func toggleFavorite(_ affirmation: any AffirmationProtocol) async throws {
        do {
            let updatedAffirmation = try await affirmationService.toggleFavorite(affirmation)

            // Update in the affirmations array
            if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
                affirmations[index] = updatedAffirmation
            }
        } catch {
            errorMessage = error.localizedDescription
            throw error
        }
    }

    @MainActor
    public func refreshLibrary() async {
        await loadAffirmations()
    }

    @MainActor
    static var preview: LibraryViewModel {
        let viewModel = LibraryViewModel(affirmationService: PreviewAffirmationService())
        Task {
            viewModel.affirmations = [
                try! await viewModel.affirmationService.createAffirmation(
                    text: "I am confident and capable", category: AffirmationCategory.personal,
                    recordingURL: nil as URL?),
                try! await viewModel.affirmationService.createAffirmation(
                    text: "I embrace challenges with courage",
                    category: AffirmationCategory.confidence,
                    recordingURL: nil as URL?),
            ]
        }
        return viewModel
    }
}

// MARK: - Equatable

@available(iOS 17.0, macOS 14.0, *)
extension LibraryViewModel {
    // Using nonisolated to ensure this function can be called from any context
    // This is required because Equatable protocol is not actor-isolated
    public static nonisolated func == (lhs: LibraryViewModel, rhs: LibraryViewModel) -> Bool {
        // For reference types, comparing by identity is the safest approach
        // when dealing with actor isolation concerns
        return ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    extension LibraryViewModel {
        // No local PreviewAffirmationService needed - using the one from ViewModels/Preview/PreviewAffirmationService.swift
    }
#endif

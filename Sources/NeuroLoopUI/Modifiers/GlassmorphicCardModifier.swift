import NeuroLoopCore
import NeuroLoopTypes
import SwiftUI

public struct GlassmorphicCardModifier: ViewModifier {
    public init() {}

    public func body(content: Content) -> some View {
        content
            .padding()
            .background(
                // Use a custom blue card background directly
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.0, green: 0.5, blue: 1.0),
                                Color(red: 0.0, green: 0.3, blue: 0.8)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: Color.black.opacity(0.3), radius: 15, x: 0, y: 8)
            )
    }
}

extension View {
    public func glassmorphicCard() -> some View {
        modifier(GlassmorphicCardModifier())
    }
}

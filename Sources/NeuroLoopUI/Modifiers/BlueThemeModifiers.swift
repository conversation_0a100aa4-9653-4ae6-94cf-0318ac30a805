import NeuroLoopCore
import <PERSON>euroLoopTypes
import SwiftUI

/// A collection of view modifiers for applying the blue theme styling consistently across the app

// MARK: - Blue Background Modifier

/// Applies the blue gradient background to a view
public struct BlueBackgroundModifier: ViewModifier {
    public init() {}
    
    public func body(content: Content) -> some View {
        content
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.1, green: 0.4, blue: 0.8),
                        Color(red: 0.0, green: 0.2, blue: 0.6)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            )
    }
}

// MARK: - Blue Card Modifier

/// Applies the blue card styling with shadow to a view
public struct BlueCardModifier: ViewModifier {
    private let cornerRadius: CGFloat
    
    public init(cornerRadius: CGFloat = 16) {
        self.cornerRadius = cornerRadius
    }
    
    public func body(content: Content) -> some View {
        content
            .padding()
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.0, green: 0.5, blue: 1.0),
                                Color(red: 0.0, green: 0.3, blue: 0.8)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: Color.black.opacity(0.3), radius: 15, x: 0, y: 8)
            )
    }
}

// MARK: - Blue Text Modifier

/// Applies white text styling for the blue theme
public struct BlueTextModifier: ViewModifier {
    private let isPrimary: Bool
    
    public init(isPrimary: Bool = true) {
        self.isPrimary = isPrimary
    }
    
    public func body(content: Content) -> some View {
        content
            .foregroundColor(isPrimary ? .white : .white.opacity(0.8))
    }
}

// MARK: - View Extensions

extension View {
    /// Applies the blue gradient background to the view
    public func blueBackground() -> some View {
        modifier(BlueBackgroundModifier())
    }
    
    /// Applies the blue card styling with shadow to the view
    public func blueCard(cornerRadius: CGFloat = 16) -> some View {
        modifier(BlueCardModifier(cornerRadius: cornerRadius))
    }
    
    /// Applies white text styling for the blue theme
    public func blueText(isPrimary: Bool = true) -> some View {
        modifier(BlueTextModifier(isPrimary: isPrimary))
    }
}

// MARK: - Preview

#if DEBUG
struct BlueThemeModifiers_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            Text("Blue Background")
                .font(.headline)
                .blueText()
                .padding()
                .blueCard()
            
            Text("Blue Card")
                .font(.headline)
                .blueText()
                .padding()
                .blueCard()
            
            Text("Secondary Text")
                .font(.subheadline)
                .blueText(isPrimary: false)
        }
        .padding()
        .blueBackground()
    }
}
#endif

import SwiftUI

/// A visualization component that displays an artistic representation of voice input
/// with animated waves that respond to audio levels.
public struct VoiceWaveVisualization: View {
    // Audio level input (0.0 to 1.0)
    let audioLevel: Double

    // Configuration options
    var primaryColor: Color = .blue
    var secondaryColor: Color = .cyan
    var backgroundColor: Color? = nil
    var showBackground: Bool = true
    var animationSpeed: Double = 1.0
    var waveCount: Int = 3
    var sensitivity: Double = 1.0

    // Animation state
    @State private var phase: CGFloat = 0
    @State private var animationTrigger = false

    public init(
        audioLevel: Double,
        primaryColor: Color = .blue,
        secondaryColor: Color = .cyan,
        backgroundColor: Color? = nil,
        showBackground: Bool = true,
        animationSpeed: Double = 1.0,
        waveCount: Int = 3,
        sensitivity: Double = 1.0
    ) {
        self.audioLevel = audioLevel
        self.primaryColor = primaryColor
        self.secondaryColor = secondaryColor
        self.backgroundColor = backgroundColor
        self.showBackground = showBackground
        self.animationSpeed = animationSpeed
        self.waveCount = waveCount
        self.sensitivity = sensitivity
    }

    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Optional background
                if showBackground, let backgroundColor = backgroundColor {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(backgroundColor)
                }

                // Multiple wave layers with different phases and amplitudes
                ForEach(0..<waveCount, id: \.self) { index in
                    VoiceWaveShape(
                        phase: phase + CGFloat(index) * 0.5,
                        amplitude: amplitudeForWave(index),
                        frequency: frequencyForWave(index)
                    )
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [primaryColor, secondaryColor]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .opacity(opacityForWave(index))
                    .clipShape(RoundedRectangle(cornerRadius: 12)) // Clip to prevent overflow
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity) // Constrain to parent view size
            .onAppear {
                // Start continuous animation
                withAnimation(Animation.linear(duration: 2 / animationSpeed).repeatForever(autoreverses: false)) {
                    phase = .pi * 2
                    animationTrigger.toggle()
                }
            }
            .onChange(of: audioLevel) { _, _ in
                // Trigger subtle animation changes when audio level changes
                withAnimation(.easeInOut(duration: 0.2)) {
                    animationTrigger.toggle()
                }
            }
        }
    }

    // Calculate amplitude for each wave based on audio level and wave index
    private func amplitudeForWave(_ index: Int) -> CGFloat {
        let baseAmplitude = CGFloat(audioLevel * sensitivity)
        let normalizedIndex = CGFloat(index) / CGFloat(waveCount)

        // Scale amplitude based on wave index (first waves are larger)
        let scaleFactor = 1.0 - (normalizedIndex * 0.3)

        // Add subtle randomness for more natural look
        let randomness = CGFloat.random(in: 0.95...1.05)

        return max(0.05, baseAmplitude * scaleFactor * randomness)
    }

    // Calculate frequency for each wave
    private func frequencyForWave(_ index: Int) -> CGFloat {
        // Base frequency with slight variations per wave
        return 1.0 + CGFloat(index) * 0.2
    }

    // Calculate opacity for each wave
    private func opacityForWave(_ index: Int) -> Double {
        // First wave is most opaque, later waves more transparent
        // Significantly reduced opacity to prevent UI overlay issues
        let baseOpacity = 0.3 - (Double(index) * 0.1)
        return max(0.1, min(0.3, baseOpacity))
    }
}

/// Shape that draws a sine wave with configurable parameters
private struct VoiceWaveShape: Shape {
    var phase: CGFloat
    var amplitude: CGFloat
    var frequency: CGFloat

    // Make the shape animatable
    var animatableData: CGFloat {
        get { phase }
        set { phase = newValue }
    }

    func path(in rect: CGRect) -> Path {
        let width = rect.width
        let height = rect.height
        let midHeight = height / 2
        let wavelength = width / frequency

        var path = Path()
        path.move(to: CGPoint(x: 0, y: midHeight))

        // Draw a sine wave across the width
        for x in stride(from: 0, through: width, by: 1) {
            let relativeX = x / wavelength
            let sine = sin(relativeX + phase)
            let y = midHeight + sine * midHeight * amplitude
            path.addLine(to: CGPoint(x: x, y: y))
        }

        // Complete the path by adding lines to the bottom corners
        path.addLine(to: CGPoint(x: width, y: height))
        path.addLine(to: CGPoint(x: 0, y: height))
        path.closeSubpath()

        return path
    }
}

// MARK: - Preview
#if DEBUG
struct VoiceWaveVisualization_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            // Low audio level
            VoiceWaveVisualization(
                audioLevel: 0.2,
                primaryColor: .blue,
                secondaryColor: .cyan,
                backgroundColor: Color.black.opacity(0.1),
                animationSpeed: 0.8
            )
            .frame(height: 100)
            .previewDisplayName("Low Audio Level")

            // Medium audio level
            VoiceWaveVisualization(
                audioLevel: 0.5,
                primaryColor: .purple,
                secondaryColor: .pink,
                backgroundColor: Color.black.opacity(0.1),
                animationSpeed: 1.0
            )
            .frame(height: 100)
            .previewDisplayName("Medium Audio Level")

            // High audio level
            VoiceWaveVisualization(
                audioLevel: 0.9,
                primaryColor: .red,
                secondaryColor: .orange,
                backgroundColor: Color.black.opacity(0.1),
                animationSpeed: 1.2
            )
            .frame(height: 100)
            .previewDisplayName("High Audio Level")
        }
        .padding()
        .background(Color.white)
    }
}
#endif

import NeuroLoopCore
import SwiftUI

/// AudioWaveformStyle defines the rendering style for the waveform.
public enum AudioWaveformStyle {
    case line
    case filled
    case bars
}

/// AudioWaveform is a reusable SwiftUI visualization for displaying audio sample data as a waveform.
///
/// Usage:
/// ```swift
/// AudioWaveform(samples: samples, style: .line)
/// ```
/// - Parameters:
///   - samples: Array of audio sample amplitudes
///   - color: Foreground color
///   - backgroundColor: Background color
///   - lineWidth: Line or bar width
///   - style: Rendering style (.line, .filled, .bars)
public struct AudioWaveform: View {
    private let samples: [Float]
    private let color: Color
    private let backgroundColor: Color
    private let lineWidth: CGFloat
    private let style: AudioWaveformStyle

    /// Create an AudioWaveform
    /// - Parameters:
    ///   - samples: Array of audio sample amplitudes (normalized -1...1 or 0...1)
    ///   - color: Foreground color
    ///   - backgroundColor: Background color
    ///   - lineWidth: Line or bar width
    ///   - style: Rendering style (.line, .filled, .bars)
    public init(
        samples: [Float],
        color: Color,
        backgroundColor: Color = Color.gray.opacity(0.2),
        lineWidth: CGFloat = 2,
        style: AudioWaveformStyle = .line
    ) {
        self.samples = samples
        self.color = color
        self.backgroundColor = backgroundColor
        self.lineWidth = lineWidth
        self.style = style
    }

    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                Rectangle().fill(backgroundColor)
                switch style {
                case .line:
                    Path { path in
                        let width = geometry.size.width
                        let height = geometry.size.height
                        let midY = height / 2
                        let sampleCount = samples.count
                        let step = width / CGFloat(sampleCount - 1)

                        path.move(to: CGPoint(x: 0, y: midY))

                        for (index, sample) in samples.enumerated() {
                            let x = CGFloat(index) * step
                            let y = midY + CGFloat(sample) * height / 2
                            path.addLine(to: CGPoint(x: x, y: y))
                        }
                    }
                    .stroke(color, lineWidth: lineWidth)
                case .filled:
                    Path { path in
                        let width = geometry.size.width
                        let height = geometry.size.height
                        let centerY = height / 2
                        let sampleCount = samples.count
                        guard sampleCount > 1 else { return }
                        let step = width / CGFloat(sampleCount - 1)
                        // Top half
                        for (index, sample) in samples.enumerated() {
                            let x = CGFloat(index) * step
                            let amplitude = CGFloat(sample) * centerY
                            let y = centerY - amplitude
                            if index == 0 {
                                path.move(to: CGPoint(x: x, y: y))
                            } else {
                                path.addLine(to: CGPoint(x: x, y: y))
                            }
                        }
                        // Bottom half (reverse)
                        for (index, sample) in samples.enumerated().reversed() {
                            let x = CGFloat(index) * step
                            let amplitude = CGFloat(sample) * centerY
                            let y = centerY + amplitude
                            path.addLine(to: CGPoint(x: x, y: y))
                        }
                        path.closeSubpath()
                    }
                    .fill(color)
                case .bars:
                    let width = geometry.size.width
                    let height = geometry.size.height
                    let barCount = samples.count
                    let spacing: CGFloat = 2
                    let barWidth = max(
                        1, (width - CGFloat(barCount - 1) * spacing) / CGFloat(barCount))
                    HStack(alignment: .center, spacing: spacing) {
                        ForEach(0..<barCount, id: \.self) { index in
                            let sample = samples[index]
                            let barHeight = CGFloat(sample) * height
                            Rectangle()
                                .fill(color)
                                .frame(width: barWidth, height: barHeight)
                                .offset(y: (height - barHeight) / 2)
                        }
                    }
                    .frame(width: width, height: height, alignment: .center)
                }
            }
        }
    }
}

// MARK: - Preview
#if DEBUG
    struct AudioWaveform_Previews: PreviewProvider {
        static var previews: some View {
            VStack(spacing: 20) {
                AudioWaveform(
                    samples: [0.2, 0.5, 0.8, 0.3, 0.6, 0.9, 0.4, 0.7],
                    color: ThemeManager.shared.currentTheme.accentColor.asColor,
                    backgroundColor: ThemeManager.shared.currentTheme.cardBackgroundColor.asColor
                        .opacity(0.2)
                )
                .frame(height: 60)
                .padding()
                .background(ThemeManager.shared.currentTheme.backgroundColor.asColor)
                .previewDisplayName("Default")

                AudioWaveform(
                    samples: [0.1, 0.3, 0.5, 0.7, 0.9, 0.7, 0.5, 0.3],
                    color: ThemeManager.shared.currentTheme.successColor.color,
                    backgroundColor: ThemeManager.shared.currentTheme.cardBackgroundColor.asColor
                        .opacity(0.2)
                )
                .frame(height: 60)
                .padding()
                .background(ThemeManager.shared.currentTheme.backgroundColor.asColor)
                .previewDisplayName("Success")

                AudioWaveform(
                    samples: [0.9, 0.7, 0.5, 0.3, 0.1, 0.3, 0.5, 0.7],
                    color: ThemeManager.shared.currentTheme.errorColor.color,
                    backgroundColor: ThemeManager.shared.currentTheme.cardBackgroundColor.asColor
                        .opacity(0.2)
                )
                .frame(height: 60)
                .padding()
                .background(ThemeManager.shared.currentTheme.backgroundColor.asColor)
                .previewDisplayName("Error")
            }
            .environmentObject(ThemeManager.shared)
        }
    }
#endif

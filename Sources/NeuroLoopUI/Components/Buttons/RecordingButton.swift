import NeuroLoopCore
import <PERSON>euroLoopTypes
import SwiftUI

/// RecordingButton is a reusable SwiftUI button for audio recording control (record/stop).
///
/// Usage:
/// ```swift
/// RecordingButton(isRecording: isRecording) { ... }
/// ```
/// - Parameters:
///   - isRecording: Whether audio is currently recording
///   - action: Action to perform on tap

public struct RecordingButton: View {
    @EnvironmentObject private var themeManager: ThemeManager
    let isRecording: Bool
    let onTap: () -> Void
    let haptics: HapticGenerating

    // Optional audio level for visualization (0.0 to 1.0)
    var audioLevel: Double = 0.0

    // Animation state
    @State private var pulseEffect = false
    @State private var rotationEffect = false

    public var body: some View {
        Button(action: {
            Task {
                await haptics.playImpact(style: .medium)
                onTap()
            }
        }) {
            ZStack {
                if isRecording {
                    // Recording state (red button with pulsing effect)

                    // Audio level visualization rings (multiple rings that scale with audio level)
                    ForEach(0..<5) { i in
                        let scale = 1.0 + Double(i) * 0.15 + (audioLevel * 0.5)
                        Circle()
                            .stroke(themeManager.currentTheme.errorColor.color.opacity(0.7 - Double(i) * 0.15))
                            .frame(width: 80, height: 80)
                            .scaleEffect(scale)
                            .opacity(audioLevel > 0.1 ? 1.0 - Double(i) * 0.2 : 0.0)
                    }

                    // Outer pulsing circle
                    Circle()
                        .fill(themeManager.currentTheme.errorColor.asColor.opacity(0.3))
                        .frame(width: 80, height: 80)
                        .scaleEffect(pulseEffect ? 1.2 : 1.0)
                        .opacity(pulseEffect ? 0.5 : 0.8)
                        .animation(
                            Animation.easeInOut(duration: 1.0)
                                .repeatForever(autoreverses: true),
                            value: pulseEffect
                        )
                        .onAppear {
                            pulseEffect = true
                        }

                    // Main red button
                    Circle()
                        .fill(themeManager.currentTheme.errorColor.asColor)
                        .frame(width: 60, height: 60)
                        .shadow(color: Color.black.opacity(0.5), radius: 10, x: 0, y: 6)

                    // Stop icon
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.white)
                        .frame(width: 20, height: 20)


                } else {
                    // Ready to record state (white button with blue border and blue center)

                    // Blue outer ring with subtle animation
                    Circle()
                        .fill(Color(red: 0.2, green: 0.4, blue: 0.8).opacity(0.5))
                        .frame(width: 70, height: 70)
                        .shadow(color: Color.black.opacity(0.5), radius: 10, x: 0, y: 6)
                        .scaleEffect(pulseEffect ? 1.05 : 1.0)
                        .animation(
                            Animation.easeInOut(duration: 2.0)
                                .repeatForever(autoreverses: true),
                            value: pulseEffect
                        )
                        .onAppear {
                            pulseEffect = true
                        }

                    // White main circle
                    Circle()
                        .fill(Color.white)
                        .frame(width: 55, height: 55)

                    // Blue inner circle with subtle rotation
                    Circle()
                        .fill(Color(red: 0.2, green: 0.4, blue: 0.8))
                        .frame(width: 22, height: 22)
                        .rotationEffect(Angle(degrees: rotationEffect ? 360 : 0))
                        .animation(
                            Animation.linear(duration: 8.0)
                                .repeatForever(autoreverses: false),
                            value: rotationEffect
                        )
                        .onAppear {
                            rotationEffect = true
                        }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

struct RecordingButton_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            RecordingButton(
                isRecording: true,
                onTap: {},
                haptics: PreviewMockHapticManager(),
                audioLevel: 0.7
            )
            .previewDisplayName("Recording with Audio")

            RecordingButton(
                isRecording: true,
                onTap: {},
                haptics: PreviewMockHapticManager(),
                audioLevel: 0.0
            )
            .previewDisplayName("Recording No Audio")

            RecordingButton(
                isRecording: false,
                onTap: {},
                haptics: PreviewMockHapticManager()
            )
            .previewDisplayName("Not Recording")
        }
        .padding()
        .previewLayout(.sizeThatFits)
        .environmentObject(ThemeManager.shared)
    }
}

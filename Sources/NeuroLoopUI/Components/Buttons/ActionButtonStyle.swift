import NeuroLoopCore
import NeuroLoopTypes
import SwiftUI

public struct ActionButtonStyle: ButtonStyle {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let isDestructive: Bool

    public init(isDestructive: Bool = false) {
        self.isDestructive = isDestructive
    }

    public func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(
                        isDestructive
                            ? themeManager.currentTheme.errorColor.color
                            : themeManager.currentTheme.accentColor.asColor
                    )
            )
            .foregroundColor(.white)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#if DEBUG
    struct ActionButtonStyle_Previews: PreviewProvider {
        static var previews: some View {
            VStack(spacing: 20) {
                Button("Normal Action") {}
                    .buttonStyle(ActionButtonStyle())

                But<PERSON>("Destructive Action") {}
                    .buttonStyle(ActionButtonStyle(isDestructive: true))
            }
            .padding()
            .environmentObject(NeuroLoopCore.ThemeManager.shared)
        }
    }
#endif

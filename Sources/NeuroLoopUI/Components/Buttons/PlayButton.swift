import SwiftUI

/// PlayButton is a reusable SwiftUI button for audio playback control (play/pause).
///
/// Usage:
/// ```swift
/// PlayButton(isPlaying: isPlaying) { ... }
/// ```
/// - Parameters:
///   - isPlaying: Whether audio is currently playing
///   - size: Button size
///   - color: Button color
///   - action: Action to perform on tap

public struct PlayButton: View {
    private let isPlaying: Bool
    private let size: CGFloat
    private let color: Color
    private let action: () -> Void
    
    public init(
        isPlaying: Bool,
        size: CGFloat = 50,
        color: Color = .blue,
        action: @escaping () -> Void
    ) {
        self.isPlaying = isPlaying
        self.size = size
        self.color = color
        self.action = action
    }
    
    public var body: some View {
        Button(action: action) {
            ZStack {
                // Background
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: size, height: size)
                
                // Inner circle
                Circle()
                    .fill(color)
                    .frame(width: size * 0.8, height: size * 0.8)
                
                // Play/Pause icon
                if isPlaying {
                    // Pause icon
                    HStack(spacing: size * 0.1) {
                        Rectangle()
                            .fill(Color.white)
                            .frame(width: size * 0.15, height: size * 0.3)
                        
                        Rectangle()
                            .fill(Color.white)
                            .frame(width: size * 0.15, height: size * 0.3)
                    }
                } else {
                    // Play icon
                    Triangle()
                        .fill(Color.white)
                        .frame(width: size * 0.3, height: size * 0.3)
                        .offset(x: size * 0.05)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Triangle Shape

private struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.minX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.midY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.closeSubpath()
        return path
    }
}

// MARK: - Preview

@available(iOS 17.0, macOS 14.0, *)
struct PlayButton_Previews: PreviewProvider {
    static var previews: some View {
        HStack(spacing: 20) {
            PlayButton(
                isPlaying: false,
                size: 50,
                color: .blue
            ) {}
            
            PlayButton(
                isPlaying: true,
                size: 50,
                color: .blue
            ) {}
        }
        .padding()
    }
} 
import NeuroLoopCore
import SwiftUI

public struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    public init(title: String, isSelected: Bool, action: @escaping () -> Void) {
        self.title = title
        self.isSelected = isSelected
        self.action = action
    }

    public var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(isSelected ? .semibold : .regular)
                .foregroundColor(accessibleForegroundColor)
                .dynamicTypeSize(.large ... .xxLarge)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(accessibleBackgroundColor)
                )
                .overlay(
                    Capsule()
                        .stroke(
                            isSelected
                                ? Color.clear
                                : themeManager.currentTheme.accentColor.asColor.opacity(0.3),
                            lineWidth: 1
                        )
                )
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Category: \(title)\(isSelected ? ", selected" : "")")
        .accessibilityHint("Double tap to filter by this category")
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.accentColor, lineWidth: isFocused ? 2 : 0)
                .opacity(isFocused ? 1 : 0)
        )
        .accessibilityAddTraits(isSelected ? .isSelected : .isButton)
        // Accessibility: Ensure color contrast and visible focus indicator
        #if os(macOS)
            .focused($isFocused)
        #endif
    }

    // MARK: - Accessibility Color Helpers
    @FocusState private var isFocused: Bool
    private var accessibleBackgroundColor: Color {
        isSelected
            ? themeManager.currentTheme.accentColor.asColor
            : themeManager.currentTheme.cardBackgroundColor.asColor
    }
    private var accessibleForegroundColor: Color {
        if isSelected {
            // Use white or black depending on accent color brightness
            return accessibleBackgroundColor.isDarkColor ? .white : .black
        } else {
            return themeManager.currentTheme.primaryTextColor.color
        }
    }
}

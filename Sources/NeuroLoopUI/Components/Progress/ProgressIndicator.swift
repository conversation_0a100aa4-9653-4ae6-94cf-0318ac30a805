import SwiftUI
import NeuroLoopInterfaces

/// ProgressIndicator is a reusable SwiftUI component for displaying circular or linear progress.
///
/// Usage:
/// ```swift
/// ProgressIndicator(progress: 0.5)
/// ```
/// - Parameters:
///   - progress: Progress value (0.0 to 1.0)
///   - lineWidth: Stroke width
///   - size: Indicator size
///   - backgroundColor: Background color
///   - foregroundColor: Progress color

/// A circular progress indicator
@available(iOS 17.0, macOS 14.0, *)
public struct ProgressIndicator: View {
    // MARK: - Properties

    private let progress: Double
    private let lineWidth: CGFloat
    private let size: CGFloat
    private let backgroundColor: Color
    private let foregroundColor: Color
    private let showText: Bool
    private let text: String?

    // MARK: - Initialization

    public init(
        progress: Double,
        lineWidth: CGFloat = 4,
        size: CGFloat = 40,
        backgroundColor: Color,
        foregroundColor: Color,
        showText: Bool = true,
        text: String? = nil
    ) {
        self.progress = progress
        self.lineWidth = lineWidth
        self.size = size
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.showText = showText
        self.text = text
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(backgroundColor, lineWidth: lineWidth)

            // Progress circle
            Circle()
                .trim(from: 0, to: CGFloat(min(progress, 1.0)))
                .stroke(
                    foregroundColor,
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut, value: progress)

            // Text
            if showText {
                if let text = text {
                    Text(text)
                        .font(.system(size: size * 0.3, weight: .bold))
                        .foregroundColor(foregroundColor)
                } else {
                    Text("\(Int(progress * 100))%")
                        .font(.system(size: size * 0.3, weight: .bold))
                        .foregroundColor(foregroundColor)
                }
            }
        }
        .frame(width: size, height: size)
        // Accessibility: Announce progress changes politely, support keyboard focus on macOS
        .accessibilityElement(children: .ignore)
        .accessibilityLabel(Text("Progress indicator"))
        .accessibilityValue(Text("\(Int(progress * 100)) percent"))
        .accessibilityAddTraits(.updatesFrequently)
        #if os(macOS)
        .focusable()
        #endif
    }
}

// MARK: - Preview

#if DEBUG
@available(iOS 17.0, macOS 14.0, *)
struct ProgressIndicator_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Preview with different progress values
            HStack(spacing: 20) {
                ProgressIndicator(
                    progress: 0.25,
                    backgroundColor: .gray.opacity(0.2),
                    foregroundColor: .blue
                )
                
                ProgressIndicator(
                    progress: 0.5,
                    backgroundColor: .gray.opacity(0.2),
                    foregroundColor: .green
                )
                
                ProgressIndicator(
                    progress: 0.75,
                    backgroundColor: .gray.opacity(0.2),
                    foregroundColor: .purple
                )
                
                ProgressIndicator(
                    progress: 1.0,
                    backgroundColor: .gray.opacity(0.2),
                    foregroundColor: .orange
                )
            }
            .padding()
            .previewLayout(.sizeThatFits)
            .previewDisplayName("Different Progress Values")
            
            // Preview with different sizes
            HStack(spacing: 20) {
                ProgressIndicator(
                    progress: 0.5,
                    lineWidth: 2,
                    size: 30,
                    backgroundColor: .gray.opacity(0.2),
                    foregroundColor: .blue
                )
                
                ProgressIndicator(
                    progress: 0.5,
                    lineWidth: 4,
                    size: 40,
                    backgroundColor: .gray.opacity(0.2),
                    foregroundColor: .blue
                )
                
                ProgressIndicator(
                    progress: 0.5,
                    lineWidth: 6,
                    size: 50,
                    backgroundColor: .gray.opacity(0.2),
                    foregroundColor: .blue
                )
            }
            .padding()
            .previewLayout(.sizeThatFits)
            .previewDisplayName("Different Sizes")
        }
    }
}
#endif

import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public struct StreakCalendar: View {
    let days: [StreakCalendarDay]
    let columns: Int
    let size: CGFloat
    let spacing: CGFloat
    let completedColor: Color
    let incompleteColor: Color
    let backgroundColor: Color
    
    public init(
        days: [StreakCalendarDay],
        columns: Int = 7,
        size: CGFloat = 30,
        spacing: CGFloat = 4,
        completedColor: Color = .green,
        incompleteColor: Color = .red,
        backgroundColor: Color = Color.gray.opacity(0.2)
    ) {
        self.days = days
        self.columns = columns
        self.size = size
        self.spacing = spacing
        self.completedColor = completedColor
        self.incompleteColor = incompleteColor
        self.backgroundColor = backgroundColor
    }
    
    public var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.fixed(size), spacing: spacing), count: columns), spacing: spacing) {
            ForEach(days) { day in
                StreakCalendarDayView(
                    day: day,
                    size: size,
                    completedColor: completedColor,
                    incompleteColor: incompleteColor,
                    backgroundColor: backgroundColor
                )
            }
        }
    }
}

@available(iOS 17.0, macOS 14.0, *)
private struct StreakCalendarDayView: View {
    let day: StreakCalendarDay
    let size: CGFloat
    let completedColor: Color
    let incompleteColor: Color
    let backgroundColor: Color
    
    var body: some View {
        ZStack {
            // Background
            RoundedRectangle(cornerRadius: 4)
                .fill(backgroundColor)
            
            // Progress circle
            Circle()
                .trim(from: 0, to: CGFloat(min(day.progress, 1.0)))
                .stroke(
                    day.isComplete ? completedColor : incompleteColor,
                    style: StrokeStyle(
                        lineWidth: 2,
                        lineCap: .round
                    )
                )
                .rotationEffect(.degrees(-90))
                .padding(4)
            
            // Day number
            Text("\(Calendar.current.component(.day, from: day.date))")
                .font(.system(size: size * 0.3))
                .foregroundColor(day.isComplete ? completedColor : incompleteColor)
        }
        .frame(width: size, height: size)
    }
}

#if DEBUG
@available(iOS 17.0, macOS 14.0, *)
struct StreakCalendar_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Preview with sample data
            let days = (0..<30).map { i in
                let date = Calendar.current.date(byAdding: .day, value: -i, to: Date())!
                let isComplete = Bool.random()
                let repetitions = isComplete ? 100 : Int.random(in: 0...99)
                let progress = Double(repetitions) / 100.0
                
                return StreakCalendarDay(
                    date: date,
                    repetitions: repetitions,
                    isComplete: isComplete,
                    progress: progress
                )
            }
            
            StreakCalendar(
                days: days,
                columns: 7,
                size: 30,
                spacing: 4,
                completedColor: .green,
                incompleteColor: .red,
                backgroundColor: Color.gray.opacity(0.2)
            )
            .padding()
            .previewLayout(.sizeThatFits)
            .previewDisplayName("Sample Data")
            
            // Preview with different colors
            StreakCalendar(
                days: days,
                columns: 7,
                size: 30,
                spacing: 4,
                completedColor: .blue,
                incompleteColor: .orange,
                backgroundColor: Color.gray.opacity(0.2)
            )
            .padding()
            .previewLayout(.sizeThatFits)
            .previewDisplayName("Different Colors")
        }
    }
}
#endif 
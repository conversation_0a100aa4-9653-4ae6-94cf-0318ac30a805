import SwiftUI

/// A progress bar component that displays a visual representation of progress.
///
/// The ProgressBar is a customizable progress indicator that can be used to show
/// completion status, loading progress, or any other percentage-based metric.
///
/// Example usage:
/// ```swift
/// ProgressBar(value: 0.75, color: .blue)
///     .frame(height: 6)
/// ```
@available(iOS 15.0, macOS 12.0, *)
public struct ProgressBar: View {
    // MARK: - Properties
    
    /// The current progress value between 0.0 and 1.0
    private let value: Double
    
    /// The color to use for the progress indicator
    private let color: Color
    
    // MARK: - Initialization
    
    /// Creates a new progress bar with the specified value and color.
    ///
    /// - Parameters:
    ///   - value: The progress value between 0.0 and 1.0. Values outside this range will be clamped.
    ///   - color: The color to use for the progress indicator.
    public init(value: Double, color: Color) {
        self.value = min(max(value, 0.0), 1.0)
        self.color = color
    }
    
    // MARK: - Body
    
    public var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // Background
                RoundedRectangle(cornerRadius: 3)
                    .fill(Color.gray.opacity(0.2))
                
                // Progress
                RoundedRectangle(cornerRadius: 3)
                    .fill(color)
                    .frame(width: geometry.size.width * CGFloat(value))
            }
        }
        .accessibilityElement(children: .ignore)
        .accessibilityLabel("Progress indicator")
        .accessibilityValue("\(Int(value * 100)) percent complete")
    }
}

// MARK: - Preview

#if DEBUG
@available(iOS 15.0, macOS 12.0, *)
struct ProgressBar_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // Different progress values
            Group {
                ProgressBar(value: 0.25, color: .blue)
                    .frame(height: 6)
                ProgressBar(value: 0.5, color: .green)
                    .frame(height: 6)
                ProgressBar(value: 0.75, color: .orange)
                    .frame(height: 6)
                ProgressBar(value: 1.0, color: .purple)
                    .frame(height: 6)
            }
            .padding(.horizontal)
            
            // Different heights
            Group {
                ProgressBar(value: 0.5, color: .blue)
                    .frame(height: 4)
                ProgressBar(value: 0.5, color: .blue)
                    .frame(height: 8)
                ProgressBar(value: 0.5, color: .blue)
                    .frame(height: 12)
            }
            .padding(.horizontal)
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
#endif 
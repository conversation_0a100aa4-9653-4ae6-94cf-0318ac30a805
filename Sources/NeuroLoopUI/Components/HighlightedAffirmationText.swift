import SwiftUI
import NeuroLoopTypes
import NeuroLoopCore

/// A component that displays affirmation text with word-by-word highlighting
/// based on speech recognition results
public struct HighlightedAffirmationText: View {
    @EnvironmentObject private var themeManager: ThemeManager

    // The full affirmation text to display
    let affirmationText: String

    // The recognized speech text
    let recognizedText: String

    // Whether the component is in recording mode
    let isRecording: Bool

    // Optional title to display above the affirmation
    var title: String?

    // Whether to show the "Repeat Exactly" instruction
    var showRepeatExactly: Bool = false

    // Action to perform when the "Repeat Exactly" button is tapped
    var onRepeatExactly: (() -> Void)?

    public init(
        affirmationText: String,
        recognizedText: String,
        isRecording: Bool,
        title: String? = nil,
        showRepeatExactly: Bool = false,
        onRepeatExactly: (() -> Void)? = nil
    ) {
        self.affirmationText = affirmationText
        self.recognizedText = recognizedText
        self.isRecording = isRecording
        self.title = title
        self.showRepeatExactly = showRepeatExactly
        self.onRepeatExactly = onRepeatExactly
    }

    public var body: some View {
        VStack(spacing: 8) {
            // Optional title
            if let title = title {
                Text(title)
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
                    .padding(.bottom, 4)
            }

            // "Repeat Exactly" instruction
            if showRepeatExactly {
                Button(action: {
                    onRepeatExactly?()
                }) {
                    Label("Repeat Exactly", systemImage: "speaker.wave.2")
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                }
                .padding(.bottom, 4)
            }

            // Affirmation text with word highlighting
            highlightedTextView
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(themeManager.currentTheme.cardBackgroundColor.asColor)
                        .shadow(color: Color.black.opacity(0.2), radius: 5, x: 0, y: 2)
                )
        }
    }

    // The highlighted text view
    private var highlightedTextView: some View {
        let affirmationWords = affirmationText.split(separator: " ").map(String.init)
        let recognizedWords = recognizedText.lowercased().split(separator: " ").map(String.init)

        return VStack(alignment: .leading, spacing: 4) {
            Text(attributedString(
                words: affirmationWords,
                recognizedWords: recognizedWords,
                isRecording: isRecording
            ))
            .font(.system(size: 18, weight: .medium))
            .lineSpacing(4)
            .multilineTextAlignment(.center)
            .fixedSize(horizontal: false, vertical: true)
        }
    }

    // Create an attributed string with highlighted words
    private func attributedString(
        words: [String],
        recognizedWords: [String],
        isRecording: Bool
    ) -> AttributedString {
        var result = AttributedString("")

        for (index, word) in words.enumerated() {
            // Create a word with proper casing from the original
            var wordAttr = AttributedString(word)

            // Default styling
            wordAttr.foregroundColor = themeManager.currentTheme.primaryTextColor.color

            // Only apply highlighting if recording
            if isRecording && !recognizedWords.isEmpty {
                // Check if this word has been recognized (case insensitive)
                let wordLower = word.lowercased()
                let isRecognized = recognizedWords.contains { $0.lowercased() == wordLower }

                if isRecognized {
                    // Word has been recognized - highlight in green
                    wordAttr.foregroundColor = Color.green
                    wordAttr.backgroundColor = Color.green.opacity(0.2)
                    wordAttr.font = .system(size: 18, weight: .bold)
                }
            }

            // Add the word to the result
            result.append(wordAttr)

            // Add a space after each word except the last one
            if index < words.count - 1 {
                result.append(AttributedString(" "))
            }
        }

        return result
    }
}

// MARK: - Preview

struct HighlightedAffirmationText_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            HighlightedAffirmationText(
                affirmationText: "I am confident and capable in everything I do",
                recognizedText: "I am confident",
                isRecording: true,
                title: "Your Affirmation",
                showRepeatExactly: true
            )

            HighlightedAffirmationText(
                affirmationText: "I am confident and capable in everything I do",
                recognizedText: "I am confident and capable in everything I do",
                isRecording: true
            )

            HighlightedAffirmationText(
                affirmationText: "I am confident and capable in everything I do",
                recognizedText: "",
                isRecording: false
            )
        }
        .padding()
        .environmentObject(ThemeManager.shared)
    }
}

import NeuroLoopCore
import NeuroLoopTypes
import SwiftUI

public struct CardModifier: ViewModifier {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let cornerRadius: CGFloat
    let shadowRadius: CGFloat

    public init(cornerRadius: CGFloat = 16, shadowRadius: CGFloat = 10) {
        self.cornerRadius = cornerRadius
        self.shadowRadius = shadowRadius
    }

    public func body(content: Content) -> some View {
        content
            .padding()
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(themeManager.currentTheme.cardBackgroundColor.asColor)
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                        radius: shadowRadius,
                        x: 0,
                        y: 5
                    )
            )
    }
}

extension View {
    public func cardStyle(cornerRadius: CGFloat = 16, shadowRadius: CGFloat = 10) -> some View {
        modifier(CardModifier(cornerRadius: cornerRadius, shadowRadius: shadowRadius))
    }
}

#if DEBUG
    struct CardModifier_Previews: PreviewProvider {
        static var previews: some View {
            VStack {
                Text("Card Content")
                    .cardStyle()
            }
            .padding()
            .environmentObject(NeuroLoopCore.ThemeManager.shared)
        }
    }
#endif

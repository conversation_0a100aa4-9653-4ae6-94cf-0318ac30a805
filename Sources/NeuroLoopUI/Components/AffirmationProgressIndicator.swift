import SwiftUI
import NeuroLoopTypes
import NeuroLoopCore

/// A component that displays progress through an affirmation
/// based on speech recognition results
public struct AffirmationProgressIndicator: View {
    @EnvironmentObject private var themeManager: ThemeManager

    // The full affirmation text
    let affirmationText: String

    // The recognized speech text
    let recognizedText: String

    // Whether the component is in recording mode
    let isRecording: Bool

    // Optional color for the progress bar
    var progressColor: Color?

    // Optional background color for the progress bar
    var backgroundColor: Color?

    // Optional height for the progress bar
    var height: CGFloat = 8

    // Optional corner radius for the progress bar
    var cornerRadius: CGFloat = 4

    // Optional animation duration
    var animationDuration: Double = 0.3

    public init(
        affirmationText: String,
        recognizedText: String,
        isRecording: Bool,
        progressColor: Color? = nil,
        backgroundColor: Color? = nil,
        height: CGFloat = 8,
        cornerRadius: CGFloat = 4,
        animationDuration: Double = 0.3
    ) {
        self.affirmationText = affirmationText
        self.recognizedText = recognizedText
        self.isRecording = isRecording
        self.progressColor = progressColor
        self.backgroundColor = backgroundColor
        self.height = height
        self.cornerRadius = cornerRadius
        self.animationDuration = animationDuration
    }

    public var body: some View {
        VStack(spacing: 4) {
            // Progress bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(backgroundColor ?? Color.gray.opacity(0.3))
                        .frame(height: height)

                    // Progress
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(progressColor ?? themeManager.currentTheme.accentColor.asColor)
                        .frame(width: max(0, min(geometry.size.width, geometry.size.width * progress)), height: height)
                        .animation(.easeInOut(duration: animationDuration), value: progress)
                }
            }
            .frame(height: height)

            // Word count indicator
            HStack {
                Text("\(recognizedWordCount) of \(totalWordCount) words")
                    .font(.caption)
                    .foregroundColor(Color.gray)

                Spacer()

                // Percentage indicator
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .foregroundColor(Color.gray)
            }
        }
    }

    // Calculate progress through the affirmation
    private var progress: Double {
        guard !affirmationText.isEmpty else { return 0 }

        if !isRecording || recognizedText.isEmpty {
            return 0
        }

        // Calculate progress based on word count
        return Double(recognizedWordCount) / Double(totalWordCount)
    }

    // Count of words in the affirmation
    private var totalWordCount: Int {
        affirmationText.split(separator: " ").count
    }

    // Count of recognized words
    private var recognizedWordCount: Int {
        guard !recognizedText.isEmpty else { return 0 }

        let affirmationWords = affirmationText.lowercased().split(separator: " ").map(String.init)
        let recognizedWords = recognizedText.lowercased().split(separator: " ").map(String.init)

        // Count how many words from the affirmation have been recognized
        var count = 0

        // For each word in the affirmation, check if it's in the recognized text
        for word in affirmationWords {
            if recognizedWords.contains(word) {
                count += 1
            }
        }

        return count
    }
}

// MARK: - Preview

struct AffirmationProgressIndicator_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            AffirmationProgressIndicator(
                affirmationText: "I am confident and capable in everything I do",
                recognizedText: "I am confident",
                isRecording: true
            )
            .previewDisplayName("Partial Progress")

            AffirmationProgressIndicator(
                affirmationText: "I am confident and capable in everything I do",
                recognizedText: "I am confident and capable in everything I do",
                isRecording: true,
                progressColor: .green
            )
            .previewDisplayName("Complete Progress")

            AffirmationProgressIndicator(
                affirmationText: "I am confident and capable in everything I do",
                recognizedText: "",
                isRecording: false
            )
            .previewDisplayName("Not Recording")
        }
        .padding()
        .environmentObject(ThemeManager.shared)
    }
}

import SwiftUI
import NeuroLoopTypes
import NeuroLoopCore

/// A component that displays an audio waveform visualization
/// based on the current audio level
public struct AudioWaveformView: View {
    @EnvironmentObject private var themeManager: ThemeManager

    // The current audio level (0.0 to 1.0)
    let audioLevel: Double

    // Whether the waveform is active (recording)
    let isActive: Bool

    // The color of the waveform
    var color: Color?

    // The number of bars in the waveform
    var barCount: Int = 30

    // Animation state
    @State private var phase: Double = 0

    public init(
        audioLevel: Double,
        isActive: Bool,
        color: Color? = nil,
        barCount: Int = 30
    ) {
        self.audioLevel = audioLevel
        self.isActive = isActive
        self.color = color
        self.barCount = barCount
    }

    public var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<barCount, id: \.self) { index in
                WaveformBar(
                    index: index,
                    barCount: barCount,
                    audioLevel: audioLevel,
                    phase: phase,
                    isActive: isActive,
                    color: color ?? themeManager.currentTheme.accentColor.asColor
                )
            }
        }
        .frame(height: 40)
        .onAppear {
            // Start the animation when the view appears
            withAnimation(Animation.linear(duration: 2).repeatForever(autoreverses: false)) {
                phase = 2 * .pi
            }
        }
    }
}

/// A single bar in the waveform
private struct WaveformBar: View {
    let index: Int
    let barCount: Int
    let audioLevel: Double
    let phase: Double
    let isActive: Bool
    let color: Color

    var body: some View {
        // Calculate the height of the bar based on the audio level and position
        let heightPercentage = barHeight(for: index, of: barCount, audioLevel: audioLevel, phase: phase)

        // The bar
        RoundedRectangle(cornerRadius: 2)
            .fill(color.opacity(isActive ? 1.0 : 0.5))
            .frame(height: isActive ? max(3, 40 * heightPercentage) : 3)
            .animation(.spring(response: 0.3), value: audioLevel)
    }

    /// Calculate the height of a bar based on its position and the audio level
    private func barHeight(for index: Int, of total: Int, audioLevel: Double, phase: Double) -> Double {
        guard isActive else { return 0.1 }

        // Calculate the position of the bar in the waveform (0.0 to 1.0)
        let position = Double(index) / Double(total)

        // Create a sine wave pattern
        let sineValue = sin(phase + position * 2 * .pi)

        // Scale the sine wave based on the audio level
        let baseHeight = 0.1 + (audioLevel * 0.5) // Base height between 0.1 and 0.6
        let variableHeight = audioLevel * 0.4 * sineValue // Variable height based on sine wave

        // Combine base height and variable height
        return baseHeight + variableHeight
    }
}

// MARK: - Preview

struct AudioWaveformView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            AudioWaveformView(audioLevel: 0.8, isActive: true)
                .previewDisplayName("Active - High Level")

            AudioWaveformView(audioLevel: 0.3, isActive: true)
                .previewDisplayName("Active - Low Level")

            AudioWaveformView(audioLevel: 0.8, isActive: false)
                .previewDisplayName("Inactive")

            AudioWaveformView(audioLevel: 0.6, isActive: true, color: .red)
                .previewDisplayName("Custom Color")
        }
        .padding()
        .background(Color.black.opacity(0.1))
        .environmentObject(ThemeManager.shared)
    }
}

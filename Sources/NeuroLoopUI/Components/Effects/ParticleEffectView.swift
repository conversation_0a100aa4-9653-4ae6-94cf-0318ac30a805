import SwiftUI

/// A view that displays a particle effect animation
public struct ParticleEffectView: View {
    @Binding var showingParticles: Bool
    var particlePosition: CGPoint

    public init(showingParticles: Binding<Bool>, particlePosition: CGPoint) {
        self._showingParticles = showingParticles
        self.particlePosition = particlePosition
    }

    public var body: some View {
        ZStack {
            ForEach(0..<8) { index in
                Circle()
                    .fill(Color.white)
                    .frame(width: 4, height: 4)
                    .position(
                        x: particlePosition.x + cos(Double(index) * .pi / 4) * 20,
                        y: particlePosition.y + sin(Double(index) * .pi / 4) * 20
                    )
                    .opacity(showingParticles ? 0 : 1)
                    .scaleEffect(showingParticles ? 0 : 1)
                    .animation(
                        .easeOut(duration: 0.5)
                            .repeatCount(1, autoreverses: false),
                        value: showingParticles
                    )
            }
        }
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                showingParticles = false
            }
        }
    }
}

#Preview {
    ZStack {
        Color.blue
            .edgesIgnoringSafeArea(.all)

        ParticleEffectView(
            showingParticles: .constant(true),
            particlePosition: CGPoint(x: 100, y: 100)
        )
    }
}

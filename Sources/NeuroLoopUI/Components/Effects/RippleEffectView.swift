import SwiftUI

public struct RippleEffectView: View {
    @Binding var showingRipple: Bool
    var ripplePosition: CGPoint

    public init(showingRipple: Binding<Bool>, ripplePosition: CGPoint) {
        self._showingRipple = showingRipple
        self.ripplePosition = ripplePosition
    }

    public var body: some View {
        Circle()
            .stroke(Color.white.opacity(0.5), lineWidth: 2)
            .frame(width: 50, height: 50)
            .position(ripplePosition)
            .scaleEffect(showingRipple ? 2 : 0.5)
            .opacity(showingRipple ? 0 : 1)
            .animation(
                .easeOut(duration: 0.5)
                    .repeatCount(1, autoreverses: false),
                value: showingRipple
            )
            .onAppear {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    showingRipple = false
                }
            }
    }
}

#Preview {
    ZStack {
        Color.blue
            .edgesIgnoringSafeArea(.all)

        RippleEffectView(
            showingRipple: .constant(true),
            ripplePosition: CGPoint(x: 100, y: 100)
        )
    }
}

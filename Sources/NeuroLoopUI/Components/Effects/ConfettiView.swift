import SwiftUI

/// ConfettiView is a reusable SwiftUI visualization for displaying a confetti celebration effect.
///
/// Usage:
/// ```swift
/// ConfettiView()
/// ```

public struct ConfettiView: View {
    @State private var particles: [ConfettiParticle] = []
    
    public init() {}
    
    public var body: some View {
        ZStack {
            ForEach(particles) { particle in
                particle.view
            }
        }
        .onAppear {
            generateParticles()
        }
    }
    
    private func generateParticles() {
        for _ in 0..<100 {
            particles.append(ConfettiParticle())
        }
    }
}

// MARK: - Confetti Particle

private struct ConfettiParticle: Identifiable {
    let id = UUID()
    let position: CGPoint
    let size: CGFloat
    let color: Color
    let rotation: Double
    let view: AnyView
    
    @MainActor
    init() {
#if canImport(UIKit)
        let screenWidth: CGFloat = {
            var width: CGFloat = 375 // Default fallback
            if Thread.isMainThread {
                width = UIScreen.main.bounds.width
            } else {
                DispatchQueue.main.sync {
                    width = UIScreen.main.bounds.width
                }
            }
            return width
        }()
        position = CGPoint(
            x: CGFloat.random(in: 0...screenWidth),
            y: CGFloat.random(in: -50...0)
        )
#else
        position = CGPoint(
            x: CGFloat.random(in: 0...500), // Fallback width for macOS
            y: CGFloat.random(in: -50...0)
        )
#endif
        size = CGFloat.random(in: 5...15)
        color = [.red, .blue, .green, .yellow, .purple, .orange].randomElement()!
        rotation = Double.random(in: 0...360)
        
        // Randomly choose between circle and rectangle
        if Bool.random() {
            view = AnyView(
                Circle()
                    .fill(color)
                    .frame(width: size, height: size)
                    .position(position)
                    .rotationEffect(.degrees(rotation))
                    .animation(
                        Animation.linear(duration: Double.random(in: 2...4))
                            .repeatForever(autoreverses: false), value: position
                    )
            )
        } else {
            view = AnyView(
                Rectangle()
                    .fill(color)
                    .frame(width: size, height: size)
                    .position(position)
                    .rotationEffect(.degrees(rotation))
                    .animation(
                        Animation.linear(duration: Double.random(in: 2...4))
                            .repeatForever(autoreverses: false), value: position
                    )
            )
        }
    }
}

// MARK: - Preview

struct ConfettiView_Previews: PreviewProvider {
    static var previews: some View {
        ConfettiView()
            .frame(width: 300, height: 300)
    }
} 
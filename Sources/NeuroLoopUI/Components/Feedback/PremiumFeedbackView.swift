import NeuroLoopCore
import SwiftUI

public struct PremiumFeedbackView: View {
    public enum FeedbackType {
        case success(message: String)
        case error(message: String)
    }

    let type: FeedbackType
    let dismissAction: () -> Void
    var autoDismiss: Bool = true
    var dismissDelay: Double = 2.5

    @State private var isVisible: Bool = false
    @Environment(\.accessibilityReduceMotion) private var reduceMotion
    @EnvironmentObject private var themeManager: ThemeManager

    public init(
        type: FeedbackType, dismissAction: @escaping () -> Void, autoDismiss: Bool = true,
        dismissDelay: Double = 2.5
    ) {
        self.type = type
        self.dismissAction = dismissAction
        self.autoDismiss = autoDismiss
        self.dismissDelay = dismissDelay
    }

    public var body: some View {
        ZStack {
            backgroundView
            feedbackContent
        }
        .onAppear {
            isVisible = true
            triggerHaptic()
            if autoDismiss {
                DispatchQueue.main.asyncAfter(deadline: .now() + dismissDelay) {
                    dismissAction()
                }
            }
        }
        .onDisappear {
            isVisible = false
        }
    }

    private var backgroundView: some View {
        Color.black.opacity(0.4)
            .edgesIgnoringSafeArea(.all)
            .onTapGesture { dismissAction() }
            .accessibilityHidden(true)
    }

    private var feedbackContent: some View {
        VStack(spacing: 20) {
            if case .success = type {
                Image(systemName: "checkmark.seal.fill")
                    .font(.system(size: 50))
                    .foregroundColor(themeManager.currentTheme.successColor.foreground)
                    .accessibilityHidden(true)
            } else {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(themeManager.currentTheme.errorColor.foreground)
                    .accessibilityHidden(true)
            }

            Text(typeTitle)
                .font(.headline)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.foreground)
                .accessibilityAddTraits(.isHeader)
                .accessibilityLabel(typeTitle)

            Text(typeMessage)
                .font(.subheadline)
                .multilineTextAlignment(.center)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.foreground)
                .accessibilityLabel(typeMessage)

            Button(action: dismissAction) {
                Text("OK")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(
                        themeManager.currentTheme.accentColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.accentColor.asColor,
                                    themeManager.currentTheme.accentColor.asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .cornerRadius(10)
            }
            .accessibilityIdentifier("PremiumFeedbackDismissButton")
        }
        .padding(30)
        .background(cardBackground)
        .overlay(confettiOverlay)
        .opacity(isVisible ? 1 : 0)
        .animation(reduceMotion ? nil : .easeInOut(duration: 0.3), value: isVisible)
        .accessibilityElement(children: .combine)
        .accessibilityAddTraits(.isModal)
        .accessibilityIdentifier("PremiumFeedbackView")
    }

    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 15)
            .fill(
                themeManager.currentTheme.cardBackgroundColor.asGradient
                    ?? LinearGradient(
                        gradient: Gradient(colors: [
                            themeManager.currentTheme.cardBackgroundColor.asColor,
                            themeManager.currentTheme.cardBackgroundColor.asColor,
                        ]),
                        startPoint: .top, endPoint: .bottom
                    )
            )
            .shadow(
                color: themeManager.currentTheme.shadowColor.foreground.opacity(0.2),
                radius: 8, x: 0, y: 4)
    }

    private var confettiOverlay: some View {
        Group {
            if case .success = type {
                ConfettiView()
            }
        }
    }

    private var typeTitle: String {
        switch type {
        case .success:
            return "Success"
        case .error:
            return "Error"
        }
    }

    private var typeMessage: String {
        switch type {
        case .success:
            return "Your premium features have been activated successfully."
        case .error:
            return "There was an error processing your premium purchase. Please try again."
        }
    }

    private func triggerHaptic() {
        #if os(iOS)
            if case .success = type {
                let generator = UINotificationFeedbackGenerator()
                generator.notificationOccurred(.success)
            } else {
                let generator = UINotificationFeedbackGenerator()
                generator.notificationOccurred(.error)
            }
        #endif
    }
}

import NeuroLoopCore
import SwiftUI

struct LoadingView: View {
    let message: String
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        ZStack {
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 20) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(themeManager.currentTheme.accentColor.asColor)

                Text(message)
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.2), radius: 8,
                        x: 0, y: 4)
            )
        }
    }
}

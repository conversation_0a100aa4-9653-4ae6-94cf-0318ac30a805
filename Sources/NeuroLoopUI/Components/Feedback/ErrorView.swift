import NeuroLoopCore
import SwiftUI

struct ErrorView: View {
    let error: Error
    let retryAction: () -> Void
    let dismissAction: () -> Void
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        ZStack {
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(themeManager.currentTheme.errorColor.color)

                Text("Error")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                Text(error.localizedDescription)
                    .font(.subheadline)
                    .multilineTextAlignment(.center)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)

                HStack(spacing: 20) {
                    Button(action: retryAction) {
                        Text("Retry")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(themeManager.currentTheme.accentColor.asColor)
                            .cornerRadius(10)
                    }

                    Button(action: dismissAction) {
                        Text("Dismiss")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(themeManager.currentTheme.secondaryTextColor.color)
                            .cornerRadius(10)
                    }
                }
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.2), radius: 8,
                        x: 0, y: 4)
            )
        }
    }
}

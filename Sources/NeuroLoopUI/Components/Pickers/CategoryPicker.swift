import NeuroLoopTypes
import SwiftUI

public struct CategoryPicker: View {
    @Binding var selectedCategory: AffirmationCategory?
    var includeAllOption: Bool

    public init(selectedCategory: Binding<AffirmationCategory?>, includeAllOption: Bool = false) {
        self._selectedCategory = selectedCategory
        self.includeAllOption = includeAllOption
    }

    public var body: some View {
        Picker("Category", selection: $selectedCategory) {
            if includeAllOption {
                Text("All").tag(AffirmationCategory?.none)
            }
            ForEach(AffirmationCategory.allCases, id: \.self) { category in
                Text(category.rawValue.capitalized).tag(Optional(category))
            }
        }
    }
}

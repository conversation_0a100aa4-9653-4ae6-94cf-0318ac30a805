import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import Swift<PERSON>

@available(iOS 17.0, macOS 14.0, *)
public struct ThemePickerView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @Binding private var selectedTheme: Theme

    public init(selectedTheme: Binding<Theme>) {
        self._selectedTheme = selectedTheme
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Theme")
                .font(.headline)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(themeManager.builtInThemes + themeManager.customThemes, id: \.id) {
                        theme in
                        ThemePreviewCard(
                            theme: theme,
                            isSelected: theme.id == selectedTheme.id,
                            onSelect: {
                                selectedTheme = theme
                                themeManager.setTheme(theme)
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor)
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        )
    }
}

@available(iOS 17.0, macOS 14.0, *)
private struct ThemePreviewCard: View {
    let theme: Theme
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        VStack(spacing: 8) {
            RoundedRectangle(cornerRadius: 12)
                .fill(theme.backgroundColor.asColor)
                .frame(width: 60, height: 60)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            isSelected ? Color.accentColor : Color.clear,
                            lineWidth: 2
                        )
                )

            Text(theme.name)
                .font(.caption)
                .foregroundColor(theme.primaryTextColor.color)
        }
        .onTapGesture(perform: onSelect)
    }
}

#if DEBUG
    struct ThemePickerView_Previews: PreviewProvider {
        static var previews: some View {
            ThemePickerView(selectedTheme: .constant(Theme.light))
                .environmentObject(NeuroLoopCore.ThemeManager.shared)
        }
    }
#endif

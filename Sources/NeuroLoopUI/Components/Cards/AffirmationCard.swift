import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

// Import Theme from NeuroLoopTypes
// Make it public to fix the visibility issue
@available(iOS 13.0, macOS 10.15, *)
public typealias CardTheme = NeuroLoopTypes.Theme

/// A card displaying an affirmation with its progress
@available(iOS 17.0, macOS 14.0, *)
public struct AffirmationCard: View {
    // MARK: - Properties

    private let affirmation: any AffirmationProtocol
    private let onTap: () -> Void
    private let onFavorite: () -> Void

    @State private var isPressed = false
    @EnvironmentObject private var themeManager: ThemeManager

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        onTap: @escaping () -> Void,
        onFavorite: @escaping () -> Void
    ) {
        self.affirmation = affirmation
        self.onTap = onTap
        self.onFavorite = onFavorite
    }

    // MARK: - Body

    public var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Affirmation text with quotation marks
                ZStack(alignment: .leading) {
                    // Left quotation mark
                    Text("""
                         "
                         """)
                        .font(.system(size: 60, weight: .bold, design: .serif))
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color.opacity(0.3))
                        .padding(.leading, -10)
                        .padding(.top, -20)
                        .shadow(color: themeManager.currentTheme.shadowColor.color.opacity(0.2), radius: 2, x: 1, y: 1)
                        .zIndex(1)

                    VStack(alignment: .leading, spacing: 8) {
                        // Main affirmation text
                        Text(affirmation.text)
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
                            .multilineTextAlignment(.center)
                            .fixedSize(horizontal: false, vertical: true) // Allow text to expand vertically
                            .minimumScaleFactor(0.7) // Scale down text if needed to fit
                            .lineSpacing(4) // Add some space between lines
                            .padding(.top, 16)
                            .padding(.horizontal, 16)

                        HStack {
                            Spacer()

                            // Right quotation mark
                            Text("""
                                 "
                                 """)
                                .font(.system(size: 60, weight: .bold, design: .serif))
                                .foregroundColor(themeManager.currentTheme.primaryTextColor.color.opacity(0.3))
                                .padding(.trailing, -10)
                                .padding(.bottom, -20)
                                .shadow(color: themeManager.currentTheme.shadowColor.color.opacity(0.2), radius: 2, x: 1, y: 1)
                        }
                    }

                    // Favorite button
                    VStack {
                        HStack {
                            Spacer()

                            Button(action: onFavorite) {
                                Image(systemName: affirmation.isFavorite ? "heart.fill" : "heart")
                                    .foregroundColor(
                                        affirmation.isFavorite
                                            ? .red : themeManager.currentTheme.secondaryTextColor.color
                                    )
                                    .font(.title3)
                                    .padding(8)
                            }
                            .buttonStyle(.plain)
                        }
                        Spacer()
                    }
                }

                // Category tag
                Text(affirmation.category.displayName)
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(themeManager.currentTheme.accentColor.asColor.opacity(0.1))
                    )

                // Stats row
                HStack {
                    Label(
                        "\(affirmation.currentRepetitions) repetitions",
                        systemImage: "repeat"
                    )
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)

                    Spacer()

                    if let lastPracticed = affirmation.lastRepetitionDate {
                        Label(
                            lastPracticed.formatted(date: .abbreviated, time: .shortened),
                            systemImage: "clock"
                        )
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.0, green: 0.5, blue: 1.0),
                                Color(red: 0.0, green: 0.3, blue: 0.8)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: Color.black.opacity(0.3), radius: 15, x: 0, y: 8)
            )
            .foregroundColor(.white)
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        }
        .buttonStyle(.plain)
        .simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in isPressed = true }
                .onEnded { _ in isPressed = false }
        )
    }

    // MARK: - Sections

    private var headerSection: some View {
        HStack {
            Label(
                affirmation.category.displayName,
                systemImage: affirmation.category.iconName
            )
            .font(.caption)
            .foregroundColor(accessibleCategoryForeground)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(accessibleCategoryBackground)
            .cornerRadius(8)
            .accessibilityLabel(Text("Category: \(affirmation.category.displayName)"))
            .accessibilityAddTraits(.isStaticText)

            Spacer()

            let favoriteHint =
                "Double tap to " + (affirmation.isFavorite ? "remove from" : "add to")
                + " favorites"
            Button(action: onFavorite) {
                if #available(macOS 11.0, *) {
                    Image(systemName: affirmation.isFavorite ? "star.fill" : "star")
                        .foregroundColor(
                            affirmation.isFavorite
                                ? .yellow : themeManager.currentTheme.secondaryTextColor.asColor
                        )
                        .font(.system(size: 16, weight: .semibold))
                        .symbolEffect(.bounce, value: affirmation.isFavorite)
                } else {
                    Text(affirmation.isFavorite ? "★" : "☆")
                        .foregroundColor(
                            affirmation.isFavorite
                                ? .yellow : themeManager.currentTheme.secondaryTextColor.asColor
                        )
                        .font(.headline)
                }
            }
            .buttonStyle(PlainButtonStyle())
            .accessibilityLabel(
                Text(affirmation.isFavorite ? "Remove from favorites" : "Add to favorites")
            )
            .accessibilityAddTraits(.isButton)
            .accessibilityHint(Text(favoriteHint))
        }
    }

    private var affirmationTextSection: some View {
        Text(affirmation.text)
            .font(.headline)
            .dynamicTypeSize(.large ... .xxLarge)
            .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            .multilineTextAlignment(.leading)
            .lineLimit(3)
            .padding(.vertical, 4)
            .accessibilityLabel(Text("Affirmation: \(affirmation.text)"))
    }

    private var progressSection: some View {
        Group {
            if affirmation.hasActiveCycle {
                VStack(spacing: 8) {
                    HStack {
                        Text("Cycle Progress")
                            .font(.caption)
                            .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                        Spacer()
                        Text("\(Int(affirmation.cycleProgress * 100))%")
                            .font(.caption)
                            .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                    }
                    ProgressBar(
                        value: affirmation.cycleProgress,
                        color: themeManager.currentTheme.accentColor.asColor
                    )
                    .frame(height: 6)
                    .accessibilityLabel(
                        Text("Cycle progress: \(Int(affirmation.cycleProgress * 100)) percent"))
                }
            } else {
                HStack {
                    Text("Completed Cycles: \(affirmation.completedCycles)")
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                    Spacer()
                    Button(action: onTap) {
                        Text("Start Cycle")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(themeManager.currentTheme.accentColor.asColor)
                            .cornerRadius(8)
                    }
                }
            }
        }
    }

    private var audioSection: some View {
        Group {
            if affirmation.recordingURL != nil {
                HStack {
                    Image(systemName: "waveform")
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                        .font(.system(size: 14, weight: .semibold))
                        .accessibilityHidden(true)
                    Text("Audio Available")
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                        .accessibilityLabel(Text("Audio recording available"))
                }
            }
        }
    }

    // MARK: - Card Background & Border
    private var cardBackground: some View {
        ZStack {
            if let gradient = themeManager.currentTheme.cardBackgroundColor.asGradient {
                RoundedRectangle(cornerRadius: 15)
                    .fill(gradient)
            } else {
                RoundedRectangle(cornerRadius: 15)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                            ]),
                            startPoint: .top, endPoint: .bottom
                        )
                    )
            }
        }
        .shadow(
            color: themeManager.currentTheme.shadowColor.asColor.opacity(0.2), radius: 8, x: 0, y: 4)
    }

    private var cardBorder: some View {
        RoundedRectangle(cornerRadius: 15)
            .stroke(themeManager.currentTheme.borderColor.asColor.opacity(0.1), lineWidth: 1)
    }

    // MARK: - Tap Handler
    @MainActor private func handleTap() {
        #if canImport(UIKit)
            if UIAccessibility.isReduceMotionEnabled {
                onTap()
            } else {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    onTap()
                }
            }
        #else
            onTap()
        #endif
    }

    private var categoryColor: Color {
        switch affirmation.category {
        case .confidence:
            return .blue
        case .health:
            return .green
        case .abundance:
            return .purple
        case .relationships:
            return .pink
        case .personal:
            return .orange
        default:
            return .gray
        }
    }

    // MARK: - Category Color Accessibility Helpers
    private var accessibleCategoryBackground: Color {
        // Use accent color with higher opacity for better contrast, fallback to categoryColor if needed
        if themeManager.currentTheme.name.lowercased().contains("dark") {
            return categoryColor.opacity(0.35)
        } else {
            return categoryColor.opacity(0.18)
        }
    }
    private var accessibleCategoryForeground: Color {
        // Use white for dark backgrounds, otherwise use primary text color
        if accessibleCategoryBackground.isDarkColor {
            return .white
        } else {
            return themeManager.currentTheme.primaryTextColor.asColor
        }
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct AffirmationCard_Previews: PreviewProvider {
        static var previews: some View {
            Group {
                AffirmationCard(
                    affirmation: createPreviewAffirmation(
                        text: "I am confident and capable",
                        category: .confidence
                    ),
                    onTap: {},
                    onFavorite: {}
                )
                .previewDisplayName("Default")
            }
            .padding()
            .previewLayout(.sizeThatFits)
        }
    }
#endif

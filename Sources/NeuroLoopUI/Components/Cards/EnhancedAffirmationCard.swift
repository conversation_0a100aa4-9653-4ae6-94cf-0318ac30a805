import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
// NOTE: This component requires macOS 12.0+ for full icon and ProgressIndicator support.
import SwiftUI

/// An enhanced card view for displaying an affirmation with visual feedback and animations
@available(iOS 17.0, macOS 14.0, *)
public struct EnhancedAffirmationCard: View {
    /// The affirmation to display
    let affirmation: any AffirmationProtocol

    /// The action to perform when the card is tapped
    let onTap: () -> Void

    /// The action to perform when the favorite button is tapped
    let onFavorite: () -> Void

    /// Haptic feedback generator
    let haptics: NeuroLoopTypes.HapticGenerating

    /// Whether the card is currently pressed
    @State private var isPressed: Bool = false

    /// The theme manager for styling
    @EnvironmentObject private var themeManager: ThemeManager

    /// Creates a new enhanced affirmation card
    /// - Parameters:
    ///   - affirmation: The affirmation to display
    ///   - onTap: The action to perform when the card is tapped
    ///   - onFavorite: The action to perform when the favorite button is tapped
    ///   - haptics: The haptic feedback generator to use (defaults to HapticManager.shared)
    @MainActor
    @available(iOS 17.0, macOS 14.0, *)
    public init(
        affirmation: any AffirmationProtocol,
        onTap: @escaping () -> Void,
        onFavorite: @escaping () -> Void,
        haptics: NeuroLoopTypes.HapticGenerating
    ) {
        self.affirmation = affirmation
        self.onTap = onTap
        self.onFavorite = onFavorite
        self.haptics = haptics
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            categoryRow
            affirmationText
            progressIndicators
        }
    }

    var categoryRow: some View {
        HStack {
            Text(affirmation.category.displayName)
                .font(Font.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(accessibleCategoryBackground)
                .foregroundColor(accessibleCategoryForeground)
                .cornerRadius(8)
                .accessibility(label: Text("Category: \(affirmation.category.displayName)"))
            Spacer()
            Button(action: {
                Task {
                    await haptics.playImpact(style: .light)
                    onFavorite()
                }
            }) {
                if #available(macOS 11.0, *) {
                    Image(systemName: affirmation.isFavorite ? "star.fill" : "star")
                        .foregroundColor(
                            affirmation.isFavorite
                                ? .yellow : themeManager.currentTheme.secondaryTextColor.color
                        )
                        .font(.system(size: 18, weight: .semibold))
                        .symbolEffect(.bounce, value: affirmation.isFavorite)
                } else {
                    Text(affirmation.isFavorite ? "★" : "☆")
                        .foregroundColor(
                            affirmation.isFavorite
                                ? .yellow : themeManager.currentTheme.secondaryTextColor.color
                        )
                        .font(.headline)
                        .buttonStyle(PlainButtonStyle())
                }
            }
            .accessibility(
                label: Text(affirmation.isFavorite ? "Remove from favorites" : "Add to favorites"))
        }
    }

    var affirmationText: some View {
        Text(affirmation.text)
            .font(Font.headline)
            .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
            .lineLimit(3)
            .padding(.vertical, 4)
            .accessibility(label: Text("Affirmation: \(affirmation.text)"))
    }

    var progressIndicators: some View {
        HStack {
            todayProgress
            Spacer()
            cycleProgress
            Spacer()
            if affirmation.recordingURL != nil {
                Image(systemName: "waveform")
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    .font(.system(size: 16, weight: .semibold))
                    .accessibility(label: Text("Has audio recording"))
            }
        }
    }

    var todayProgress: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Today")
                .font(Font.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
            if #available(macOS 12.0, iOS 15.0, *) {
                ProgressView(value: affirmation.todayProgress)
                    .accentColor(themeManager.currentTheme.accentColor.asColor)
                    .frame(width: 60)
            } else {
                // Fallback for older OS versions
                Text("Today's progress: \(Int(affirmation.todayProgress * 100))%")
                    .font(Font.caption)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
            }
        }
        .padding()
        .background(
            ZStack {
                RoundedRectangle(cornerRadius: 15)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                ], startPoint: .top, endPoint: .bottom)
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.2), radius: 8,
                        x: 0, y: 4)
            }
        )
        .overlay(
            RoundedRectangle(cornerRadius: 15)
                .stroke(themeManager.currentTheme.borderColor.color.opacity(0.1), lineWidth: 1)
        )
        .contentShape(Rectangle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
        .onTapGesture {
            Task {
                await haptics.playImpact(style: .medium)
                withAnimation {
                    isPressed = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation {
                        isPressed = false
                        onTap()
                    }
                }
            }
        }
        .accessibilityElement(children: .combine)
        .accessibility(addTraits: .isButton)
        .accessibility(hint: Text("Double tap to view details"))
    }

    var cycleProgress: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Cycle")
                .font(Font.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
            if #available(macOS 12.0, iOS 15.0, *) {
                ProgressView(value: affirmation.cycleProgress)
                    .accentColor(themeManager.currentTheme.accentColor.asColor)
                    .frame(width: 60)
            } else {
                // Fallback for older OS versions
                Text("Cycle progress: \(Int(affirmation.cycleProgress * 100))%")
                    .font(Font.caption)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
            }
        }
        .padding()
        .background(
            ZStack {
                RoundedRectangle(cornerRadius: 15)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                ], startPoint: .top, endPoint: .bottom)
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.2), radius: 8,
                        x: 0, y: 4)
            }
        )
        .overlay(
            RoundedRectangle(cornerRadius: 15)
                .stroke(themeManager.currentTheme.borderColor.color.opacity(0.1), lineWidth: 1)
        )
        .contentShape(Rectangle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
        .onTapGesture {
            Task {
                await haptics.playImpact(style: .medium)
                withAnimation {
                    isPressed = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation {
                        isPressed = false
                        onTap()
                    }
                }
            }
        }
        .accessibilityElement(children: .combine)
        .accessibility(addTraits: .isButton)
        .accessibility(hint: Text("Double tap to view details"))
    }

    /// Returns the color for the affirmation category
    private var categoryColor: Color {
        switch affirmation.category {
        case .confidence:
            return .blue
        case .health:
            return .green
        case .abundance:
            return .purple
        case .relationships:
            return .pink
        case .personal:
            return .orange
        default:
            return .gray
        }
    }

    /// Returns the background color for the category label
    private var accessibleCategoryBackground: Color {
        if themeManager.currentTheme.name.lowercased().contains("dark") {
            return categoryColor.opacity(0.35)
        } else {
            return categoryColor.opacity(0.18)
        }
    }

    /// Returns the foreground color for the category label
    private var accessibleCategoryForeground: Color {
        if accessibleCategoryBackground.isDarkColor {
            return .white
        } else {
            return themeManager.currentTheme.primaryTextColor.color
        }
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct EnhancedAffirmationCard_Previews: PreviewProvider {
        static var previews: some View {
            Group {
                EnhancedAffirmationCard(
                    affirmation: createPreviewAffirmation(
                        text: "I am confident and capable",
                        category: .confidence
                    ),
                    onTap: {},
                    onFavorite: {},
                    haptics: HapticManager.shared
                )
                .previewDisplayName("Default")
            }
            .padding()
            .previewLayout(.sizeThatFits)
        }
    }
#endif

import SwiftUI
import NeuroLoopCore
import NeuroLoopTypes

/// A reusable blue card background with shadow
public struct BlueCardBackground: View {
    private let cornerRadius: CGFloat
    
    public init(cornerRadius: CGFloat = 16) {
        self.cornerRadius = cornerRadius
    }
    
    public var body: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.0, green: 0.5, blue: 1.0),
                        Color(red: 0.0, green: 0.3, blue: 0.8)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .shadow(color: Color.black.opacity(0.3), radius: 15, x: 0, y: 8)
    }
}

/// A reusable blue background gradient
public struct BlueBackgroundGradient: View {
    public init() {}
    
    public var body: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.1, green: 0.4, blue: 0.8),
                Color(red: 0.0, green: 0.2, blue: 0.6)
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .ignoresSafeArea()
    }
}

/// A container view that applies the blue card styling to its content
public struct BlueCard<Content: View>: View {
    private let content: Content
    private let cornerRadius: CGFloat
    
    public init(cornerRadius: CGFloat = 16, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.cornerRadius = cornerRadius
    }
    
    public var body: some View {
        content
            .padding()
            .background(BlueCardBackground(cornerRadius: cornerRadius))
    }
}

#if DEBUG
struct BlueCardBackground_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            BlueBackgroundGradient()
            
            VStack(spacing: 20) {
                BlueCard {
                    Text("Blue Card")
                        .font(.headline)
                        .foregroundColor(.white)
                }
                
                Text("Regular Text")
                    .font(.body)
                    .foregroundColor(.white)
                    .padding()
                    .background(BlueCardBackground())
            }
            .padding()
        }
    }
}
#endif

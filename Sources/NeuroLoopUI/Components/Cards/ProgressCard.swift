import NeuroLoopCore
import NeuroLoopTypes
import SwiftUI

public struct ProgressCard: View {
    let statistic: Statistic
    let theme: NeuroLoopTypes.Theme

    public init(statistic: Statistic, theme: NeuroLoopTypes.Theme) {
        self.statistic = statistic
        self.theme = theme
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(statistic.title)
                .font(.caption)
                .foregroundColor(theme.secondaryTextColor.color)

            Text(statistic.value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(theme.primaryTextColor.color)

            Text(statistic.subtitle)
                .font(.caption2)
                .foregroundColor(theme.secondaryTextColor.color)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    AnyShapeStyle(
                        theme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [theme.cardBackgroundColor.asColor]),
                                startPoint: .top, endPoint: .bottom))
                )
                .shadow(color: theme.shadowColor.color.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

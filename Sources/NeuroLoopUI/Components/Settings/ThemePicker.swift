import NeuroLoopCore
import NeuroLoopTypes
import SwiftUI

public struct ThemePicker: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @State private var selectedTheme: Theme

    public init() {
        _selectedTheme = State(initialValue: ThemeManager.shared.currentTheme)
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Theme")
                .font(.headline)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(ThemeManager.shared.availableThemes, id: \.id) { theme in
                        ThemePreviewCard(
                            theme: theme,
                            isSelected: theme.id == selectedTheme.id,
                            onSelect: {
                                selectedTheme = theme
                                themeManager.setTheme(theme)
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor)
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        )
    }
}

private struct ThemePreviewCard: View {
    let theme: Theme
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        VStack(spacing: 8) {
            RoundedRectangle(cornerRadius: 12)
                .fill(theme.backgroundColor.asColor)
                .frame(width: 60, height: 60)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            isSelected ? Color.accentColor : Color.clear,
                            lineWidth: 2
                        )
                )

            Text(theme.name)
                .font(.caption)
                .foregroundColor(theme.primaryTextColor.color)
        }
        .onTapGesture(perform: onSelect)
    }
}

#if DEBUG
    struct ThemePicker_Previews: PreviewProvider {
        static var previews: some View {
            ThemePicker()
                .environmentObject(NeuroLoopCore.ThemeManager.shared)
        }
    }
#endif

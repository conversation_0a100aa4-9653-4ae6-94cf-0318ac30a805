import SwiftUI
import NeuroLoopTypes
import NeuroLoopCore

/// A component that displays contextual tips to help users
public struct ContextualTipView: View {
    @EnvironmentObject private var themeManager: ThemeManager

    // The tip to display
    let tip: ContextualTip

    // Whether to show the tip
    @Binding var isVisible: Bool

    // Optional action to perform when the tip is dismissed
    var onDismiss: (() -> Void)?

    // Optional action to perform when the tip is acknowledged
    var onAcknowledge: (() -> Void)?

    public init(
        tip: ContextualTip,
        isVisible: Binding<Bool>,
        onDismiss: (() -> Void)? = nil,
        onAcknowledge: (() -> Void)? = nil
    ) {
        self.tip = tip
        self._isVisible = isVisible
        self.onDismiss = onDismiss
        self.onAcknowledge = onAcknowledge
    }

    public var body: some View {
        if isVisible {
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    // Icon
                    Image(systemName: tip.iconName)
                        .font(.system(size: 20))
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)

                    // Title
                    Text(tip.title)
                        .font(.headline)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                    Spacer()

                    // Dismiss button
                    Button(action: {
                        withAnimation {
                            isVisible = false
                            onDismiss?()
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(Color.gray.opacity(0.7))
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                // Description
                Text(tip.description)
                    .font(.body)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                    .fixedSize(horizontal: false, vertical: true)

                // Action button (if provided)
                if let actionTitle = tip.actionTitle {
                    Button(action: {
                        withAnimation {
                            isVisible = false
                            onAcknowledge?()
                        }
                    }) {
                        Text(actionTitle)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(themeManager.currentTheme.accentColor.asColor)
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.top, 4)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(themeManager.currentTheme.cardBackgroundColor.asColor)
                    .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
            )
            .padding(.horizontal)
            .transition(.opacity.combined(with: .move(edge: .bottom)))
            .animation(.spring(response: 0.4), value: isVisible)
        }
    }
}

// ContextualTip model moved to Sources/NeuroLoopUI/Models/ContextualTip.swift

// MARK: - Preview

struct ContextualTipView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            ContextualTipView(
                tip: .speakClearly,
                isVisible: .constant(true)
            )

            ContextualTipView(
                tip: .autoStop,
                isVisible: .constant(true)
            )

            ContextualTipView(
                tip: .noisyEnvironment,
                isVisible: .constant(true)
            )
        }
        .padding()
        .environmentObject(ThemeManager.shared)
    }
}

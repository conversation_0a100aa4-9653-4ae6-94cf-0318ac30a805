import Foundation
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes

// Helper function to create preview affirmations using AffirmationDTO
public func createPreviewAffirmation(
    text: String,
    category: AffirmationCategory = .custom,
    recordingURL: URL? = nil,
    createdAt: Date = Date(),
    updatedAt: Date = Date(),
    completedCycles: Int = 0,
    currentRepetitions: Int = 0,
    lastRepetitionDate: Date? = nil,
    energyLevel: Double = 0.5,
    moodRating: Int? = nil,
    notes: String? = nil,
    isFavorite: Bool = false,
    playCount: Int = 0,
    hasActiveCycle: Bool = false,
    currentCycleDay: Int = 1,
    cycleStartDate: Date? = nil,
    cycles: [RepetitionCycleDTO] = []
) -> AffirmationDTO {
    return AffirmationDTO(
        id: UUID(),
        text: text,
        category: category,
        recordingURL: recordingURL,
        createdAt: createdAt,
        updatedAt: updatedAt,
        completedCycles: completedCycles,
        currentRepetitions: currentRepetitions,
        lastRepetitionDate: lastRepetitionDate,
        energyLevel: energyLevel,
        moodRating: moodRating,
        notes: notes,
        isFavorite: isFavorite,
        playCount: playCount,
        hasActiveCycle: hasActiveCycle,
        currentCycleDay: currentCycleDay,
        cycleStartDate: cycleStartDate,
        cycles: cycles
    )
}

import SwiftUI

public struct SkeletonView: View {
    public var shape: SkeletonShape = .rectangle
    public var size: CGSize
    public var cornerRadius: CGFloat = 8

    public enum SkeletonShape {
        case rectangle, circle
    }

    public init(shape: SkeletonShape = .rectangle, size: CGSize, cornerRadius: CGFloat = 8) {
        self.shape = shape
        self.size = size
        self.cornerRadius = cornerRadius
    }

    public var body: some View {
        Group {
            switch shape {
            case .rectangle:
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(Color.gray.opacity(0.15))
                    .frame(width: size.width, height: size.height)
            case .circle:
                Circle()
                    .fill(Color.gray.opacity(0.15))
                    .frame(width: size.width, height: size.height)
            }
        }
        .redacted(reason: .placeholder)
        .accessibilityHidden(true)
    }
} 
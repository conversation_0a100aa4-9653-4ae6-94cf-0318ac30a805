import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import SwiftUI

public struct OnboardingPageView: View {
    let page: OnboardingPage
    @EnvironmentObject private var themeManager: ThemeManager

    public init(page: OnboardingPage) {
        self.page = page
    }

    public var body: some View {
        VStack(spacing: 30) {
            Image(systemName: page.imageName)
                .resizable()
                .scaledToFit()
                .frame(width: 120, height: 120)
                .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                .padding()
                .background(
                    Circle()
                        .fill(
                            themeManager.currentTheme.cardBackgroundColor.asGradient
                                ?? LinearGradient(
                                    gradient: Gradient(colors: [
                                        themeManager.currentTheme.cardBackgroundColor.asColor,
                                        themeManager.currentTheme.cardBackgroundColor.asColor,
                                    ]),
                                    startPoint: .top, endPoint: .bottom
                                )
                        )
                        .shadow(
                            color: themeManager.currentTheme.shadowColor.color.opacity(0.2),
                            radius: 10, x: 0, y: 5)
                )

            VStack(spacing: 16) {
                Text(page.title)
                    .font(.title)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                Text(page.description)
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                    .padding(.horizontal)
            }
        }
        .padding()
    }
}

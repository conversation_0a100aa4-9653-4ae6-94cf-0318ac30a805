import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import SwiftUI

/// Main view for browsing and managing the affirmation library
@available(iOS 17.0, macOS 14.0, *)
public struct LibraryView: View {
    @StateObject private var viewModel: LibraryViewModel
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var searchText = ""
    @State private var selectedCategory: AffirmationCategory?
    @State private var showSubscriptionSheet = false
    @State private var selectedAffirmation: (any AffirmationProtocol)?
    @State private var showingDeleteConfirmation = false
    @State private var selectedCategories: Set<AffirmationCategory> = []
    @State private var affirmations: [any AffirmationProtocol] = []
    @State private var showingAffirmationDetail = false

    // MARK: - Initialization

    public init(affirmationService: AffirmationServiceProtocol) {
        _viewModel = StateObject(
            wrappedValue: LibraryViewModel(affirmationService: affirmationService))
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            backgroundGradient

            VStack(spacing: 0) {
                searchAndFilterBar
                affirmationList
            }
        }
        .navigationTitle("Library")
        .navigationDestination(for: NavigationRoute.self) { route in
            switch route {
            case .affirmationDetail(let id):
                if let affirmation = viewModel.affirmations.first(where: { $0.id == id }) {
                    AffirmationDetailView(
                        viewModel: AffirmationDetailViewModel(affirmation: affirmation),
                        affirmation: affirmation
                    )
                } else {
                    EmptyView()
                }
            default:
                EmptyView()
            }
        }
        .task {
            await viewModel.loadAffirmations()
        }
        .refreshable {
            await viewModel.refreshLibrary()
        }
        .sheet(
            item: Binding(
                get: { selectedAffirmation as? Affirmation },
                set: { selectedAffirmation = $0 }
            )
        ) { affirmation in
            AffirmationDetailView(
                viewModel: AffirmationDetailViewModel(affirmation: affirmation),
                affirmation: affirmation
            )
        }
    }

    // MARK: - Helper Views

    private var backgroundGradient: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                themeManager.currentTheme.backgroundColor.asColor,
                themeManager.currentTheme.backgroundColor.asColor.opacity(0.8),
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .ignoresSafeArea()
    }

    private var searchAndFilterBar: some View {
        VStack(spacing: 16) {
            searchBar
            filterChips
        }
        .padding(.vertical)
        .background(headerBackground)
    }

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.foreground)

            TextField("Search affirmations...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .foregroundColor(themeManager.currentTheme.primaryTextColor.foreground)
                .accessibilityLabel("Search affirmations")
                .accessibilityHint("Enter text to search your affirmation library")

            if !searchText.isEmpty {
                Button(action: { searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.foreground)
                }
                .accessibilityLabel("Clear search text")
                .accessibilityHint("Clears the current search query")
            }
        }
        .padding()
        .background(searchBarBackground)
    }

    private var searchBarBackground: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.7))
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [.white.opacity(0.5), .clear]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.2),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        }
    }

    private var filterChips: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(AffirmationCategory.allCases, id: \.self) { category in
                    FilterChip(
                        title: category.rawValue,
                        isSelected: selectedCategories.contains(category),
                        action: { toggleCategory(category) }
                    )
                    .accessibilityLabel(
                        "Category: \(category.rawValue)\(selectedCategories.contains(category) ? ", selected" : "")"
                    )
                    .accessibilityHint("Double tap to filter by this category")
                }
            }
            .padding(.horizontal)
        }
    }

    private var headerBackground: some View {
        ZStack {
            Rectangle()
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.7))
                .background(
                    Rectangle()
                        .fill(.ultraThinMaterial)
                )
                .overlay(
                    Rectangle()
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [.white.opacity(0.5), .clear]),
                                startPoint: .top,
                                endPoint: .bottom
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.2),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        }
    }

    private var affirmationList: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(filteredAffirmations, id: \.id) { affirmation in
                    AffirmationCard(
                        affirmation: affirmation,
                        onTap: {
                            selectedAffirmation = affirmation
                        },
                        onFavorite: {
                            // TODO: Implement favorite functionality
                        }
                    )
                }
            }
            .padding()
        }
    }

    // MARK: - Helper Methods

    private var filteredAffirmations: [any AffirmationProtocol] {
        viewModel.filteredAffirmations.filter { affirmation in
            let textMatch =
                searchText.isEmpty
                || affirmation.text.lowercased().contains(searchText.lowercased())
            let categoryMatch =
                selectedCategories.isEmpty || selectedCategories.contains(affirmation.category)
            return textMatch && categoryMatch
        }
    }

    private func toggleCategory(_ category: AffirmationCategory) {
        if selectedCategories.contains(category) {
            selectedCategories.remove(category)
        } else {
            selectedCategories.insert(category)
        }
    }

    private func handleAffirmationSelection(_ affirmation: any AffirmationProtocol) {
        selectedAffirmation = affirmation
        showingAffirmationDetail = true
    }

    private func handleAffirmationDeletion(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationEdit(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationShare(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationFavorite(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationArchive(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationLock(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationHide(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationSync(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationComplete(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUncomplete(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnfavorite(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnarchive(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnlock(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnhide(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnsync(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }
}

// MARK: - Preview

#if DEBUG
    #if canImport(SwiftUI)
        private class PreviewMockAffirmationService: AffirmationServiceProtocol, @unchecked Sendable
        {
            func fetchAffirmations() async throws -> [any AffirmationProtocol] { [] }
            func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? { nil }
            func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
                async throws -> any AffirmationProtocol
            {
                createPreviewAffirmation(
                    text: text,
                    category: category,
                    recordingURL: recordingURL,
                    currentRepetitions: 0,
                    energyLevel: 0.5,
                    isFavorite: false
                )
            }
            func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {}
            func deleteAffirmation(id: UUID) async throws {}
            func fetchAffirmations(category: AffirmationCategory) async throws
                -> [any AffirmationProtocol]
            { [] }
            func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] { [] }
            func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? { nil }
            func toggleFavorite(_ affirmation: any AffirmationProtocol) async throws
                -> any AffirmationProtocol
            { affirmation }
            func startCycle(for affirmation: any AffirmationProtocol) async throws {}
            func recordRepetition(for affirmation: any AffirmationProtocol) async throws
                -> any AffirmationProtocol
            { affirmation }
            func updateEnergyLevel(_ level: Double, for affirmation: any AffirmationProtocol)
                async throws -> any AffirmationProtocol
            { affirmation }
            func recordMood(_ rating: Int, notes: String?, for affirmation: any AffirmationProtocol)
                async throws -> any AffirmationProtocol
            { affirmation }
            func getStatistics() async throws -> AffirmationStatistics {
                var statistics = AffirmationStatistics()
                statistics.totalAffirmations = 0
                statistics.activeAffirmations = 0
                statistics.favoriteAffirmations = 0
                statistics.totalCompletedCycles = 0
                statistics.totalRepetitions = 0
                statistics.categoryDistribution = [:]
                statistics.activeStreaks = 0
                statistics.completedCycles = 0
                statistics.longestCurrentStreak = 0
                return statistics
            }
        }
        struct LibraryView_Previews: PreviewProvider {
            static var previews: some View {
                let viewModel = LibraryViewModel(
                    affirmationService: PreviewMockAffirmationService())
                let affirmation = createPreviewAffirmation(
                    text: "I am confident and capable",
                    category: .confidence,
                    recordingURL: nil,
                    currentRepetitions: 5,
                    energyLevel: 0.5,
                    isFavorite: true
                )

                return NavigationStack {
                    LibraryView(affirmationService: PreviewMockAffirmationService())
                        .environmentObject(NavigationCoordinator())
                        .environmentObject(ThemeManager.shared)
                }
            }
        }
    #endif
#endif

// Add a local error type for display
private enum LibraryError: Error, LocalizedError {
    case display(String)
    var errorDescription: String? {
        switch self {
        case .display(let message): return message
        }
    }
}

// MARK: - Library Skeleton View

private struct LibrarySkeletonView: View {
    var body: some View {
        VStack(spacing: 24) {
            // Search/filter skeleton
            SkeletonView(size: CGSize(width: 280, height: 32), cornerRadius: 8)
            // Affirmation row skeletons
            ForEach(0..<5) { _ in
                SkeletonView(size: CGSize(width: 320, height: 56), cornerRadius: 12)
            }
        }
        .padding()
        .accessibilityHidden(true)
    }
}

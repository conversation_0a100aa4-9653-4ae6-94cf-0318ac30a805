import NeuroLoopCore
import SwiftUI

public struct AchievementSharingView: View {
    @ObservedObject var viewModel: AchievementSharingViewModel
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject private var themeManager: ThemeManager

    public init(viewModel: AchievementSharingViewModel) {
        self.viewModel = viewModel
    }

    public var body: some View {
        NavigationView {
            mainContent
        }
    }

    private var mainContent: some View {
        VStack(spacing: 24) {
            // Preview of shareable content
            ShareableAchievementCardView(
                achievement: viewModel.achievement, theme: viewModel.selectedTheme,
                quote: viewModel.selectedQuote
            )
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.foreground.opacity(0.2),
                        radius: 8, x: 0, y: 4)
            )
            .accessibilityElement(children: .combine)

            // Privacy controls
            PrivacyControlsView(
                includeName: $viewModel.includeName,
                includeMetrics: $viewModel.includeMetrics,
                includeDownloadLink: $viewModel.includeDownloadLink
            )
            .padding(.horizontal)

            // Customization options
            CustomizationOptionsView(
                selectedTheme: $viewModel.selectedTheme,
                selectedQuote: $viewModel.selectedQuote,
                availableThemes: viewModel.availableThemes,
                availableQuotes: viewModel.availableQuotes
            )
            .padding(.horizontal)

            // Share button
            Button(action: viewModel.shareAchievement) {
                Label("Share Achievement", systemImage: "square.and.arrow.up")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(RoundedRectangle(cornerRadius: 12).fill(Color.blue))
                    .foregroundColor(.white)
            }
            .padding(.horizontal)
            .accessibilityLabel("Share your achievement")

            Spacer()
        }
        .navigationTitle("Share Achievement")
        .toolbar {
            ToolbarItem(placement: .cancellationAction) {
                Button("Cancel") { presentationMode.wrappedValue.dismiss() }
            }
        }
    }
}

// Placeholder for the shareable card
struct ShareableAchievementCardView: View {
    let achievement: String
    let theme: String
    let quote: String
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        VStack(spacing: 12) {
            Text("🏆")
                .font(.system(size: 48))
            Text(achievement)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.foreground)
            Text(quote)
                .font(.body)
                .italic()
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.foreground)
                .padding(.top, 4)
            Text("#NeuroLoopApp")
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.foreground)
            if !theme.isEmpty {
                Text("Theme: \(theme)")
                    .font(.caption2)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.foreground)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                            ]),
                            startPoint: .top, endPoint: .bottom
                        )
                )
        )
    }
}

// Placeholder for privacy controls
struct PrivacyControlsView: View {
    @Binding var includeName: Bool
    @Binding var includeMetrics: Bool
    @Binding var includeDownloadLink: Bool
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Toggle("Include my name", isOn: $includeName)
            Toggle("Include achievement metrics", isOn: $includeMetrics)
            Toggle("Include download link", isOn: $includeDownloadLink)
        }
    }
}

// Placeholder for customization options
struct CustomizationOptionsView: View {
    @Binding var selectedTheme: String
    @Binding var selectedQuote: String
    let availableThemes: [String]
    let availableQuotes: [String]
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Theme")
            Picker("Theme", selection: $selectedTheme) {
                ForEach(availableThemes, id: \.self) { theme in
                    Text(theme)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            Text("Quote")
            Picker("Quote", selection: $selectedQuote) {
                ForEach(availableQuotes, id: \.self) { quote in
                    Text(quote)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
}

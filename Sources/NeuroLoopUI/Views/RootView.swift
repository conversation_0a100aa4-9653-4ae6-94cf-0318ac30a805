import Combine
import NeuroLoopCore
import NeuroLoopInterfaces
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import SwiftUI

/// The root view of the app containing the main TabView
public struct RootView: View {
    // MARK: - Properties

    @StateObject private var navigationCoordinator = NavigationCoordinator()
    @State private var selectedTab: RootTab = .home
    @AppStorage("hasCompletedOnboarding") private var hasCompletedOnboarding: Bool = false
    @State private var showOnboarding: Bool = true
    @State private var selectedAffirmation: (any AffirmationProtocol)?
    @State private var affirmations: [any AffirmationProtocol] = []
    @State private var showingAffirmationDetail = false

    // MARK: - Initializer

    /// Public initializer to allow instantiation from other modules
    public init() {}

    // MARK: - Body

    public var body: some View {
        ZStack {
            // App-wide background gradient
            BackgroundGradientView()
                .ignoresSafeArea()

            TabView(selection: $selectedTab) {
                HomeTabView()
                    .tabItem {
                        Label(RootTab.home.title, systemImage: RootTab.home.icon)
                            .accessibilityLabel("Home Tab")
                            .accessibilityHint(
                                "Shows your daily affirmations and progress overview")
                    }
                    .tag(RootTab.home)

                LibraryTabView()
                    .tabItem {
                        Label(RootTab.library.title, systemImage: RootTab.library.icon)
                            .accessibilityLabel("Library Tab")
                            .accessibilityHint("Browse and manage your saved affirmations")
                    }
                    .tag(RootTab.library)

                SettingsTabView()
                    .tabItem {
                        Label(RootTab.settings.title, systemImage: RootTab.settings.icon)
                            .accessibilityLabel("Settings Tab")
                            .accessibilityHint("Access app settings and premium features")
                    }
                    .tag(RootTab.settings)
            }
            .environmentObject(navigationCoordinator)

            if showOnboarding {
                OnboardingView(isPresented: $showOnboarding)
                    .transition(.opacity)
                    .accessibilityAddTraits(.isModal)
                    .accessibilityLabel("Onboarding")
                    .accessibilityHint("Guides you through the app's main features")
                    .onDisappear {
                        hasCompletedOnboarding = true
                    }
            }
        }
        .onAppear {
            showOnboarding = !hasCompletedOnboarding
        }
    }

    // MARK: - Background Gradient View

    private struct BackgroundGradientView: View {
        @EnvironmentObject private var themeManager: ThemeManager

        var body: some View {
            // Use a custom blue gradient directly
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.1, green: 0.4, blue: 0.8),
                    Color(red: 0.0, green: 0.2, blue: 0.6),
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
        }
    }

    private func handleAffirmationSelection(_ affirmation: any AffirmationProtocol) {
        selectedAffirmation = affirmation
        showingAffirmationDetail = true
    }

    private func handleAffirmationDeletion(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationEdit(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationShare(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationFavorite(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationArchive(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationLock(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationHide(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationSync(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationComplete(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUncomplete(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnfavorite(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnarchive(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnlock(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnhide(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }

    private func handleAffirmationUnsync(_ affirmation: any AffirmationProtocol) {
        // Implementation
    }
}

// MARK: - Tab Views

/// The home tab view with its navigation stack
@available(iOS 17.0, macOS 14.0, *)
struct HomeTabView: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @State private var selectedAffirmationId: UUID?
    @StateObject private var viewModel = HomeViewModel(
        repository: MockAffirmationRepository(),
        streakService: StreakService(repository: MockAffirmationRepository())
    )

    var body: some View {
        NavigationStack(path: $navigationCoordinator.homePath) {
            HomeView(repository: MockAffirmationRepository(), viewModel: viewModel)
                .onReceive(NotificationCenter.default.publisher(for: .navigateToSpeakAffirmation)) {
                    notification in
                    if let userInfo = notification.userInfo,
                        let affirmationId = userInfo["affirmationId"] as? UUID
                    {
                        selectedAffirmationId = affirmationId
                        navigationCoordinator.navigate(
                            to: .speakAffirmation(affirmationId), in: .home)
                    }
                }
                .navigationDestination(for: NavigationRoute.self) { route in
                    switch route {
                    case .speakAffirmation:
                        if #available(iOS 17.0, macOS 14.0, *) {
                            // Create a mock affirmation for the ID
                            let mockAffirmation = createMockAffirmation()

                            // Use the original SpeakAffirmationView
                            SpeakAffirmationView(
                                affirmation: mockAffirmation,
                                repetitionService: RootViewMockRepetitionService(),
                                affirmationService: MockAffirmationService(),
                                streakService: MockStreakService()
                            )
                        } else {
                            Text("This feature requires iOS 17.0 or later")
                        }
                    default:
                        Text("Route not implemented yet")
                    }
                }
        }
    }

    // Helper function to create a mock affirmation
    private func createMockAffirmation() -> any AffirmationProtocol {
        // Create a simple mock affirmation without async calls
        return MockAffirmationObject()
    }

    // Simple mock implementation of AffirmationProtocol for UI testing
    private class MockAffirmationObject: AffirmationProtocol, @unchecked Sendable {
        let id = UUID()
        let text = "I am confident and capable in everything I do"
        let category: AffirmationCategory = .confidence
        let recordingURL: URL? = nil
        let createdAt = Date()
        let updatedAt = Date()
        let currentCycleDay = 1
        let cycleStartDate: Date? = Date()
        let completedCycles = 0
        let currentRepetitions = 0
        let dailyProgress: [Date: Int] = [:]
        let lastRepetitionDate: Date? = nil
        let energyLevel = 0.5
        let moodRating: Int? = nil
        let notes: String? = nil
        let isFavorite = false
        let playCount = 0
        let hasActiveCycle = true
        let isCurrentCycleComplete = false
        let todayProgress = 0.0
        let cycleProgress = 0.0
        let hasTodayQuotaMet = false
        let hasRecording = false
        let canPerformRepetition = true

        // Add longestStreak property for AffirmationProtocol conformance
        var longestStreak: Int = 0

        // Required methods from AffirmationProtocol
        func recordRepetition() throws {
            // Mock implementation - does nothing
        }

        func updateEnergyLevel(_ level: Double) {
            // Mock implementation - does nothing
        }

        func recordMood(_ rating: Int, notes: String?) {
            // Mock implementation - does nothing
        }

        static func == (lhs: MockAffirmationObject, rhs: MockAffirmationObject) -> Bool {
            return lhs.id == rhs.id
        }
    }
}

/// The library tab view with its navigation stack
@available(iOS 17.0, macOS 14.0, *)
struct LibraryTabView: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator

    var body: some View {
        NavigationStack(path: $navigationCoordinator.libraryPath) {
            LibraryView(affirmationService: MockAffirmationService())
        }
    }
}

/// The settings tab view with its navigation stack
@available(iOS 17.0, macOS 14.0, *)
struct SettingsTabView: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator

    var body: some View {
        NavigationStack(path: $navigationCoordinator.settingsPath) {
            let mockViewModel = SettingsViewModel(
                userDefaults: .standard,
                purchaseManager: MockPurchaseManager(),
                dataExportService: MockDataExportService(),
                themeManager: ThemeManager.shared,
                hapticManager: MockHapticManager(),
                syncService: MockSyncService()
            )
            SettingsView(viewModel: mockViewModel)
        }
    }
}

// MARK: - Tab Enum

public enum RootTab: String {
    case home, library, settings

    public var title: String {
        switch self {
        case .home: return "Home"
        case .library: return "Library"
        case .settings: return "Settings"
        }
    }

    public var icon: String {
        switch self {
        case .home: return "house.fill"
        case .library: return "book.fill"
        case .settings: return "gear"
        }
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct RootView_Previews: PreviewProvider {
        static var previews: some View {
            RootView()
        }
    }
#endif

// MARK: - Mock Services

public class MockAffirmationRepository: AffirmationRepositoryProtocol, @unchecked Sendable {
    public init() {}

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        return []
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        return nil
    }

    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
        async throws -> any AffirmationProtocol
    {
        // Use the public AffirmationStub from Mocks folder
        return await AffirmationStub(text: text, category: category, recordingURL: recordingURL)
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
    }

    public func deleteAffirmation(id: UUID) async throws {
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        return []
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        return []
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        return nil
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws {
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
    }
}

public class MockAffirmationService: AffirmationServiceProtocol, @unchecked Sendable {
    public init() {}

    public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        return []
    }

    public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        return nil
    }

    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?)
        async throws -> any AffirmationProtocol
    {
        // Use the public AffirmationStub from Mocks folder
        return await AffirmationStub(text: text, category: category, recordingURL: recordingURL)
    }

    public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
    }

    public func deleteAffirmation(id: UUID) async throws {
    }

    public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        return []
    }

    public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        return []
    }

    public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        return nil
    }

    public func toggleFavorite(_ affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        return affirmation
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws {
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        return affirmation
    }

    public func updateEnergyLevel(_ level: Double, for affirmation: any AffirmationProtocol)
        async throws
        -> any AffirmationProtocol
    {
        return affirmation
    }

    public func recordMood(_ rating: Int, notes: String?, for affirmation: any AffirmationProtocol)
        async throws -> any AffirmationProtocol
    {
        return affirmation
    }

    public func getStatistics() async throws -> AffirmationStatistics {
        var statistics = AffirmationStatistics()
        statistics.totalAffirmations = 0
        statistics.activeAffirmations = 0
        statistics.favoriteAffirmations = 0
        statistics.totalCompletedCycles = 0
        statistics.totalRepetitions = 0
        statistics.categoryDistribution = [:]
        statistics.activeStreaks = 0
        statistics.completedCycles = 0
        statistics.longestCurrentStreak = 0
        return statistics
    }
}

public class MockPurchaseManager: PurchaseManagerSendable, @unchecked Sendable {
    public init() {}

    public func purchasePremium() async throws {}

    public func restorePurchases() async throws {}
}

public class MockDataExportService: DataExportServiceProtocol, @unchecked Sendable {
    public init() {}

    public func exportData() async throws -> URL {
        return URL(fileURLWithPath: NSTemporaryDirectory())
    }

    public func deleteAllData() async throws {
    }
}

// Add mock haptic and sync services for UI-only builds
public class MockHapticManager: HapticGenerating, @unchecked Sendable {
    public init() {}

    @MainActor
    public func lightImpact() async {}
    @MainActor
    public func mediumImpact() async {}
    @MainActor
    public func heavyImpact() async {}
    @MainActor
    public func playSuccess() async {}
    @MainActor
    public func playError() async {}
    @MainActor
    public func playWarning() async {}
    @MainActor
    public func playSelection() async {}
    @MainActor
    public func playAffirmationCompletionPattern() async {}
    @MainActor
    public func playCelebrationPattern() async {}
    @MainActor
    public func playStreakCompletionPattern() async {}
    @MainActor
    public func playImpact(style: ImpactStyle) async {}
}

public class MockSyncService: SyncServiceProtocol, @unchecked Sendable {
    public init() {}

    public var syncStatus: SyncStatus { .idle }
    public var syncStatusPublisher: AnyPublisher<SyncStatus, Never> {
        Just(.idle).eraseToAnyPublisher()
    }
    public var lastSyncError: Error? { nil }
    public var lastSyncDate: Date? { nil }
    public var isAutomaticSyncEnabled: Bool { true }
    public func sync() async throws {}
    public func syncNow() async throws {}
    public func setAutomaticSyncEnabled(_ enabled: Bool) async {}
    public func configureBackgroundSyncIfNeeded(isPremium: Bool) {}
    public func deleteAffirmationWithTombstone(id: UUID) async throws {}
}

public class RootViewMockRepetitionService: RepetitionServiceProtocol, @unchecked Sendable {
    public init() {}

    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> RepetitionResult
    {
        return RepetitionResult(
            success: true,
            updatedAffirmation: affirmation,
            isQuotaMet: false,
            isCycleComplete: false
        )
    }

    public func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        return ProgressInfo(
            todayProgress: 0.5,
            cycleProgress: 0.5,
            currentDay: 1,
            totalDays: 7,
            currentRepetitions: 5,
            totalRepetitions: 10,
            hasTodayQuotaMet: false,
            isCycleComplete: false,
            hasActiveCycle: true
        )
    }

    public func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        return StreakInfo(
            currentStreak: 1,
            longestStreak: 3,
            completedCycles: 1,
            hasActiveCycle: true,
            cycleStartDate: Date(),
            lastRepetitionDate: Date()
        )
    }

    public func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        return true
    }

    public func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval? {
        return nil
    }

    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws
        -> CycleResult
    {
        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }

    public func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        return false
    }

    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }

    public func validateStreaks() async throws -> StreakValidationResult {
        return StreakValidationResult(
            success: true,
            error: nil,
            validatedAffirmations: 1,
            brokenStreaks: 0
        )
    }
}

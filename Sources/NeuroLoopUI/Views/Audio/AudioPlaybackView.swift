import SwiftUI

public struct AudioPlaybackView: View {
    private let isPlaying: Bool
    private let progress: Double
    private let onPlay: () -> Void
    private let onPause: () -> Void
    
    public init(
        isPlaying: Bool,
        progress: Double,
        onPlay: @escaping () -> Void,
        onPause: @escaping () -> Void
    ) {
        self.isPlaying = isPlaying
        self.progress = progress
        self.onPlay = onPlay
        self.onPause = onPause
    }
    
    public var body: some View {
        VStack(spacing: 10) {
            // Waveform visualization
            AudioWaveform(
                samples: Array(repeating: Float.random(in: 0...1), count: 50),
                color: .blue,
                backgroundColor: Color.gray.opacity(0.2),
                lineWidth: 2
            )
            .frame(height: 50)
            
            // Playback controls
            HStack {
                Button(action: {
                    if isPlaying {
                        onPause()
                    } else {
                        onPlay()
                    }
                }) {
                    Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.blue)
                }
                
                // Progress bar
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 8)
                            .cornerRadius(4)
                        
                        Rectangle()
                            .fill(Color.blue)
                            .frame(width: geometry.size.width * progress, height: 8)
                            .cornerRadius(4)
                    }
                }
                .frame(height: 8)
            }
        }
    }
}

// MARK: - Preview

struct AudioPlaybackView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            AudioPlaybackView(
                isPlaying: false,
                progress: 0.3,
                onPlay: {},
                onPause: {}
            )
            
            AudioPlaybackView(
                isPlaying: true,
                progress: 0.7,
                onPlay: {},
                onPause: {}
            )
        }
        .padding()
    }
} 
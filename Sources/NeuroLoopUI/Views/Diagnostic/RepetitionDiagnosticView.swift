import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes
import SwiftUI

/**
 * RepetitionDiagnosticView
 *
 * A diagnostic view for troubleshooting repetition counter issues.
 * This view provides a simple interface to test the repetition counter
 * with Debug Mode enabled.
 */

@available(iOS 17.0, macOS 14.0, *)
public struct RepetitionDiagnosticView: View {
    @EnvironmentObject private var themeManager: ThemeManager

    @StateObject private var viewModel: SpeakAffirmationViewModel
    @State private var showInstructions = true

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: RepetitionServiceProtocol,
        audioService: AudioRecordingServiceProtocol,
        affirmationService: AffirmationServiceProtocol,
        streakService: StreakServiceProtocol
    ) {
        self._viewModel = StateObject(
            wrappedValue: SpeakAffirmationViewModel(
                affirmation: affirmation,
                repetitionService: repetitionService,
                audioService: audioService,
                affirmationService: affirmationService,
                streakService: streakService
            ))
    }

    public var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("Repetition Counter Diagnostic")
                    .font(.largeTitle)
                    .bold()

                if showInstructions {
                    instructionsView
                }

                // Affirmation card
                affirmationCard

                // Repetition progress
                repetitionProgress

                // Debug mode toggle
                Toggle("Debug Mode (Always Count)", isOn: $viewModel.debugBypassSpeechRecognition)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)

                // Recording button
                recordingButton

                // Audio level visualization
                if viewModel.isRecording {
                    audioLevelVisualization
                }

                // Reset button
                Button("Reset Counter") {
                    Task {
                        await viewModel.resetRepetitionCount()
                    }
                }
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(8)
            }
            .padding()
            .alert(item: $viewModel.alertItem) { (alertItem: AlertItem) in
                Alert(
                    title: Text(alertItem.title),
                    message: Text(alertItem.message),
                    dismissButton: .default(Text("OK"))
                )
            }
            .onAppear {
                Task {
                    await viewModel.initialize()
                    // Enable debug mode by default
                    viewModel.debugBypassSpeechRecognition = true
                }
            }
        }
    }

    private var instructionsView: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Text("Instructions")
                    .font(.headline)

                Spacer()

                Button(action: {
                    showInstructions.toggle()
                }) {
                    Image(systemName: "xmark.circle")
                        .foregroundColor(.gray)
                }
            }

            Text("This diagnostic tool helps troubleshoot issues with the repetition counter.")
                .font(.subheadline)

            Text("1. Debug Mode is enabled by default, which bypasses speech recognition.")
                .font(.caption)

            Text(
                "2. Tap the record button, then tap it again to stop. The counter should increment."
            )
            .font(.caption)

            Text(
                "3. If the counter doesn't increment, there's an issue with the repetition service."
            )
            .font(.caption)
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(8)
    }

    private var affirmationCard: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.blue)
                .shadow(radius: 5)

            VStack {
                Text("\"\(viewModel.affirmation.text)\"")
                    .font(.title2)
                    .foregroundColor(.white)
                    .padding()
            }
            .padding()
        }
        .frame(height: 150)
    }

    private var repetitionProgress: some View {
        ZStack {
            Circle()
                .stroke(Color.blue.opacity(0.3), lineWidth: 10)
                .frame(width: 100, height: 100)

            Circle()
                .trim(
                    from: 0,
                    to: CGFloat(viewModel.todayRepetitions) / CGFloat(viewModel.totalRepetitions)
                )
                .stroke(Color.blue, lineWidth: 10)
                .frame(width: 100, height: 100)
                .rotationEffect(.degrees(-90))

            VStack {
                Text("\(viewModel.todayRepetitions)")
                    .font(.title)
                    .bold()

                Text("of \(viewModel.totalRepetitions)")
                    .font(.caption)
            }
        }
        .frame(height: 120)
    }

    private var recordingButton: some View {
        Button(action: {
            Task {
                if viewModel.isRecording {
                    await viewModel.stopRecording()
                } else {
                    do {
                        try await viewModel.startRecording()
                    } catch {
                        print("Error starting recording: \(error)")
                        viewModel.alertItem = AlertItem(
                            title: "Recording Error", message: error.localizedDescription)
                    }
                }
            }
        }) {
            ZStack {
                Circle()
                    .fill(viewModel.isRecording ? Color.red : Color.white)
                    .frame(width: 70, height: 70)
                    .shadow(radius: 5)

                if viewModel.isRecording {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.white)
                        .frame(width: 20, height: 20)
                } else {
                    Circle()
                        .fill(Color.blue)
                        .frame(width: 60, height: 60)
                }
            }
        }
        .padding()
    }

    private var audioLevelVisualization: some View {
        VStack {
            Text("Audio Level: \(Int(viewModel.currentAudioLevel * 100))%")

            // Audio level bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 20)

                    Rectangle()
                        .fill(Color.blue)
                        .frame(
                            width: geometry.size.width * CGFloat(viewModel.currentAudioLevel),
                            height: 20)
                }
                .cornerRadius(10)
            }
            .frame(height: 20)
        }
    }
}

// Preview
@available(iOS 17.0, macOS 14.0, *)
struct RepetitionDiagnosticView_Previews: PreviewProvider {
    static var previews: some View {
        let stub = AffirmationStub(
            text: "I am confident and capable in everything I do",
            category: .health
        )

        return RepetitionDiagnosticView(
            affirmation: stub,
            repetitionService: FullMockRepetitionService(),
            audioService: MockAudioService(),
            affirmationService: MockAffirmationService(),
            streakService: MockStreakService()
        )
        .environmentObject(ThemeManager.shared)
    }
}

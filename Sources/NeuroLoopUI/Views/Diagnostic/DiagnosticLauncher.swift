import SwiftUI
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import NeuroLoopModels

/**
 * DiagnosticLauncher
 *
 * A simple view that provides access to various diagnostic tools.
 * This view can be added to any part of your app to provide easy access to diagnostics.
 */

@available(iOS 17.0, macOS 14.0, *)
public struct DiagnosticLauncher: View {
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var showDiagnosticSheet = false
    @State private var selectedDiagnostic: DiagnosticTool = .repetitionCounter

    private let serviceFactory: ServiceFactory

    public init(serviceFactory: ServiceFactory) {
        self.serviceFactory = serviceFactory
    }

    public var body: some View {
        Button(action: {
            showDiagnosticSheet = true
        }) {
            HStack {
                Image(systemName: "stethoscope")
                Text("Diagnostics")
            }
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .sheet(isPresented: $showDiagnosticSheet) {
            NavigationView {
                VStack {
                    Picker("Diagnostic Tool", selection: $selectedDiagnostic) {
                        ForEach(DiagnosticTool.allCases, id: \.self) { tool in
                            Text(tool.displayName).tag(tool)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .padding()

                    selectedDiagnosticView
                }
                .navigationTitle("Diagnostic Tools")
                .toolbar {
                    #if os(iOS)
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Close") {
                            showDiagnosticSheet = false
                        }
                    }
                    #else
                    ToolbarItem(placement: .automatic) {
                        Button("Close") {
                            showDiagnosticSheet = false
                        }
                    }
                    #endif
                }
            }
        }
    }

    @ViewBuilder
    private var selectedDiagnosticView: some View {
        switch selectedDiagnostic {
        case .repetitionCounter:
            repetitionCounterDiagnostic
        case .audioRecording:
            audioRecordingDiagnostic
        case .speechRecognition:
            speechRecognitionDiagnostic
        }
    }

    private var repetitionCounterDiagnostic: some View {
        let stub = AffirmationStub(
            text: "I am confident and capable in everything I do",
            category: .health
        )

        return RepetitionDiagnosticView(
            affirmation: stub,
            repetitionService: FullMockRepetitionService(),
            audioService: MockAudioService(),
            affirmationService: MockAffirmationService(),
            streakService: MockStreakService()
        )
    }

    private var audioRecordingDiagnostic: some View {
        VStack {
            Text("Audio Recording Diagnostic")
                .font(.title)

            Text("Coming soon...")
                .padding()
        }
    }

    private var speechRecognitionDiagnostic: some View {
        VStack {
            Text("Speech Recognition Diagnostic")
                .font(.title)

            Text("Coming soon...")
                .padding()
        }
    }
}

@available(iOS 17.0, macOS 14.0, *)
enum DiagnosticTool: String, CaseIterable {
    case repetitionCounter
    case audioRecording
    case speechRecognition

    var displayName: String {
        switch self {
        case .repetitionCounter:
            return "Repetition Counter"
        case .audioRecording:
            return "Audio Recording"
        case .speechRecognition:
            return "Speech Recognition"
        }
    }
}

// Preview
@available(iOS 17.0, macOS 14.0, *)
struct DiagnosticLauncher_Previews: PreviewProvider {
    static var previews: some View {
        DiagnosticLauncher(serviceFactory: ServiceFactory.shared)
            .environmentObject(ThemeManager.shared)
    }
}

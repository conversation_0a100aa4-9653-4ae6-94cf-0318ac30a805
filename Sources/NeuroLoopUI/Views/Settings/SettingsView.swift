import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import Swift<PERSON>

@available(iOS 17.0, macOS 14.0, *)
public struct SettingsView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel: SettingsViewModel

    public init(viewModel: SettingsViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }

    public var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Theme section
                ThemeSectionView()

                // Notifications section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Notifications")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                    Toggle("Daily Reminders", isOn: $viewModel.dailyRemindersEnabled)
                        .tint(themeManager.currentTheme.accentColor.asColor)

                    if viewModel.dailyRemindersEnabled {
                        DatePicker(
                            "Reminder Time",
                            selection: $viewModel.reminderTime,
                            displayedComponents: .hourAndMinute
                        )
                        .tint(themeManager.currentTheme.accentColor.asColor)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            themeManager.currentTheme.cardBackgroundColor.asGradient
                                ?? themeManager.currentTheme.cardBackgroundColor.asColor
                                .asFallbackGradient()
                        )
                        .shadow(
                            color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                            radius: 10,
                            x: 0,
                            y: 5
                        )
                )

                // Data & Privacy section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Data & Privacy")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                    Button(action: { viewModel.exportData() }) {
                        HStack {
                            Image(systemName: "square.and.arrow.up")
                            Text("Export Data")
                        }
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }

                    Button(action: { viewModel.clearData() }) {
                        HStack {
                            Image(systemName: "trash")
                            Text("Clear All Data")
                        }
                        .foregroundColor(.red)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            themeManager.currentTheme.cardBackgroundColor.asGradient
                                ?? themeManager.currentTheme.cardBackgroundColor.asColor
                                .asFallbackGradient()
                        )
                        .shadow(
                            color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                            radius: 10,
                            x: 0,
                            y: 5
                        )
                )

                // About section
                VStack(alignment: .leading, spacing: 16) {
                    Text("About")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                    HStack {
                        Text("Version")
                            .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                        Spacer()
                        Text(viewModel.appVersion)
                            .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
                    }

                    Button(action: { viewModel.openPrivacyPolicy() }) {
                        HStack {
                            Image(systemName: "hand.raised")
                            Text("Privacy Policy")
                        }
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }

                    Button(action: { viewModel.openTermsOfService() }) {
                        HStack {
                            Image(systemName: "doc.text")
                            Text("Terms of Service")
                        }
                        .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            themeManager.currentTheme.cardBackgroundColor.asGradient
                                ?? themeManager.currentTheme.cardBackgroundColor.asColor
                                .asFallbackGradient()
                        )
                        .shadow(
                            color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                            radius: 10,
                            x: 0,
                            y: 5
                        )
                )
            }
            .padding()
        }
        .navigationTitle("Settings")
        #if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
        #endif
        .background(
            Group {
                if let gradient = themeManager.currentTheme.backgroundColor.asGradient {
                    gradient
                } else {
                    themeManager.currentTheme.backgroundColor.asColor
                }
            }
            .ignoresSafeArea()
        )
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct SettingsView_Previews: PreviewProvider {
        static var previews: some View {
            NavigationStack {
                SettingsView(viewModel: SettingsViewModel(
                    userDefaults: UserDefaults.standard,
                    purchaseManager: PreviewMockPurchaseManager(),
                    dataExportService: PreviewMockDataExportService(),
                    themeManager: ThemeManager.shared,
                    hapticManager: HapticManager.shared,
                    syncService: nil
                ))
                .environmentObject(NeuroLoopCore.ThemeManager.shared)
            }
        }
    }
#endif

import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import SwiftUI

public struct NotificationsSectionView: View {
    @ObservedObject var viewModel: SettingsViewModel
    @EnvironmentObject private var themeManager: ThemeManager

    public init(viewModel: SettingsViewModel) {
        self.viewModel = viewModel
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Notifications")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            Toggle("Daily Reminders", isOn: $viewModel.notificationsEnabled)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            if viewModel.notificationsEnabled {
                DatePicker(
                    "Reminder Time", selection: $viewModel.reminderTime,
                    displayedComponents: .hourAndMinute
                )
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
            }
        }
        .glassmorphicCard()
    }
}

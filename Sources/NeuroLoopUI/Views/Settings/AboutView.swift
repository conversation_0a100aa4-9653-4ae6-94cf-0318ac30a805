import NeuroLoopCore
import <PERSON>euro<PERSON>oopTypes
import SwiftUI

public struct AboutView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    public init() {}

    public var body: some View {
        List {
            Section {
                VStack(spacing: 16) {
                    Image("AppIcon")
                        .resizable()
                        .frame(width: 100, height: 100)
                        .cornerRadius(20)

                    Text("NeuroLoop")
                        .font(.title)
                        .bold()

                    Text("Version 1.0.0")
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical)
            }

            Section {
                Link(destination: URL(string: "https://neuroloop.app/privacy")!) {
                    SettingsRow(
                        title: "Privacy Policy",
                        icon: "lock.fill",
                        color: themeManager.currentTheme.accentColor.asColor
                    )
                }

                Link(destination: URL(string: "https://neuroloop.app/terms")!) {
                    SettingsRow(
                        title: "Terms of Service",
                        icon: "doc.text.fill",
                        color: themeManager.currentTheme.accentColor.asColor
                    )
                }
            }

            Section {
                Link(destination: URL(string: "https://neuroloop.app/support")!) {
                    SettingsRow(
                        title: "Contact Support",
                        icon: "envelope.fill",
                        color: themeManager.currentTheme.accentColor.asColor
                    )
                }

                Link(destination: URL(string: "https://neuroloop.app/feedback")!) {
                    SettingsRow(
                        title: "Send Feedback",
                        icon: "message.fill",
                        color: themeManager.currentTheme.accentColor.asColor
                    )
                }
            }
        }
        .navigationTitle("About")
    }
}

private struct SettingsRow: View {
    let title: String
    let icon: String
    let color: Color

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 24, height: 24)

            Text(title)
                .foregroundColor(.primary)

            Spacer()

            Image(systemName: "arrow.up.right")
                .foregroundColor(.secondary)
        }
    }
}

#if DEBUG
    struct AboutView_Previews: PreviewProvider {
        static var previews: some View {
            NavigationView {
                AboutView()
                    .environmentObject(NeuroLoopCore.ThemeManager.shared)
            }
        }
    }
#endif

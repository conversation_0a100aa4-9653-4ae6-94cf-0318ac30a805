import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import SwiftUI

public struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    public init() {}

    public var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Privacy Policy")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                    Group {
                        Text("Data Collection")
                            .font(.headline)
                        Text(
                            "NeuroLoop collects and stores your meditation data locally on your device. This includes your meditation sessions, preferences, and settings."
                        )

                        Text("Data Usage")
                            .font(.headline)
                        Text(
                            "Your data is used solely to provide you with a personalized meditation experience. We do not share your data with third parties."
                        )

                        Text("Data Security")
                            .font(.headline)
                        Text(
                            "We implement industry-standard security measures to protect your data. All data is encrypted and stored securely on your device."
                        )
                    }
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                }
                .padding()
            }
            .toolbar {
                #if os(iOS)
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            dismiss()
                        }
                    }
                #else
                    ToolbarItem {
                        Button("Done") {
                            dismiss()
                        }
                    }
                #endif
            }
        }
    }
}

private struct BulletPoint: View {
    let text: String

    init(_ text: String) {
        self.text = text
    }

    var body: some View {
        HStack(alignment: .top) {
            Text("•")
                .padding(.trailing, 4)
            Text(text)
        }
    }
}

#if DEBUG
    struct PrivacyPolicyView_Previews: PreviewProvider {
        static var previews: some View {
            NavigationView {
                PrivacyPolicyView()
                    .environmentObject(NeuroLoopCore.ThemeManager.shared)
            }
        }
    }
#endif

import NeuroLoopCore
import Neuro<PERSON>oopTypes
import SwiftUI

public struct NotificationSettingsView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel = NotificationViewModel()

    public init() {}

    public var body: some View {
        List {
            Section {
                Toggle("Daily Reminders", isOn: $viewModel.dailyReminderEnabled)
                    .tint(themeManager.currentTheme.accentColor.asColor)

                if viewModel.dailyReminderEnabled {
                    DatePicker(
                        "Reminder Time",
                        selection: $viewModel.preferredTime,
                        displayedComponents: .hourAndMinute
                    )
                }
            } header: {
                Text("Daily Reminders")
            } footer: {
                Text("Receive daily reminders to practice your affirmations.")
            }

            Section {
                Toggle(
                    "Achievement Notifications", isOn: $viewModel.milestoneNotificationsEnabled
                )
                .tint(themeManager.currentTheme.accentColor.asColor)
            } header: {
                Text("Achievements")
            } footer: {
                Text("Get notified when you reach new milestones and achievements.")
            }
        }
        .navigationTitle("Notifications")
    }
}

#if DEBUG
    struct NotificationSettingsView_Previews: PreviewProvider {
        static var previews: some View {
            NavigationView {
                NotificationSettingsView()
                    .environmentObject(NeuroLoopCore.ThemeManager.shared)
            }
        }
    }
#endif

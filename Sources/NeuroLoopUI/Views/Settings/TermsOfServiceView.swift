import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import SwiftUI

public struct TermsOfServiceView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    public init() {}

    public var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Terms of Service")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                    Group {
                        Text("Acceptance of Terms")
                            .font(.headline)
                        Text(
                            "By using NeuroLoop, you agree to these terms of service. If you do not agree to these terms, please do not use the application."
                        )

                        Text("User Responsibilities")
                            .font(.headline)
                        Text(
                            "You are responsible for maintaining the confidentiality of your data and for all activities that occur under your account."
                        )

                        Text("Limitations")
                            .font(.headline)
                        Text(
                            "NeuroLoop is provided 'as is' without any warranties. We are not responsible for any damages arising from the use of the application."
                        )
                    }
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                }
                .padding()
            }
            .toolbar {
                #if os(iOS)
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            dismiss()
                        }
                    }
                #else
                    ToolbarItem {
                        Button("Done") {
                            dismiss()
                        }
                    }
                #endif
            }
        }
    }
}

import NeuroLoopCore
import Neuro<PERSON>oopTypes
import SwiftUI

public struct ThemeSettingsView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @State private var selectedTheme: Theme

    public init() {
        _selectedTheme = State(initialValue: ThemeManager.shared.currentTheme)
    }

    public var body: some View {
        List {
            Section {
                ForEach(ThemeManager.shared.availableThemes, id: \.id) { theme in
                    ThemeRow(
                        theme: theme,
                        isSelected: theme.id == selectedTheme.id,
                        onSelect: {
                            selectedTheme = theme
                            themeManager.setTheme(theme)
                        }
                    )
                }
            } header: {
                Text("Choose a theme")
            } footer: {
                Text("Your theme preference will be saved and applied across the app.")
            }
        }
        .navigationTitle("Theme")
    }
}

private struct ThemeRow: View {
    let theme: Theme
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack {
                VStack(alignment: .leading) {
                    Text(theme.name)
                        .font(.headline)

                    Text(theme.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(.accentColor)
                }
            }
        }
        .foregroundColor(.primary)
    }
}

#if DEBUG
    struct ThemeSettingsView_Previews: PreviewProvider {
        static var previews: some View {
            NavigationView {
                ThemeSettingsView()
                    .environmentObject(NeuroLoopCore.ThemeManager.shared)
            }
        }
    }
#endif

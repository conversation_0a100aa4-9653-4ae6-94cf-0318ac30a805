import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import <PERSON><PERSON>

@available(iOS 17.0, macOS 14.0, *)
public struct ThemeSectionView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    public init() {}

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Appearance")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            SettingsThemePickerView()
                .environmentObject(themeManager)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor)
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        )
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct ThemeSectionView_Previews: PreviewProvider {
        static var previews: some View {
            ThemeSectionView()
                .environmentObject(NeuroLoopCore.ThemeManager.shared)
                .padding()
                .background(Color.gray.opacity(0.1))
        }
    }
#endif

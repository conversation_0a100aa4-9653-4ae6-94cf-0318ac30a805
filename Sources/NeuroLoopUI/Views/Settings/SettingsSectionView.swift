import NeuroLoopCore
import <PERSON>eur<PERSON><PERSON>oopTypes
import SwiftUI

public struct SettingsSectionView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    let title: String
    let content: () -> AnyView

    public init(
        title: String,
        @ViewBuilder content: @escaping () -> some View
    ) {
        self.title = title
        self.content = { AnyView(content()) }
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            content()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor)
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        )
    }
}

#if DEBUG
    struct SettingsSectionView_Previews: PreviewProvider {
        static var previews: some View {
            SettingsSectionView(title: "Test Section") {
                Text("Test Content")
            }
            .padding()
            .environmentObject(NeuroLoopCore.ThemeManager.shared)
        }
    }
#endif

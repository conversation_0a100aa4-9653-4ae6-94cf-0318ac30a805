import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import SwiftUI

public struct AboutSectionView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var showingPrivacyPolicy = false
    @State private var showingTermsOfService = false

    public init() {}

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("About")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            VStack(alignment: .leading, spacing: 8) {
                Text("NeuroLoop")
                    .font(.headline)
                Text("Version 1.0.0")
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
            }

            Divider()
                .background(themeManager.currentTheme.secondaryTextColor.color)

            Button(action: { showingPrivacyPolicy = true }) {
                HStack {
                    Image(systemName: "lock.shield")
                    Text("Privacy Policy")
                    Spacer()
                    Image(systemName: "chevron.right")
                }
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
            }

            Button(action: { showingTermsOfService = true }) {
                HStack {
                    Image(systemName: "doc.text")
                    Text("Terms of Service")
                    Spacer()
                    Image(systemName: "chevron.right")
                }
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
            }
        }
        .glassmorphicCard()
        .sheet(isPresented: $showingPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showingTermsOfService) {
            TermsOfServiceView()
        }
    }
}

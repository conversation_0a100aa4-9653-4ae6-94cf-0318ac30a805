import NeuroLoopCore
import NeuroLoopInterfaces
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import SwiftUI

public struct DataSectionView: View {
    @ObservedObject var viewModel: SettingsViewModel
    let dataExportService: DataExportServiceProtocol
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var showingExportSuccess = false
    @State private var showingExportError = false
    @State private var exportError: Error?

    public init(viewModel: SettingsViewModel, dataExportService: DataExportServiceProtocol) {
        self.viewModel = viewModel
        self.dataExportService = dataExportService
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Data")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            Button(action: {
                Task {
                    do {
                        _ = try await viewModel.exportData(using: dataExportService)
                        showingExportSuccess = true
                    } catch {
                        exportError = error
                        showingExportError = true
                    }
                }
            }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                    Text("Export Data")
                }
                .gradientButton(color: themeManager.currentTheme.accentColor.asColor)
            }

            Button(action: {
                Task {
                    do {
                        try await viewModel.deleteAllData(using: dataExportService)
                    } catch {
                        exportError = error
                        showingExportError = true
                    }
                }
            }) {
                HStack {
                    Image(systemName: "trash")
                    Text("Clear All Data")
                }
                .gradientButton(color: themeManager.currentTheme.errorColor.color)
            }
        }
        .glassmorphicCard()
        .modifier(ExportSuccessAlertModifier(showingExportSuccess: $showingExportSuccess))
        .modifier(
            ErrorAlertModifier(showingExportError: $showingExportError, exportError: exportError))
    }
}

// MARK: - Error Alert Modifier for Backwards Compatibility

private struct ErrorAlertModifier: ViewModifier {
    @Binding var showingExportError: Bool
    let exportError: Error?

    func body(content: Content) -> some View {
        content.alert("Error", isPresented: $showingExportError, presenting: exportError) { error in
            Button("OK", role: .cancel) {}
        } message: { error in
            Text(error.localizedDescription)
        }
    }
}

// MARK: - Export Success Alert Modifier for Backwards Compatibility

private struct ExportSuccessAlertModifier: ViewModifier {
    @Binding var showingExportSuccess: Bool

    func body(content: Content) -> some View {
        content.alert(
            "Export Successful", isPresented: $showingExportSuccess,
            actions: {
                Button("OK", role: .cancel) {}
            })
    }
}

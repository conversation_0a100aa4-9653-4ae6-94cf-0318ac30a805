import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import Swift<PERSON>

@available(iOS 17.0, macOS 14.0, *)
public struct MainTabView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel: PreviewMainTabViewModel

    public init() {
        _viewModel = StateObject(wrappedValue: PreviewMainTabViewModel())
    }

    public var body: some View {
        TabView(selection: $viewModel.selectedTab) {
            Text("Home Tab")
                .tabItem {
                    Label("Home", systemImage: MainTab.home.icon)
                }
                .tag(MainTab.home)

            Text("Affirmations Tab")
                .tabItem {
                    Label("Affirmations", systemImage: MainTab.affirmations.icon)
                }
                .tag(MainTab.affirmations)

            PreviewSettingsView()
                .tabItem {
                    Label("Settings", systemImage: MainTab.settings.icon)
                }
                .tag(MainTab.settings)
        }
        .tint(themeManager.currentTheme.accentColor.asColor)
    }

    // Add static preview method
    public static func preview() -> some View {
        MainTabView()
            .environmentObject(NeuroLoopCore.ThemeManager.shared)
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct MainTabView_Previews: PreviewProvider {
        static var previews: some View {
            MainTabView.preview()
        }
    }
#endif

import NeuroLoopCore
import NeuroLoopInterfaces
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import SwiftUI

/// Manages shared navigation state across the app
@MainActor
public class NavigationCoordinator: ObservableObject {
    // MARK: - Published Properties

    @Published public var homePath: [NavigationRoute] = []
    @Published public var libraryPath: [NavigationRoute] = []
    @Published public var settingsPath: [NavigationRoute] = []

    // MARK: - Initializer
    public init() {}

    // MARK: - Public Methods

    public func navigate(to route: NavigationRoute, in tab: Tab) {
        switch tab {
        case .home:
            homePath.append(route)
        case .library:
            libraryPath.append(route)
        case .settings:
            settingsPath.append(route)
        }
    }

    public func popToRoot(in tab: Tab) {
        switch tab {
        case .home:
            homePath.removeAll()
        case .library:
            libraryPath.removeAll()
        case .settings:
            settingsPath.removeAll()
        }
    }
}

// MARK: - Navigation Types

/// Represents the available tabs in the app
public enum Tab: String, CaseIterable {
    case home
    case library
    case settings

    var title: String {
        switch self {
        case .home: return "Home"
        case .library: return "Library"
        case .settings: return "Settings"
        }
    }

    var icon: String {
        switch self {
        case .home: return "house.fill"
        case .library: return "book.fill"
        case .settings: return "gear"
        }
    }
}

/// Represents possible navigation routes in the app
public enum NavigationRoute: Hashable {
    case affirmationDetail(UUID)
    case addAffirmation
    case settingsDetail(String)
    case streakDashboard
    case repetitionTracking(UUID)
    case speakAffirmation(UUID)

    public func hash(into hasher: inout Hasher) {
        switch self {
        case .affirmationDetail(let id):
            hasher.combine("affirmationDetail")
            hasher.combine(id)
        case .addAffirmation:
            hasher.combine("addAffirmation")
        case .settingsDetail(let key):
            hasher.combine("settingsDetail")
            hasher.combine(key)
        case .streakDashboard:
            hasher.combine("streakDashboard")
        case .repetitionTracking(let id):
            hasher.combine("repetitionTracking")
            hasher.combine(id)
        case .speakAffirmation(let id):
            hasher.combine("speakAffirmation")
            hasher.combine(id)
        }
    }

    public static func == (lhs: NavigationRoute, rhs: NavigationRoute) -> Bool {
        switch (lhs, rhs) {
        case (.affirmationDetail(let id1), .affirmationDetail(let id2)):
            return id1 == id2
        case (.addAffirmation, .addAffirmation):
            return true
        case (.settingsDetail(let key1), .settingsDetail(let key2)):
            return key1 == key2
        case (.streakDashboard, .streakDashboard):
            return true
        case (.repetitionTracking(let id1), .repetitionTracking(let id2)):
            return id1 == id2
        case (.speakAffirmation(let id1), .speakAffirmation(let id2)):
            return id1 == id2
        default:
            return false
        }
    }
}

extension Notification.Name {
    static let navigateToProgress = Notification.Name("navigateToProgress")
    static let navigateToSpeakAffirmation = Notification.Name("navigateToSpeakAffirmation")
}

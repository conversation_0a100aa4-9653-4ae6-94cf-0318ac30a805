import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public struct PreviewMainTabView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @State private var selectedTab: MainTab = .home

    public init() {}

    public var body: some View {
        TabView(selection: $selectedTab) {
            AsyncContentView {
                await PreviewHomeView(repository: await PreviewAffirmationRepository())
            }
            .tabItem {
                Label("Home", systemImage: MainTab.home.icon)
            }
            .tag(MainTab.home)

            AsyncContentView {
                await PreviewAffirmationListView(repository: await PreviewAffirmationRepository())
            }
            .tabItem {
                Label("Affirmations", systemImage: MainTab.affirmations.icon)
            }
            .tag(MainTab.affirmations)

            PreviewSettingsView()
                .tabItem {
                    Label("Settings", systemImage: MainTab.settings.icon)
                }
                .tag(MainTab.settings)
        }
        .tint(themeManager.currentTheme.accentColor.asColor)
    }

    // Helper view to handle async initialization
    struct AsyncContentView<Content: View>: View {
        let content: () async -> Content

        @State private var loadedContent: Content?

        var body: some View {
            ZStack {
                if let loadedContent {
                    loadedContent
                } else {
                    ProgressView()
                        .onAppear {
                            Task {
                                loadedContent = await content()
                            }
                        }
                }
            }
        }
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct PreviewMainTabView_Previews: PreviewProvider {
        static var previews: some View {
            PreviewMainTabView()
                .environmentObject(NeuroLoopCore.ThemeManager.shared)
        }
    }
#endif

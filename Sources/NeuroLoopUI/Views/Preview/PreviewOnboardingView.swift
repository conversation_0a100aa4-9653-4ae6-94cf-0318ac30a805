import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public struct PreviewOnboardingView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @State private var currentPage = 0
    @State private var showingMainApp = false

    private let pages = [
        OnboardingPage(
            title: "Welcome to NeuroLoop",
            description: "Your personal affirmation companion for positive change and growth.",
            imageName: "brain.head.profile",
            color: .blue
        ),
        OnboardingPage(
            title: "Track Your Progress",
            description: "Monitor your daily affirmations and build lasting habits.",
            imageName: "chart.line.uptrend.xyaxis",
            color: .green
        ),
        OnboardingPage(
            title: "Stay Motivated",
            description: "Get reminders and track your streaks to maintain momentum.",
            imageName: "flame.fill",
            color: .orange
        ),
        OnboardingPage(
            title: "Ready to Begin?",
            description: "Start your journey to positive change today.",
            imageName: "checkmark.circle.fill",
            color: .purple
        ),
    ]

    public init() {}

    public var body: some View {
        ZStack {
            themeManager.currentTheme.backgroundColor.asColor
                .ignoresSafeArea()

            VStack {
                TabView(selection: $currentPage) {
                    ForEach(0..<pages.count, id: \.self) { index in
                        PreviewOnboardingPageView(page: pages[index])
                            .tag(index)
                    }
                }
                #if os(iOS)
                .tabViewStyle(.page)
                .indexViewStyle(.page(backgroundDisplayMode: .always))
                #else
                .tabViewStyle(.automatic)
                #endif

                if currentPage == pages.count - 1 {
                    Button(action: { showingMainApp = true }) {
                        Text("Get Started")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(themeManager.currentTheme.accentColor.asColor)
                            .cornerRadius(12)
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 32)
                } else {
                    Button(action: { currentPage += 1 }) {
                        Text("Next")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(themeManager.currentTheme.accentColor.asColor)
                            .cornerRadius(12)
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 32)
                }
            }
        }
        #if os(iOS)
        .fullScreenCover(isPresented: $showingMainApp) {
            PreviewMainTabView()
        }
        #else
        .sheet(isPresented: $showingMainApp) {
            PreviewMainTabView()
        }
        #endif
    }
}

private struct OnboardingPage {
    let title: String
    let description: String
    let imageName: String
    let color: Color
}

private struct PreviewOnboardingPageView: View {
    let page: OnboardingPage
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    var body: some View {
        VStack(spacing: 32) {
            Image(systemName: page.imageName)
                .font(.system(size: 80))
                .foregroundColor(page.color)
                .padding()
                .background(
                    Circle()
                        .fill(page.color.opacity(0.2))
                        .frame(width: 160, height: 160)
                )

            VStack(spacing: 16) {
                Text(page.title)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                Text(page.description)
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                    .padding(.horizontal)
            }
        }
        .padding()
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct PreviewOnboardingView_Previews: PreviewProvider {
        static var previews: some View {
            PreviewOnboardingView()
                .environmentObject(NeuroLoopCore.ThemeManager.shared)
        }
    }
#endif

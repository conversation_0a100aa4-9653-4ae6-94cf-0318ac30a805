import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public struct PreviewAddAffirmationView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel: PreviewAddAffirmationViewModel
    @Environment(\.dismiss) private var dismiss

    public init(repository: AffirmationRepositoryProtocol) {
        _viewModel = StateObject(
            wrappedValue: PreviewAddAffirmationViewModel(repository: repository)
        )
    }

    public var body: some View {
        NavigationStack {
            Form {
                Section {
                    TextField("Enter your affirmation", text: $viewModel.text)
                        .textFieldStyle(.roundedBorder)
                } header: {
                    Text("Affirmation Text")
                }

                Section {
                    Picker("Category", selection: $viewModel.selectedCategory) {
                        ForEach(AffirmationCategory.allCases, id: \.self) { category in
                            Text(category.rawValue)
                                .tag(category)
                        }
                    }
                } header: {
                    Text("Category")
                }

                Section {
                    Button(action: {
                        // Preview recording action
                    }) {
                        HStack {
                            Image(systemName: "mic.fill")
                            Text("Record Voice")
                        }
                    }
                } header: {
                    Text("Voice Recording")
                }

                Section {
                    ZStack {
    Button(action: {
        Task {
            await viewModel.save()
            dismiss()
        }
    }) {
        if viewModel.isSaving {
            ProgressView()
                .frame(maxWidth: .infinity)
        } else {
            Text("Save Affirmation")
                .frame(maxWidth: .infinity)
        }
    }
    .disabled(viewModel.text.isEmpty || viewModel.isSaving)
    .accessibilityLabel("Save Affirmation")
    .accessibilityHint(viewModel.isSaving ? "Saving in progress" : (viewModel.text.isEmpty ? "Enter affirmation text to enable" : "Saves your new affirmation and returns to the previous screen"))
    .accessibilityAddTraits(.isButton)
}
                }
            }
            .navigationTitle("Add Affirmation")
            #if os(iOS)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("Cancel") {
    dismiss()
}
.accessibilityLabel("Cancel adding affirmation")
.accessibilityHint("Double tap to cancel and return to the previous screen.")
.accessibilityAddTraits(.isButton)
                    }
                }
            #endif
        }
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct PreviewAddAffirmationView_Previews: PreviewProvider {
        static var previews: some View {
            Text("Preview not available")
            // Temporarily disabled due to async initialization issues
        }
    }
#endif

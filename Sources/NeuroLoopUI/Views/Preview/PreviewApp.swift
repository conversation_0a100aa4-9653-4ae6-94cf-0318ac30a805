import NeuroLoopCore
import <PERSON>euroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public struct PreviewApp: App {
    @StateObject private var themeManager = PreviewServiceFactory.shared.getThemeManager()
    private let hapticManager = PreviewServiceFactory.shared.getHapticManager()
    @State private var showingOnboarding = true

    public init() {}

    public var body: some Scene {
        WindowGroup {
            if showingOnboarding {
                PreviewOnboardingView()
                    .environmentObject(themeManager)
                    .environment(\.hapticManager, hapticManager)
            } else {
                PreviewMainTabView()
                    .environmentObject(themeManager)
                    .environment(\.hapticManager, hapticManager)
            }
        }
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct PreviewMainTabView_Previews_App: PreviewProvider {
        static var previews: some View {
            Group {
                // iPhone 16 Pro Max
                PreviewMainTabView()
                    .environmentObject(PreviewServiceFactory.shared.getThemeManager())
                    .environment(\.hapticManager, PreviewServiceFactory.shared.getHapticManager())
                    .previewDevice(PreviewDevice(rawValue: "iPhone 16 Pro Max"))
                    .previewDisplayName("iPhone 16 Pro Max")

                // iPhone 16 Pro Max Landscape
                PreviewMainTabView()
                    .environmentObject(PreviewServiceFactory.shared.getThemeManager())
                    .environment(\.hapticManager, PreviewServiceFactory.shared.getHapticManager())
                    .previewDevice(PreviewDevice(rawValue: "iPhone 16 Pro Max"))
                    .previewInterfaceOrientation(.landscapeLeft)
                    .previewDisplayName("iPhone 16 Pro Max Landscape")

                // iPhone SE
                PreviewMainTabView()
                    .environmentObject(PreviewServiceFactory.shared.getThemeManager())
                    .environment(\.hapticManager, PreviewServiceFactory.shared.getHapticManager())
                    .previewDevice(PreviewDevice(rawValue: "iPhone SE (3rd generation)"))
                    .previewDisplayName("iPhone SE")

                // iPad Pro
                PreviewMainTabView()
                    .environmentObject(PreviewServiceFactory.shared.getThemeManager())
                    .environment(\.hapticManager, PreviewServiceFactory.shared.getHapticManager())
                    .previewDevice(PreviewDevice(rawValue: "iPad Pro (12.9-inch) (6th generation)"))
                    .previewDisplayName("iPad Pro")
            }
        }
    }
#endif

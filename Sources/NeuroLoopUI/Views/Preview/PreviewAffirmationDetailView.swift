import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

@available(iOS 17.0, macOS 14.0, *)
public struct PreviewAffirmationDetailView: View {
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
    @StateObject private var viewModel: PreviewAffirmationDetailViewModel
    @Environment(\.dismiss) private var dismiss

    private let affirmation: any AffirmationProtocol

    public init(viewModel: PreviewAffirmationDetailViewModel, affirmation: any AffirmationProtocol)
    {
        _viewModel = StateObject(wrappedValue: viewModel)
        self.affirmation = affirmation
    }

    public var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    AffirmationTextView(text: affirmation.text)
                    ProgressSectionView(progress: affirmation.todayProgress)
                    StatisticsSectionView(affirmation: affirmation)
                    ActionsSectionView(viewModel: viewModel)
                }
                .padding()
            }
            .navigationTitle("Affirmation Details")
            #if os(iOS)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        FavoriteButton(
                            isFavorite: affirmation.isFavorite,
                            action: {
                                Task {
                                    await viewModel.toggleFavorite()
                                }
                            }
                        )
                    }
                }
            #endif
        }
    }

    // MARK: - Affirmation Text View

    private struct AffirmationTextView: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
        let text: String

        var body: some View {
            Text(text)
                .font(.title2)
                .fontWeight(.semibold)
                .multilineTextAlignment(.center)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
                .padding()
                .frame(maxWidth: .infinity)
                .background(CardBackground())
        }
    }

    // MARK: - Progress Section View

    private struct ProgressSectionView: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
        let progress: Double

        var body: some View {
            VStack(spacing: 16) {
                ProgressView(value: progress)
                    .tint(themeManager.currentTheme.accentColor.asColor)

                HStack {
                    Text("Today's Progress")
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)

                    Spacer()

                    Text("\(Int(progress * 100))%")
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
                }
            }
            .padding()
            .background(CardBackground())
        }
    }

    // MARK: - Statistics Section View

    private struct StatisticsSectionView: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
        let affirmation: any AffirmationProtocol

        var body: some View {
            VStack(alignment: .leading, spacing: 16) {
                Text("Statistics")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                HStack {
                    StatCard(
                        title: "Completed Cycles",
                        value: "\(affirmation.completedCycles)",
                        icon: "checkmark.circle.fill",
                        color: .green
                    )

                    StatCard(
                        title: "Current Repetitions",
                        value: "\(affirmation.currentRepetitions)",
                        icon: "repeat.circle.fill",
                        color: .blue
                    )
                }

                HStack {
                    StatCard(
                        title: "Play Count",
                        value: "\(affirmation.playCount)",
                        icon: "play.circle.fill",
                        color: .purple
                    )

                    StatCard(
                        title: "Energy Level",
                        value: "\(Int(affirmation.energyLevel * 100))%",
                        icon: "bolt.circle.fill",
                        color: .yellow
                    )
                }
            }
            .padding()
            .background(CardBackground())
        }
    }

    // MARK: - Actions Section View

    private struct ActionsSectionView: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
        let viewModel: PreviewAffirmationDetailViewModel

        var body: some View {
            VStack(spacing: 16) {
                ActionButton(
                    title: "Record Repetition",
                    icon: "checkmark.circle.fill",
                    color: themeManager.currentTheme.accentColor.asColor
                ) {
                    Task {
                        await viewModel.recordRepetition()
                    }
                }

                ActionButton(
                    title: "Start New Cycle",
                    icon: "play.circle.fill",
                    color: themeManager.currentTheme.accentColor.asColor
                ) {
                    Task {
                        await viewModel.startCycle()
                    }
                }
            }
            .padding()
            .background(CardBackground())
        }
    }

    // MARK: - Card Background

    private struct CardBackground: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

        var body: some View {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? themeManager.currentTheme.cardBackgroundColor.asColor
                        .asFallbackGradient()
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        }
    }

    // MARK: - Action Button

    private struct ActionButton: View {
        let title: String
        let icon: String
        let color: Color
        let action: () -> Void

        var body: some View {
            Button(action: action) {
                HStack {
                    Image(systemName: icon)
                    Text(title)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(color)
                .foregroundColor(.white)
                .cornerRadius(12)
            }
        }
    }

    // MARK: - Favorite Button

    private struct FavoriteButton: View {
        @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager
        let isFavorite: Bool
        let action: () -> Void

        var body: some View {
            Button(action: action) {
                Image(systemName: isFavorite ? "star.fill" : "star")
                .foregroundColor(
                    isFavorite
                        ? .yellow : themeManager.currentTheme.accentColor.asColor)
            }
        }
    }
}

private struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color.primary.opacity(0.05))
        .cornerRadius(12)
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct PreviewAffirmationDetailView_Previews: PreviewProvider {
        static var previews: some View {
            Text("Preview not available")
            // Temporarily disabled due to async initialization issues
        }
    }
#endif

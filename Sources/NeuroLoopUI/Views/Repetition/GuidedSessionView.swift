import AVFoundation
import Foundation
import NeuroLoopCore
import NeuroLoopModels
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes

import SwiftUI

public struct GuidedSessionView: View {
    @ObservedObject var viewModel: GuidedSessionViewModel
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var repetitionCount = 3
    @State private var backgroundSound = GuidedSessionViewModel.BackgroundSound.none
    @State private var showLearnMore = false

    public init(viewModel: GuidedSessionViewModel) {
        self.viewModel = viewModel
    }

    public var body: some View {
        ZStack {
            themeManager.currentTheme.backgroundColor.asColor
                .ignoresSafeArea()

            VStack(spacing: 24) {
                // Session Controls
                VStack(spacing: 16) {
                    // Progress and Timer
                    HStack(spacing: 20) {
                        // Circular Progress
                        ZStack {
                            Circle()
                                .stroke(
                                    themeManager.currentTheme.accentColor.asColor.opacity(0.2),
                                    lineWidth: 8
                                )

                            Circle()
                                .trim(
                                    from: 0,
                                    to: CGFloat(viewModel.currentStep)
                                        / CGFloat(viewModel.totalSteps)
                                )
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            themeManager.currentTheme.accentColor.asColor,
                                            themeManager.currentTheme.accentColor.asColor.opacity(
                                                0.8),
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    style: StrokeStyle(lineWidth: 8, lineCap: .round)
                                )
                                .rotationEffect(.degrees(-90))

                            VStack(spacing: 4) {
                                Text(viewModel.timeRemaining)
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(
                                        themeManager.currentTheme.primaryTextColor.color)

                                Text("remaining")
                                    .font(.subheadline)
                                    .foregroundColor(
                                        themeManager.currentTheme.secondaryTextColor.color)
                            }
                        }
                        .frame(width: 120, height: 120)

                        // Controls
                        VStack(spacing: 16) {
                            Button(action: viewModel.togglePlayback) {
                                Image(systemName: viewModel.isPlaying ? "pause.fill" : "play.fill")
                                    .font(.title)
                                    .foregroundColor(.white)
                                    .frame(width: 64, height: 64)
                                    .background(
                                        Circle()
                                            .fill(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [
                                                        themeManager.currentTheme.accentColor
                                                            .asColor,
                                                        themeManager.currentTheme.accentColor
                                                            .asColor.opacity(0.8),
                                                    ]),
                                                    startPoint: .topLeading,
                                                    endPoint: .bottomTrailing
                                                )
                                            )
                                    )
                                    .shadow(
                                        color: themeManager.currentTheme.accentColor.asColor
                                            .opacity(0.3),
                                        radius: 10,
                                        x: 0,
                                        y: 5
                                    )
                            }

                            Button(action: viewModel.repeatExactly) {
                                HStack {
                                    Image(systemName: "repeat")
                                    Text("Repeat Exactly")
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.gray.opacity(0.2))
                                )
                                .foregroundColor(.primary)
                            }
                        }
                    }
                }
                .cardStyle()

                // Session Settings
                VStack(alignment: .leading, spacing: 16) {
                    Text("Session Settings")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                    VStack(spacing: 20) {
                        // Repetition Count
                        HStack {
                            Text("Repetitions")
                                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                            Spacer()

                            Picker("Repetitions", selection: $repetitionCount) {
                                ForEach(1...10, id: \.self) { count in
                                    Text("\(count)").tag(count)
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                            .tint(themeManager.currentTheme.accentColor.asColor)
                        }

                        Divider()
                            .background(themeManager.currentTheme.borderColor.foreground)

                        // Background Sound
                        HStack {
                            Text("Background Sound")
                                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)

                            Spacer()

                            Picker("Sound", selection: $backgroundSound) {
                                ForEach(GuidedSessionViewModel.BackgroundSound.allCases, id: \.self)
                                { sound in
                                    Text(sound.rawValue.capitalized)
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                            .tint(themeManager.currentTheme.accentColor.asColor)
                        }
                    }
                }
                .cardStyle()

                // Action Buttons
                VStack(spacing: 16) {
                    Button(action: viewModel.endSession) {
                        Text("End Session")
                            .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.accentColor)
                    )
                    .foregroundColor(.white)

                    Button(action: { showLearnMore = true }) {
                        Text("Learn More")
                            .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.2))
                    )
                    .foregroundColor(.primary)
                }
            }
            .padding()

            // Step indicator
            ProgressView(value: Double(viewModel.currentStep), total: Double(viewModel.totalSteps))
                .tint(themeManager.currentTheme.accentColor.asColor)
                .padding()

            // Step content
            Group {
                switch viewModel.currentStepType {
                case .preparation:
                    PreparationStepView(text: viewModel.preparationText)
                case .breathing:
                    BreathingStepView(
                        duration: viewModel.breathingDuration, onComplete: viewModel.nextStep)
                case .affirmation:
                    AffirmationStepView(
                        affirmation: viewModel.affirmationText,
                        repetitions: viewModel.repetitions,
                        onComplete: viewModel.nextStep
                    )
                case .reflection:
                    ReflectionStepView(
                        text: viewModel.reflectionText, onComplete: viewModel.nextStep)
                case .complete:
                    SessionCompleteView(onDone: viewModel.finishSession)
                }
            }
            .transition(.slide)
            .animation(.easeInOut, value: viewModel.currentStep)

            // Customization
            if viewModel.showCustomization {
                SessionCustomizationView(
                    sessionLength: $viewModel.sessionLength,
                    guidanceLevel: $viewModel.guidanceLevel,
                    voiceType: $viewModel.voiceType,
                    backgroundSound: $viewModel.backgroundSound
                )
            }

            // Learn more
            if viewModel.showLearnMore {
                LearnMoreView(content: viewModel.learnMoreContent)
            }

            Spacer()
        }
        .navigationTitle("Guided Session")
        .toolbar {
            #if os(iOS)
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { viewModel.showCustomization.toggle() }) {
                        Image(systemName: "slider.horizontal.3")
                            .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                }
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { viewModel.exitSession() }) {
                        Image(systemName: "xmark")
                            .foregroundColor(themeManager.currentTheme.errorColor.asColor)
                    }
                }
            #else
                ToolbarItem(placement: .automatic) {
                    Button(action: { viewModel.showCustomization.toggle() }) {
                        Image(systemName: "slider.horizontal.3")
                            .foregroundColor(themeManager.currentTheme.accentColor.asColor)
                    }
                }
                ToolbarItem(placement: .automatic) {
                    Button(action: { viewModel.exitSession() }) {
                        Image(systemName: "xmark")
                            .foregroundColor(themeManager.currentTheme.errorColor.asColor)
                    }
                }
            #endif
        }
        .onAppear { viewModel.startSession() }
    }
}

// Placeholder subviews for each step
struct PreparationStepView: View {
    let text: String
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        VStack {
            Text("Preparation")
                .font(.title)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            Text(text)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                .padding()
        }
    }
}

struct BreathingStepView: View {
    let duration: TimeInterval
    let onComplete: () -> Void
    @State private var timeRemaining: TimeInterval
    @EnvironmentObject private var themeManager: ThemeManager

    init(duration: TimeInterval, onComplete: @escaping () -> Void) {
        self.duration = duration
        self.onComplete = onComplete
        self._timeRemaining = State(initialValue: duration)
    }

    var body: some View {
        VStack {
            Text("Deep Breathing")
                .font(.title)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            Text("Breathe deeply for \(Int(timeRemaining)) seconds")
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                .padding()
            ProgressView(value: duration - timeRemaining, total: duration)
                .tint(themeManager.currentTheme.accentColor.asColor)
                .padding()
        }
        .onReceive(Timer.publish(every: 1, on: .main, in: .common).autoconnect()) { _ in
            if timeRemaining > 1 {
                timeRemaining -= 1
            } else {
                onComplete()
            }
        }
    }
}

struct ReflectionStepView: View {
    let text: String
    let onComplete: () -> Void
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        VStack {
            Text("Reflection")
                .font(.title)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            Text(text)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.asColor)
                .padding()
            Button("Finish") {
                onComplete()
            }
            .foregroundColor(.white)
            .padding()
            .background(themeManager.currentTheme.accentColor.asColor)
            .cornerRadius(10)
        }
    }
}

struct SessionCompleteView: View {
    let onDone: () -> Void
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        VStack {
            Text("Session Complete!")
                .font(.largeTitle)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
                .padding()
            Button("Done") {
                onDone()
            }
            .foregroundColor(.white)
            .padding()
            .background(themeManager.currentTheme.accentColor.asColor)
            .cornerRadius(10)
        }
    }
}

// Placeholder for audio controls
struct AudioControlsView: View {
    let isPlaying: Bool
    let onPlay: () -> Void
    let onPause: () -> Void
    @Binding var volume: Float
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        HStack {
            Button(action: isPlaying ? onPause : onPlay) {
                Image(systemName: isPlaying ? "pause.circle" : "play.circle")
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)
            }
            Slider(value: $volume, in: 0...1)
                .tint(themeManager.currentTheme.accentColor.asColor)
        }
        .padding()
    }
}

// Placeholder for customization
struct SessionCustomizationView: View {
    @Binding var sessionLength: GuidedSessionViewModel.SessionLength
    @Binding var guidanceLevel: GuidedSessionViewModel.GuidanceLevel
    @Binding var voiceType: GuidedSessionViewModel.VoiceType
    @Binding var backgroundSound: GuidedSessionViewModel.BackgroundSound
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Session Length")
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            Picker("Length", selection: $sessionLength) {
                ForEach(GuidedSessionViewModel.SessionLength.allCases, id: \.self) {
                    (length: GuidedSessionViewModel.SessionLength) in
                    Text(length.rawValue.capitalized)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .tint(themeManager.currentTheme.accentColor.asColor)

            Text("Guidance Level")
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            Picker("Guidance", selection: $guidanceLevel) {
                ForEach(GuidedSessionViewModel.GuidanceLevel.allCases, id: \.self) {
                    (level: GuidedSessionViewModel.GuidanceLevel) in
                    Text(level.rawValue.capitalized)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .tint(themeManager.currentTheme.accentColor.asColor)

            Text("Voice Type")
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            Picker("Voice", selection: $voiceType) {
                ForEach(GuidedSessionViewModel.VoiceType.allCases, id: \.self) {
                    (type: GuidedSessionViewModel.VoiceType) in
                    Text(type.rawValue.capitalized)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .tint(themeManager.currentTheme.accentColor.asColor)

            Text("Background Sound")
                .foregroundColor(themeManager.currentTheme.primaryTextColor.asColor)
            Picker("Sound", selection: $backgroundSound) {
                ForEach(GuidedSessionViewModel.BackgroundSound.allCases, id: \.self) {
                    (sound: GuidedSessionViewModel.BackgroundSound) in
                    Text(sound.rawValue.capitalized)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .tint(themeManager.currentTheme.accentColor.asColor)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                            ]),
                            startPoint: .top, endPoint: .bottom
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.asColor.opacity(0.1), radius: 10,
                    x: 0, y: 5)
        )
    }
}

// Placeholder for learn more
struct LearnMoreView: View {
    let content: String
    var body: some View {
        ScrollView {
            Text(content)
                .padding()
        }
    }
}

// MARK: - Preview Provider
struct GuidedSessionView_Previews: PreviewProvider {
    static var previews: some View {
        GuidedSessionView(viewModel: GuidedSessionViewModel())
            .environmentObject(ThemeManager.shared)
    }
}

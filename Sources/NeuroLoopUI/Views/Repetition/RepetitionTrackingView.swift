import NeuroLoopCore
import NeuroLoopInterfaces
import SwiftUI

// MARK: - Types

struct Repetition: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let isCompleted: Bool

    init(id: UUID = UUID(), title: String, description: String, isCompleted: Bool = false) {
        self.id = id
        self.title = title
        self.description = description
        self.isCompleted = isCompleted
    }
}

public struct RepetitionTrackingView: View {
    @ObservedObject private var viewModel: RepetitionTrackingViewModel
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var showConfetti: Bool = false
    @State private var showGuidedSession: Bool = false

    // Enum to track which sheet to show
    private enum ActiveSheet: Identifiable {
        case restoration, guidedSession
        var id: Int { hashValue }
    }
    @State private var activeSheet: ActiveSheet?

    public init(viewModel: RepetitionTrackingViewModel) {
        self.viewModel = viewModel
    }

    public var body: some View {
        ScrollView {
            mainContent
        }
        .overlay(errorAndConfettiOverlay)
        .sheet(item: $activeSheet) { item in
            sheetContent(for: item)
        }
        .onChange(of: viewModel.showRestorationFlow) { oldValue, show in
            if show {
                activeSheet = .restoration
            }
        }
        .onAppear {
            Task {
                await viewModel.loadData()
            }
        }
    }

    private var mainContent: some View {
        VStack {
            // Header with affirmation info
            headerSection

            if viewModel.isLoading {
                RepetitionSkeletonView()
            } else {
                repetitionTrackingContent
            }
        }
    }

    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading) {
                Text("Day \(viewModel.currentDay) of 7")
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)

                Text("Affirmation")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
            }

            Spacer()

            Button(action: {
                showGuidedSession = true
            }) {
                Label("Guided", systemImage: "waveform.path")
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        Capsule()
                            .fill(
                                themeManager.currentTheme.accentColor.asGradient
                                    ?? LinearGradient(
                                        gradient: Gradient(colors: [
                                            themeManager.currentTheme.accentColor.asColor,
                                            themeManager.currentTheme.accentColor.asColor,
                                        ]),
                                        startPoint: .top, endPoint: .bottom
                                    )
                            )
                            .opacity(0.2)
                    )
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)
            }
        }
        .padding(.horizontal)
        .padding(.top)
    }

    private var repetitionTrackingContent: some View {
        VStack(spacing: 20) {
            // Affirmation text display with glassmorphism
            affirmationTextSection
            // Progress section
            progressSection
            // History section
            historySection
            // Repetition button
            repetitionButton
            // Audio playback (if available)
            if viewModel.hasRecording {
                AudioPlaybackView(
                    isPlaying: viewModel.isPlaying,
                    progress: viewModel.playbackProgress,
                    onPlay: { viewModel.playRecording() },
                    onPause: { viewModel.pausePlayback() }
                )
                .padding()
            }
        }
        .padding()
    }

    private var affirmationTextSection: some View {
        Text(viewModel.affirmation.text)
            .font(.title2)
            .fontWeight(.medium)
            .multilineTextAlignment(.center)
            .padding()
            .frame(maxWidth: .infinity)
            .background(
                ZStack {
                    // Glassmorphism effect
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            themeManager.currentTheme.cardBackgroundColor.asGradient
                                ?? LinearGradient(
                                    gradient: Gradient(colors: [
                                        themeManager.currentTheme
                                            .cardBackgroundColor.asColor,
                                        themeManager.currentTheme
                                            .cardBackgroundColor.asColor,
                                    ]),
                                    startPoint: .top, endPoint: .bottom
                                )
                        )
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.ultraThinMaterial)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            .white.opacity(0.5),
                                            .clear,
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                }
            )
    }

    private var progressSection: some View {
        VStack(spacing: 16) {
            Text("Today's Progress")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            HStack(spacing: 20) {
                ProgressCircle(
                    progress: viewModel.todayRepetitions,
                    total: viewModel.dailyGoal,
                    color: themeManager.currentTheme.accentColor.asColor
                )

                VStack(alignment: .leading, spacing: 8) {
                    Text("\(viewModel.todayRepetitions) / \(viewModel.dailyGoal)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(
                            themeManager.currentTheme.primaryTextColor.color)

                    Text("Repetitions")
                        .font(.subheadline)
                        .foregroundColor(
                            themeManager.currentTheme.secondaryTextColor.color)
                }
            }
        }
        .padding()
        .background(
            ZStack {
                // Glassmorphism effect
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor
                                        .asColor,
                                    themeManager.currentTheme.cardBackgroundColor
                                        .asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        .white.opacity(0.5),
                                        .clear,
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(
                            0.2),
                        radius: 15,
                        x: 0,
                        y: 5
                    )
            }
        )
    }

    private var historySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("History")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            ForEach(viewModel.recentHistory) { entry in
                HStack {
                    Text(entry.date, style: .date)
                        .foregroundColor(
                            themeManager.currentTheme.primaryTextColor.color)

                    Spacer()

                    Text("\(entry.count) reps")
                        .foregroundColor(
                            themeManager.currentTheme.secondaryTextColor.color)
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(
                            themeManager.currentTheme.cardBackgroundColor.asGradient
                                ?? LinearGradient(
                                    gradient: Gradient(colors: [
                                        themeManager.currentTheme
                                            .cardBackgroundColor.asColor,
                                        themeManager.currentTheme
                                            .cardBackgroundColor.asColor,
                                    ]),
                                    startPoint: .top, endPoint: .bottom
                                )
                        )
                        .opacity(0.5)
                )
            }
        }
        .padding()
        .background(
            ZStack {
                // Glassmorphism effect
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor
                                        .asColor,
                                    themeManager.currentTheme.cardBackgroundColor
                                        .asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        .white.opacity(0.5),
                                        .clear,
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(
                            0.2),
                        radius: 15,
                        x: 0,
                        y: 5
                    )
            }
        )
    }

    private var repetitionButton: some View {
        Button(action: {
            #if canImport(UIKit)
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()
            #endif
            viewModel.performRepetition()

            // Show confetti if milestone reached
            if viewModel.isMilestoneReached {
                #if canImport(UIKit)
                    let notificationGenerator = UINotificationFeedbackGenerator()
                    notificationGenerator.notificationOccurred(.success)
                #endif
                withAnimation {
                    showConfetti = true
                }

                // Hide confetti after delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    withAnimation {
                        showConfetti = false
                    }
                }
            }
        }) {
            Text("Record Repetition")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            themeManager.currentTheme.accentColor.asColor,
                            themeManager.currentTheme.accentColor.asColor.opacity(
                                0.8),
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(15)
                .shadow(
                    color: themeManager.currentTheme.accentColor.asColor.opacity(
                        0.4),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        }
        .disabled(!viewModel.canPerformRepetition)
        .opacity(viewModel.canPerformRepetition ? 1.0 : 0.6)
        .padding(.bottom)
    }

    private var errorAndConfettiOverlay: some View {
        ZStack {
            if let error = viewModel.error {
                ErrorView(
                    error: error,
                    retryAction: { viewModel.retry() },
                    dismissAction: { viewModel.dismissError() }
                )
            }
            if showConfetti {
                ConfettiView()
                    .transition(.opacity)
            }
        }
    }

    private func sheetContent(for item: ActiveSheet) -> some View {
        switch item {
        case .restoration:
            return AnyView(
                NeuralPathwayRestorationView(
                    viewModel: NeuralPathwayRestorationViewModel(
                        affirmation: viewModel.affirmation,
                        repetitionService: viewModel.repetitionService,
                        onCycleRestarted: { viewModel.onRestorationComplete() }
                    )
                ))
        case .guidedSession:
            return AnyView(GuidedSessionView(viewModel: GuidedSessionViewModel()))
        }
    }

    // MARK: - Preview

    #if DEBUG
        struct RepetitionTrackingView_Previews: PreviewProvider {
            static var previews: some View {
                if #available(iOS 17.0, macOS 14.0, *) {
                    RepetitionTrackingView(viewModel: RepetitionTrackingViewModel.preview)
                } else {
                    Text("Preview not available")
                }
            }
        }
    #endif

    // MARK: - Repetition Skeleton View

    private struct RepetitionSkeletonView: View {
        var body: some View {
            VStack(spacing: 24) {
                // Affirmation text skeleton
                SkeletonView(size: CGSize(width: 240, height: 32), cornerRadius: 8)
                // Progress indicators skeleton
                HStack(spacing: 40) {
                    SkeletonView(size: CGSize(width: 80, height: 80), cornerRadius: 40)
                    SkeletonView(size: CGSize(width: 80, height: 80), cornerRadius: 40)
                }
                // Streak calendar skeleton
                SkeletonView(size: CGSize(width: 220, height: 36), cornerRadius: 8)
                // Repetition button skeleton
                SkeletonView(size: CGSize(width: 200, height: 44), cornerRadius: 12)
                // Guided session button skeleton
                SkeletonView(size: CGSize(width: 180, height: 32), cornerRadius: 12)
                // Audio playback skeleton
                SkeletonView(size: CGSize(width: 220, height: 40), cornerRadius: 8)
            }
            .padding()
            .accessibilityHidden(true)
        }
    }

    struct ProgressCircle: View {
        let progress: Int
        let total: Int
        let color: Color

        private var percentage: Double {
            Double(progress) / Double(total)
        }

        var body: some View {
            ZStack {
                Circle()
                    .stroke(color.opacity(0.2), lineWidth: 8)

                Circle()
                    .trim(from: 0, to: percentage)
                    .stroke(color, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut, value: percentage)

                Text("\(Int(percentage * 100))%")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(color)
            }
            .frame(width: 100, height: 100)
        }
    }

    struct ProgressStatCard: View {
        let title: String
        let value: String
        let icon: String
        let color: Color
        @EnvironmentObject private var themeManager: ThemeManager

        var body: some View {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Text(value)
                    .font(.headline)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                Text(title)
                    .font(.caption)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.1), radius: 10,
                        x: 0, y: 5)
            )
        }
    }

    struct RepetitionCard: View {
        let repetition: Repetition
        let onTap: () -> Void
        @EnvironmentObject private var themeManager: ThemeManager

        var body: some View {
            Button(action: onTap) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(repetition.title)
                            .font(.headline)
                            .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                        Text(repetition.description)
                            .font(.subheadline)
                            .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                            .lineLimit(2)
                    }

                    Spacer()

                    if repetition.isCompleted {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(themeManager.currentTheme.successColor.color)
                    } else {
                        Image(systemName: "circle")
                            .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            themeManager.currentTheme.cardBackgroundColor.asGradient
                                ?? LinearGradient(
                                    gradient: Gradient(colors: [
                                        themeManager.currentTheme.cardBackgroundColor.asColor,
                                        themeManager.currentTheme.cardBackgroundColor.asColor,
                                    ]),
                                    startPoint: .top, endPoint: .bottom
                                )
                        )
                        .shadow(
                            color: themeManager.currentTheme.shadowColor.color.opacity(0.05),
                            radius: 5, x: 0, y: 2)
                )
            }
        }
    }
}

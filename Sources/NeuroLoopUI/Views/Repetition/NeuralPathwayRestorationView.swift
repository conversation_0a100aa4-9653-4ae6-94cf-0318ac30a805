import NeuroLoopCore
import SwiftUI

public struct NeuralPathwayRestorationView: View {
    @ObservedObject var viewModel: NeuralPathwayRestorationViewModel
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var showLearnMore: Bool = false

    public init(viewModel: NeuralPathwayRestorationViewModel) {
        self.viewModel = viewModel
    }

    public var body: some View {
        ZStack {
            backgroundView
            mainContent
        }
        .navigationTitle("Neural Pathways")
    }

    private var backgroundView: some View {
        Group {
            if let gradient = themeManager.currentTheme.backgroundColor.asGradient {
                gradient
            } else {
                themeManager.currentTheme.backgroundColor.asColor
            }
        }
        .ignoresSafeArea()
    }

    private var mainContent: some View {
        ScrollView {
            VStack(spacing: 24) {
                headerSection
                progressVisualizationSection
                statsSection
                startSessionButton
            }
            .padding(.vertical)
        }
    }

    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Neural Pathway Restoration")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            Text(
                "Track and visualize your progress in rewiring neural pathways through consistent practice."
            )
            .font(.subheadline)
            .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
        }
        .padding()
        .background(headerBackground)
    }

    private var headerBackground: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.7))
                .background(RoundedRectangle(cornerRadius: 16).fill(.ultraThinMaterial))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    .white.opacity(0.5), .clear,
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.foreground.opacity(0.2),
                    radius: 15, x: 0, y: 5
                )
        }
    }

    private var progressVisualizationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Current Progress")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            NeuralPathwayProgressView(progress: viewModel.neuralStrength)
                .frame(height: 200)
        }
        .padding()
        .background(progressBackground)
    }

    private var progressBackground: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                            ]),
                            startPoint: .top, endPoint: .bottom
                        )
                )
                .background(RoundedRectangle(cornerRadius: 16).fill(.ultraThinMaterial))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    .white.opacity(0.5), .clear,
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.foreground.opacity(0.2),
                    radius: 15, x: 0, y: 5
                )
        }
    }

    private var statsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Statistics")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            HStack(spacing: 16) {
                JourneyStatCard(
                    title: "Total Sessions",
                    value: "\(viewModel.totalSessions)",
                    icon: "number.circle.fill",
                    color: themeManager.currentTheme.accentColor.asColor
                )

                JourneyStatCard(
                    title: "Avg. Duration",
                    value: "\(viewModel.averageDuration) min",
                    icon: "clock.fill",
                    color: themeManager.currentTheme.accentColor.asColor
                )
            }

            HStack(spacing: 16) {
                JourneyStatCard(
                    title: "Consistency",
                    value: "\(Int(viewModel.consistencyRate * 100))%",
                    icon: "chart.line.uptrend.xyaxis",
                    color: themeManager.currentTheme.accentColor.asColor
                )

                JourneyStatCard(
                    title: "Next Milestone",
                    value: viewModel.nextMilestone,
                    icon: "star.fill",
                    color: themeManager.currentTheme.accentColor.asColor
                )
            }
        }
        .padding()
        .background(statsBackground)
    }

    private var statsBackground: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                            ]),
                            startPoint: .top, endPoint: .bottom
                        )
                )
                .background(RoundedRectangle(cornerRadius: 16).fill(.ultraThinMaterial))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    .white.opacity(0.5), .clear,
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.foreground.opacity(0.2),
                    radius: 15, x: 0, y: 5
                )
        }
    }

    private var startSessionButton: some View {
        Button(action: { viewModel.startSession() }) {
            HStack {
                Image(systemName: "play.fill")
                Text("Start New Session")
            }
            .font(.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        themeManager.currentTheme.accentColor.asColor,
                        themeManager.currentTheme.accentColor.asColor.opacity(0.8),
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .shadow(
                color: themeManager.currentTheme.accentColor.asColor.opacity(0.3),
                radius: 10, x: 0, y: 5
            )
        }
        .padding(.horizontal)
    }
}

struct NeuralPathwayVisualization: View {
    let progress: Double
    let color: Color

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background path
                Path { path in
                    path.move(to: CGPoint(x: 0, y: geometry.size.height / 2))
                    path.addCurve(
                        to: CGPoint(x: geometry.size.width, y: geometry.size.height / 2),
                        control1: CGPoint(x: geometry.size.width * 0.25, y: 0),
                        control2: CGPoint(x: geometry.size.width * 0.75, y: geometry.size.height)
                    )
                }
                .stroke(color.opacity(0.2), lineWidth: 4)

                // Progress path
                Path { path in
                    path.move(to: CGPoint(x: 0, y: geometry.size.height / 2))
                    path.addCurve(
                        to: CGPoint(x: geometry.size.width * progress, y: geometry.size.height / 2),
                        control1: CGPoint(x: geometry.size.width * 0.25, y: 0),
                        control2: CGPoint(x: geometry.size.width * 0.75, y: geometry.size.height)
                    )
                }
                .stroke(color, lineWidth: 4)
            }
        }
    }
}

struct NeuralPathwayProgressView: View {
    let progress: Double
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        NeuralPathwayVisualization(
            progress: progress,
            color: themeManager.currentTheme.accentColor.asColor
        )
        .overlay(
            VStack {
                Text("\(Int(progress * 100))%")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                Text("Neural Strength")
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
            }
        )
    }
}

// MARK: - Preview

#if DEBUG
    struct NeuralPathwayRestorationView_Previews: PreviewProvider {
        static var previews: some View {
            NeuralPathwayRestorationView(viewModel: NeuralPathwayRestorationViewModel.preview)
        }
    }
#endif

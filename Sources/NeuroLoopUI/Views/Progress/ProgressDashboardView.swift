import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

import SwiftUI

public struct ProgressDashboardView: View {
    @ObservedObject var viewModel: ProgressDashboardViewModel
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var selectedFilter: ProgressDashboardViewModel.Filter = .all
    @State private var searchText: String = ""
    @State private var showSubscriptionSheet = false
    @State private var debugOverlayEnabled = false
    @State private var redrawCount = 0
    @State private var peakMemoryUsage: UInt64 = 0
    @State private var isLoading: Bool = true

    private let premiumUnlocker = PremiumFeatureUnlocker(
        featureName: "Advanced Analytics", premiumService: PremiumService.shared)

    public init(viewModel: ProgressDashboardViewModel) {
        self.viewModel = viewModel
    }

    @ViewBuilder
    private func metricsSection() -> some View {
        // Key Metrics
        HStack(spacing: 16) {
            ProgressMetricCard(title: "Active", value: "\(viewModel.activeCount)")
                .accessibilityLabel("Active affirmations")
                .accessibilityValue("\(viewModel.activeCount)")
            ProgressMetricCard(title: "Completed", value: "\(viewModel.completedCount)")
                .accessibilityLabel("Completed affirmations")
                .accessibilityValue("\(viewModel.completedCount)")
            ProgressMetricCard(title: "Not Started", value: "\(viewModel.notStartedCount)")
                .accessibilityLabel("Not started affirmations")
                .accessibilityValue("\(viewModel.notStartedCount)")
        }
        .padding(.horizontal)

        // Streak Info
        HStack(spacing: 16) {
            ProgressMetricCard(title: "Current Streak", value: "\(viewModel.currentStreak)")
                .accessibilityLabel("Current streak")
                .accessibilityValue("\(viewModel.currentStreak) days")
            ProgressMetricCard(title: "Longest Streak", value: "\(viewModel.longestStreak)")
                .accessibilityLabel("Longest streak")
                .accessibilityValue("\(viewModel.longestStreak) days")
            ProgressMetricCard(
                title: "Avg. Consistency",
                value: String(format: "%.1f%%", viewModel.averageConsistency * 100)
            )
            .accessibilityLabel("Average consistency")
            .accessibilityValue(String(format: "%.1f percent", viewModel.averageConsistency * 100))
        }
        .padding(.horizontal)
    }

    @ViewBuilder
    private func analyticsSection() -> some View {
        premiumUnlocker.premiumFeatureView(
            .advancedAnalytics, onUpgrade: { showSubscriptionSheet = true }
        ) {
            if let chartData = viewModel.chartData {
                ProgressChartView(data: chartData)
                    .frame(height: 180)
                    .padding(.horizontal)
                    .accessibilityHidden(true)
            }
        }
        .sheet(isPresented: $showSubscriptionSheet) {
            SubscriptionView(viewModel: SubscriptionViewModel(premiumService: PremiumService()))
        }
    }

    @ViewBuilder
    private func filterSearchSection() -> some View {
        HStack {
            Picker("Filter", selection: $selectedFilter) {
                ForEach(ProgressDashboardViewModel.Filter.allCases, id: \.self) { filter in
                    Text(filter.displayName).tag(filter)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .onChange(of: selectedFilter) { oldValue, newValue in
                viewModel.applyFilter(newValue)
            }
            .accessibilityLabel("Filter affirmations")
            .accessibilityHint("Choose which affirmations to display")
            Spacer()
            TextField("Search", text: $searchText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .onChange(of: searchText) { oldValue, newValue in
                    viewModel.applySearch(newValue)
                }
                .frame(maxWidth: 180)
                .accessibilityLabel("Search affirmations")
                .accessibilityHint("Enter text to search affirmations")
        }
        .padding(.horizontal)
    }

    @ViewBuilder
    private func affirmationCardsSection() -> some View {
        LazyVStack(spacing: 16) {
            affirmationCardViews()
        }
        .padding(.horizontal)
    }

    @ViewBuilder
    private func affirmationCardViews() -> some View {
        ForEach(viewModel.filteredAffirmations, id: \.id) { affirmation in
            AffirmationCardRow(
                affirmation: affirmation, progress: viewModel.progress(for: affirmation))
        }
    }

    private struct AffirmationCardRow: View {
        let affirmation: any AffirmationProtocol
        let progress: ProgressInfo
        @EnvironmentObject private var viewModel: ProgressDashboardViewModel
        var body: some View {
            EquatableAffirmationProgressCard(affirmation: affirmation, progress: progress)
                .onTapGesture {
                    viewModel.navigateToAffirmationDetail(affirmation)
                }
                .accessibilityLabel("Affirmation: \(affirmation.text)")
                .accessibilityValue("Progress: \(Int(progress.cycleProgress * 100)) percent")
                .accessibilityHint("Double tap to view details")
        }
    }

    @ViewBuilder
    private func mainContentVStack() -> some View {
        VStack(spacing: 24) {
            // Overall Progress
            VStack(alignment: .leading, spacing: 8) {
                Text("Overall Progress")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
                    .accessibilityAddTraits(.isHeader)
                ProgressView(value: viewModel.overallCompletionRate) {
                    Text("\(Int(viewModel.overallCompletionRate * 100))% Complete")
                        .font(.headline)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
                }
                .progressViewStyle(
                    LinearProgressViewStyle(tint: themeManager.currentTheme.accentColor.asColor)
                )
                .accessibilityLabel("Overall completion rate")
                .accessibilityValue(
                    "\(Int(viewModel.overallCompletionRate * 100)) percent complete")
            }
            .padding()
            .background(
                ZStack {
                    // Glassmorphism effect
                    RoundedRectangle(cornerRadius: 16)
                        .fill(themeManager.currentTheme.cardBackgroundColor.asColor.opacity(0.7))
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(
                                    themeManager.currentTheme.cardBackgroundColor.asGradient
                                        ?? LinearGradient(
                                            gradient: Gradient(colors: [
                                                themeManager.currentTheme.cardBackgroundColor
                                                    .asColor
                                            ]), startPoint: .top, endPoint: .bottom),
                                    lineWidth: 1
                                )
                        )
                        .shadow(
                            color: themeManager.currentTheme.shadowColor.color.opacity(0.2),
                            radius: 15,
                            x: 0,
                            y: 5
                        )
                }
            )

            metricsSection()
            analyticsSection()
            filterSearchSection()
            affirmationCardsSection()
        }
        .padding(.vertical)
        .optimizePerformance()
    }

    public var body: some View {
        ZStack {
            // Background with gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    themeManager.currentTheme.backgroundColor.asColor,
                    themeManager.currentTheme.backgroundColor.asColor.opacity(0.8),
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            ScrollView {
                mainContentVStack()
            }
        }
        .navigationTitle("Progress")
        .onAppear {
            // Simulate loading for demo; replace with real async load if needed
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.7) {
                isLoading = false
            }
        }
    }
}

private struct ProgressMetricCard: View {
    let title: String
    let value: String
    @EnvironmentObject private var themeManager: ThemeManager
    var body: some View {
        VStack {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.accentColor.asColor)
            Text(title)
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            Group {
                if let gradient = themeManager.currentTheme.cardBackgroundColor.asGradient {
                    gradient
                } else {
                    themeManager.currentTheme.cardBackgroundColor.asColor
                }
            }
        )
        .cornerRadius(12)
        .accessibilityElement(children: .combine)
        .accessibilityLabel(title)
        .accessibilityValue(value)
    }
}

private struct ProgressChartView: View {
    let data: [Double]
    var body: some View {
        // Placeholder for a chart (replace with Swift Charts if available)
        GeometryReader { geometry in
            HStack(alignment: .bottom, spacing: 4) {
                ForEach(data.indices, id: \.self) { idx in
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.blue)  // TODO: Replace with theme color for accessibility
                        .frame(
                            width: (geometry.size.width / CGFloat(data.count)) - 4,
                            height: CGFloat(data[idx]) * geometry.size.height)
                }
            }
        }
        // TODO: Consider adding an accessibility summary for the chart if it conveys important information
    }
}

private struct AffirmationProgressCard: View {
    let affirmation: any AffirmationProtocol
    let progress: ProgressInfo
    @EnvironmentObject private var themeManager: ThemeManager
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            affirmationText
            progressBar
            progressStatus
        }
        .padding()
        .background(
            Group {
                if let gradient = themeManager.currentTheme.cardBackgroundColor.asGradient {
                    gradient
                } else {
                    themeManager.currentTheme.cardBackgroundColor.asColor
                }
            }
        )
        .cornerRadius(10)
        .shadow(
            color: themeManager.currentTheme.shadowColor.color.opacity(0.05), radius: 2, x: 0, y: 1
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Affirmation: \(affirmation.text)")
        .accessibilityValue(
            "Cycle progress: \(Int(progress.cycleProgress * 100)) percent. Day \(progress.currentDay) of \(progress.totalDays)"
        )
        .accessibilityHint(
            progress.isCycleComplete
                ? "Cycle complete"
                : (progress.hasActiveCycle ? "Cycle in progress" : "Cycle not started"))
    }

    private var affirmationText: some View {
        Text(affirmation.text)
            .font(.headline)
            .lineLimit(2)
            .foregroundColor(themeManager.currentTheme.primaryTextColor.color)
    }

    private var progressBar: some View {
        ProgressView(value: progress.cycleProgress) {
            Text("Cycle: \(Int(progress.cycleProgress * 100))%")
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
        }
        .progressViewStyle(
            LinearProgressViewStyle(tint: themeManager.currentTheme.successColor.color))
    }

    private var progressStatus: some View {
        HStack {
            Text("Day \(progress.currentDay) of \(progress.totalDays)")
                .font(.caption2)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
            Spacer()
            if progress.isCycleComplete {
                Text("Complete")
                    .font(.caption2)
                    .foregroundColor(themeManager.currentTheme.accentColor.asColor)
            } else if progress.hasActiveCycle {
                Text("In Progress")
                    .font(.caption2)
                    .foregroundColor(themeManager.currentTheme.warningColor.color)
            } else {
                Text("Not Started")
                    .font(.caption2)
                    .foregroundColor(themeManager.currentTheme.disabledColor.color)
            }
        }
    }
}

// Equatable wrapper for AffirmationProgressCard
private struct EquatableAffirmationProgressCard: View {
    let affirmation: any AffirmationProtocol
    let progress: ProgressInfo
    var body: some View {
        AffirmationProgressCard(affirmation: affirmation, progress: progress)
    }
}

// MARK: - Dashboard Skeleton View

private struct DashboardSkeletonView: View {
    var body: some View {
        VStack(spacing: 24) {
            // Progress bar skeleton
            SkeletonView(size: CGSize(width: 220, height: 24), cornerRadius: 12)
            // Metric cards skeleton
            HStack(spacing: 16) {
                ForEach(0..<3) { _ in
                    SkeletonView(size: CGSize(width: 90, height: 60), cornerRadius: 12)
                }
            }
            HStack(spacing: 16) {
                ForEach(0..<3) { _ in
                    SkeletonView(size: CGSize(width: 90, height: 60), cornerRadius: 12)
                }
            }
            // Chart skeleton
            SkeletonView(size: CGSize(width: 320, height: 120), cornerRadius: 16)
            // Filter/search skeleton
            SkeletonView(size: CGSize(width: 280, height: 32), cornerRadius: 8)
            // Affirmation cards skeleton
            ForEach(0..<3) { _ in
                SkeletonView(size: CGSize(width: 320, height: 56), cornerRadius: 12)
            }
        }
        .padding()
        .accessibilityHidden(true)
    }
}

#if DEBUG
    struct ProgressDashboardView_Previews: PreviewProvider {
        static var previews: some View {
            Text("Preview not available")
            // Temporarily disabled due to async initialization issues
        }
    }
#endif

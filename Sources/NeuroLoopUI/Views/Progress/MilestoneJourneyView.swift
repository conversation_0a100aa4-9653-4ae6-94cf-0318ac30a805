import NeuroLoopCore
// import NeuroLoopShared  // Remove if only haptics are used
import NeuroLoopTypes
import SwiftUI

#if os(macOS)
    import AppKit
#else
    import UIKit
#endif

public struct MilestoneJourneyView: View {
    @ObservedObject var viewModel: MilestoneJourneyViewModel
    @EnvironmentObject private var themeManager: ThemeManager
    @Namespace private var animation
    @State private var showSharingSheet: Bool = false
    @State private var sharingAchievement: Achievement? = nil

    public init(viewModel: MilestoneJourneyViewModel) {
        self.viewModel = viewModel
    }

    public var body: some View {
        ZStack {
            backgroundView
            mainContent
        }
        .navigationTitle("Milestone Journey")
        .alert(isPresented: $viewModel.showCelebration) {
            Alert(
                title: Text("🎉 Congratulations!"),
                message: Text(viewModel.congratulatoryMessage ?? "You've reached a new milestone!"),
                dismissButton: .default(Text("OK")))
        }
        .sheet(isPresented: $showSharingSheet) {
            if let achievement = sharingAchievement {
                AchievementSharingView(
                    viewModel: AchievementSharingViewModel(achievement: achievement.description))
            }
        }
    }

    private var backgroundView: some View {
        Group {
            if let gradient = themeManager.currentTheme.backgroundColor.asGradient {
                gradient.ignoresSafeArea()
            } else {
                themeManager.currentTheme.backgroundColor.asColor.ignoresSafeArea()
            }
        }
    }

    private var mainContent: some View {
        ScrollView {
            VStack(spacing: 24) {
                progressOverviewSection
                milestonesListSection
            }
            .padding()
        }
    }

    private var progressOverviewSection: some View {
        VStack(spacing: 16) {
            Text("Your Journey")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            HStack(spacing: 20) {
                JourneyStatCard(
                    title: "Milestones",
                    value: "\(viewModel.completedMilestones)/\(viewModel.totalMilestones)",
                    icon: "star.fill",
                    color: themeManager.currentTheme.accentColor.asColor
                )

                JourneyStatCard(
                    title: "Current Streak",
                    value: "\(viewModel.currentStreak) days",
                    icon: "flame.fill",
                    color: themeManager.currentTheme.accentColor.asColor
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                            ]),
                            startPoint: .top, endPoint: .bottom
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                    radius: 10, x: 0, y: 5)
        )
    }

    private var milestonesListSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Milestones")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            ForEach(viewModel.achievements) { milestone in
                MilestoneCard(
                    milestone: milestone,
                    isLocked: !viewModel.isMilestoneUnlocked(milestone),
                    onTap: { viewModel.selectMilestone(milestone) }
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                            ]),
                            startPoint: .top, endPoint: .bottom
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.1),
                    radius: 10, x: 0, y: 5)
        )
    }

    private var motivationalQuote: String? {
        // Placeholder: rotate through a few quotes
        [
            "Consistency is the key to success.",
            "Every repetition strengthens your neural pathways.",
            "Celebrate progress, not perfection.",
            "Persistence creates new possibilities.",
        ].randomElement()
    }
}

struct JourneyStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.headline)
                .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

            Text(title)
                .font(.caption)
                .foregroundColor(themeManager.currentTheme.secondaryTextColor.foreground)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    themeManager.currentTheme.cardBackgroundColor.asGradient
                        ?? LinearGradient(
                            gradient: Gradient(colors: [
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                                themeManager.currentTheme.cardBackgroundColor.asColor,
                            ]),
                            startPoint: .top, endPoint: .bottom
                        )
                )
                .shadow(
                    color: themeManager.currentTheme.shadowColor.color.opacity(0.1), radius: 10,
                    x: 0, y: 5)
        )
    }
}

struct MilestoneCard: View {
    let milestone: Achievement
    let isLocked: Bool
    let onTap: () -> Void
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(milestone.description)
                        .font(.headline)
                        .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                    if let value = milestone.value {
                        Text("\(value) \(milestone.type.rawValue)")
                            .font(.subheadline)
                            .foregroundColor(
                                themeManager.currentTheme.secondaryTextColor.foreground
                            )
                            .lineLimit(2)
                    }
                }

                Spacer()

                if isLocked {
                    Image(systemName: "lock.fill")
                        .foregroundColor(themeManager.currentTheme.secondaryTextColor.foreground)
                } else {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(themeManager.currentTheme.successColor.color)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.05), radius: 5,
                        x: 0, y: 2)
            )
        }
        .disabled(isLocked)
    }
}

private struct JourneyTimelineView: View {
    let achievements: [Achievement]
    let onTap: (Achievement) -> Void
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            ForEach(achievements) { achievement in
                HStack(alignment: .center, spacing: 16) {
                    Image(systemName: achievement.icon)
                        .resizable()
                        .frame(width: 36, height: 36)
                        .foregroundColor(achievement.isEarned ? .blue : .gray.opacity(0.4))
                        .scaleEffect(achievement.isEarned ? 1.1 : 1.0)
                        .animation(.spring(), value: achievement.isEarned)
                    VStack(alignment: .leading, spacing: 4) {
                        Text(achievement.description)
                            .font(.headline)
                        if let date = achievement.dateEarned, achievement.isEarned {
                            Text("Earned on \(date, formatter: dateFormatter)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        } else if !achievement.isEarned {
                            Text("Locked")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                    Spacer()
                    if achievement.isEarned {
                        Image(systemName: "checkmark.seal.fill")
                            .foregroundColor(.green)
                    }
                }
                .padding(.vertical, 8)
                .contentShape(Rectangle())
                .onTapGesture { onTap(achievement) }
            }
        }
    }
    private var dateFormatter: DateFormatter {
        let df = DateFormatter()
        df.dateStyle = .medium
        return df
    }
}

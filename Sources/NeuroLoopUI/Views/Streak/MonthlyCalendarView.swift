import NeuroLoopTypes
import Swift<PERSON>
#if os(iOS)
import UIKit
#endif

@available(iOS 17.0, macOS 14.0, *)
public struct MonthlyCalendarView: View {
    let streakDays: [StreakDay]
    @Binding var selectedMonth: Date

    public init(streakDays: [StreakDay], selectedMonth: Binding<Date>) {
        self.streakDays = streakDays
        self._selectedMonth = selectedMonth
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            monthSelector

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                ForEach(Calendar.current.shortWeekdaySymbols, id: \.self) { day in
                    Text(day)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                ForEach(daysInMonth.indices, id: \.self) { index in
                    if let date = daysInMonth[index] {
                        DayCell(
                            date: date,
                            streakDay: streakDay(for: date)
                        )
                    } else {
                        Color.clear
                    }
                }
            }
        }
        .padding()
        .background(Color(uiColor: UIColor.systemBackground))
        .cornerRadius(10)
        .shadow(radius: 2)
    }

    private var monthSelector: some View {
        HStack {
            Button(action: previousMonth) {
                Image(systemName: "chevron.left")
                    .foregroundColor(.primary)
            }

            Spacer()

            Text(selectedMonth.formatted(.dateTime.month().year()))
                .font(.headline)

            Spacer()

            Button(action: nextMonth) {
                Image(systemName: "chevron.right")
                    .foregroundColor(.primary)
            }
        }
    }

    private var daysInMonth: [Date?] {
        let calendar = Calendar.current
        let interval = calendar.dateInterval(of: .month, for: selectedMonth)!
        let firstWeekday = calendar.component(.weekday, from: interval.start)

        var days: [Date?] = Array(repeating: nil, count: firstWeekday - 1)

        let daysInMonth = calendar.range(of: .day, in: .month, for: selectedMonth)!.count

        for day in 1...daysInMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: interval.start) {
                days.append(date)
            }
        }

        // Pad the end of the month to complete the last week
        while days.count % 7 != 0 {
            days.append(nil)
        }

        return days
    }

    private func streakDay(for date: Date) -> StreakDay? {
        streakDays.first { Calendar.current.isDate($0.date, inSameDayAs: date) }
    }

    private func previousMonth() {
        if let newDate = Calendar.current.date(byAdding: .month, value: -1, to: selectedMonth) {
            selectedMonth = newDate
        }
    }

    private func nextMonth() {
        if let newDate = Calendar.current.date(byAdding: .month, value: 1, to: selectedMonth) {
            selectedMonth = newDate
        }
    }
}

private struct DayCell: View {
    let date: Date
    let streakDay: StreakDay?

    var body: some View {
        ZStack {
            Circle()
                .fill(backgroundColor)
                .frame(width: 32, height: 32)

            Text(date.formatted(.dateTime.day()))
                .font(.caption)
                .foregroundColor(textColor)
        }
    }

    private var backgroundColor: Color {
        if let day = streakDay {
            if day.isComplete {
                return .green
            } else if day.progress > 0 {
                return .yellow.opacity(day.progress)
            }
        }
        return .gray.opacity(0.1)
    }

    private var textColor: Color {
        if let day = streakDay, day.isComplete {
            return .white
        }
        return .primary
    }
}

// MARK: - Preview
@available(iOS 17.0, macOS 14.0, *)
struct MonthlyCalendarView_Previews: PreviewProvider {
    static var previews: some View {
        MonthlyCalendarView(
            streakDays: [
                StreakDay(
                    date: Date(),
                    progress: 1.0,
                    isComplete: true,
                    repetitions: 10,
                    isInCurrentMonth: true
                )
            ],
            selectedMonth: .constant(Date())
        )
    }
}

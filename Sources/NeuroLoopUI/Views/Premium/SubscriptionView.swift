import NeuroLoopCore
import StoreKit
import SwiftUI

// import PremiumFeedbackView

public struct SubscriptionView: View {
    @ObservedObject private var viewModel: SubscriptionViewModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: NeuroLoopCore.ThemeManager

    public init(viewModel: SubscriptionViewModel) {
        self.viewModel = viewModel
    }

    public var body: some View {
        ScrollView {
            mainContent
        }
        .overlay(overlayContent)
        .onAppear {
            Task {
                await viewModel.loadProducts()
            }
        }
    }

    private var mainContent: some View {
        VStack(spacing: 24) {
            // Header
            VStack(spacing: 8) {
                Text("Upgrade to Premium")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(themeManager.currentTheme.primaryTextColor.color)

                Text("Unlock all features and take your practice to the next level")
                    .font(.subheadline)
                    .foregroundColor(themeManager.currentTheme.secondaryTextColor.color)
                    .multilineTextAlignment(.center)
            }
            .padding(.top)

            // Features
            VStack(alignment: .leading, spacing: 15) {
                FeatureRow(
                    icon: "infinity",
                    title: "Unlimited Affirmations",
                    description: "Create as many affirmations as you need"
                )

                FeatureRow(
                    icon: "paintpalette",
                    title: "Custom Themes",
                    description: "Personalize your app with custom colors"
                )

                FeatureRow(
                    icon: "chart.bar",
                    title: "Advanced Analytics",
                    description: "Track your progress with detailed insights"
                )

                FeatureRow(
                    icon: "square.and.arrow.up",
                    title: "Data Export",
                    description: "Export your affirmations and progress"
                )
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        themeManager.currentTheme.cardBackgroundColor.asGradient
                            ?? LinearGradient(
                                gradient: Gradient(colors: [
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                    themeManager.currentTheme.cardBackgroundColor.asColor,
                                ]),
                                startPoint: .top, endPoint: .bottom
                            )
                    )
                    .shadow(
                        color: themeManager.currentTheme.shadowColor.color.opacity(0.2),
                        radius: 8, x: 0, y: 4)
            )

            // Subscription Options
            VStack(spacing: 15) {
                ForEach(viewModel.products) { product in
                    SubscriptionOptionView(
                        product: product,
                        isSelected: viewModel.selectedProductId == product.id,
                        action: { viewModel.selectProduct(product) }
                    )
                }
            }
            .padding()

            // Purchase Button
            Button(action: {
                Task {
                    await viewModel.purchase()
                }
            }) {
                Text(viewModel.isPurchasing ? "Processing..." : "Subscribe Now")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(viewModel.isPurchasing ? Color.gray : Color.blue)
                    )
            }
            .disabled(viewModel.isPurchasing || viewModel.selectedProductId == nil)
            .padding()
            .accessibilityLabel("Subscribe to Premium")
            .accessibilityIdentifier("SubscribeButton")

            // Restore Purchases
            Button(action: {
                Task {
                    await viewModel.restorePurchases()
                }
            }) {
                Text("Restore Purchases")
                    .font(.subheadline)
                    .foregroundColor(.blue)
            }
            .padding(.bottom)
            .accessibilityLabel("Restore Purchases")
            .accessibilityIdentifier("RestorePurchasesButton")

            // Terms and Privacy
            VStack(spacing: 5) {
                Text("By subscribing, you agree to our")
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack(spacing: 5) {
                    Link(
                        "Terms of Service",
                        destination: URL(string: "https://example.com/terms")!)
                    Text("and")
                    Link(
                        "Privacy Policy",
                        destination: URL(string: "https://example.com/privacy")!)
                }
                .font(.caption)
            }
        }
        .padding()
    }

    private var overlayContent: some View {
        Group {
            if viewModel.isLoading {
                LoadingView(message: "Loading...")
            }

            if let error = viewModel.error {
                PremiumFeedbackView(
                    type: .error(message: error.localizedDescription),
                    dismissAction: { viewModel.dismissError() }
                )
            }
            if viewModel.purchaseSuccess {
                PremiumFeedbackView(
                    type: .success(
                        message: "Your purchase was successful! Enjoy premium features."),
                    dismissAction: { viewModel.dismissSuccess() }
                )
            }
            if viewModel.restoreSuccess {
                PremiumFeedbackView(
                    type: .success(message: "Your purchases have been restored!"),
                    dismissAction: { viewModel.dismissSuccess() }
                )
            }
            if viewModel.didBecomePremium {
                PremiumFeedbackView(
                    type: .success(message: "Congratulations! You are now a Premium user. 🎉"),
                    dismissAction: { viewModel.dismissSuccess() }
                )
            }
        }
    }
}

private struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
    }
}

private struct SubscriptionOptionView: View {
    let product: Product
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.displayName)
                        .font(.headline)

                    Text(product.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Text(product.displayPrice)
                    .font(.headline)

                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .blue : .gray)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("Subscription option: \(product.displayName), \(product.displayPrice)")
        .accessibilityIdentifier("SubscriptionOption_\(product.id)")
    }
}

// MARK: - Preview

struct SubscriptionView_Previews: PreviewProvider {
    static var previews: some View {
        SubscriptionView(
            viewModel: SubscriptionViewModel(
                premiumService: MockPremiumService()
            )
        )
    }
}

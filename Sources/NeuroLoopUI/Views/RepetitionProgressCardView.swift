import SwiftUI
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
import Combine

/// A simplified version of the RepetitionProgressCardView that is more reliable
/// and easier to maintain. This view displays the current repetition count and
/// progress towards the daily goal.
public struct RepetitionProgressCardView: View {
    // MARK: - Properties

    // View model for tracking repetition progress
    @ObservedObject var viewModel: RepetitionTrackingViewModel

    // Animation state
    @State private var animateProgress = false

    // State to force refresh when count changes
    @State private var forceRefreshID = UUID()

    // Callback for tap actions
    private let onTap: () -> Void

    // Direct count tracking for immediate UI updates
    @State private var displayCount: Int = 0

    /// Primary initializer that takes a view model
    public init(
        viewModel: RepetitionTrackingViewModel,
        onTap: @escaping () -> Void
    ) {
        self.viewModel = viewModel
        self.onTap = onTap
    }

    /// Legacy initializer for backward compatibility - DEPRECATED
    /// This initializer creates a disconnected view model and should not be used in production
    public init(
        currentCount: Int,
        totalCount: Int,
        currentDay: Int,
        totalDays: Int,
        affirmationId: UUID? = nil,
        onTap: @escaping () -> Void
    ) {
        print("⚠️ WARNING: Using deprecated RepetitionProgressCardView initializer")
        print("⚠️ This creates a disconnected view model that won't update properly")
        print("⚠️ Please use the primary initializer with a proper RepetitionTrackingViewModel")

        // Create a stub affirmation
        let stubAffirmation = AffirmationStub(
            text: "I am confident and capable in everything I do",
            category: .health
        )

        // Use the shared preview instance
        let tempViewModel = RepetitionTrackingViewModel.preview

        // Set the values manually
        tempViewModel.updateRepetitionCount(currentCount)

        self.viewModel = tempViewModel
        self.onTap = onTap
    }

    public var body: some View {
        // Always use the latest count from the view model
        let currentCount = viewModel.todayRepetitions

        ZStack(alignment: .top) {
            // Card background
            RoundedRectangle(cornerRadius: 24, style: .continuous)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.blue.opacity(0.85), Color.blue.opacity(0.65),
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.black.opacity(0.3), radius: 15, x: 0, y: 8)

            VStack(spacing: 16) {
                // Title
                Text("Repetition Progress")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
                    .padding(.top, 12)

                // Hidden for cleaner UI
                // Text("Count: \(displayCount)/100")
                //     .font(.system(size: 14))
                //     .fontWeight(.bold)
                //     .foregroundColor(.white)
                //     .padding(.horizontal, 8)
                //     .padding(.vertical, 2)
                //     .background(Color.black.opacity(0.3))
                //     .cornerRadius(4)

                // Progress Counter
                RepetitionCounterView(
                    currentCount: displayCount,
                    totalCount: 100,
                    onTap: {
                        // Provide haptic feedback
                        #if os(iOS)
                        let generator = UIImpactFeedbackGenerator(style: .medium)
                        generator.impactOccurred()
                        #endif
                        onTap()
                    }
                )
                .padding(.vertical, 4)
                .frame(height: 90)
                .frame(maxWidth: .infinity)
                .scaleEffect(animateProgress ? 1.05 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: animateProgress)

                // Text showing count out of total
                Text("\(displayCount) out of 100")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))
                    .padding(.top, 4)
                    .padding(.horizontal, 8)

                Spacer(minLength: 12)
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)

            // Streak badge
            HStack(spacing: 4) {
                Image(systemName: "flame.fill")
                    .foregroundColor(.orange)
                    .font(.system(size: 8))
                Text("Day \(viewModel.currentDay) of 7")
                    .font(.system(size: 8))
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.white.opacity(0.18))
            .clipShape(Capsule())
            .overlay(
                Capsule().stroke(Color.white.opacity(0.25), lineWidth: 0.5)
            )
            .padding([.bottom, .trailing], 8)
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottomTrailing)
        }
        .frame(width: 180, height: 200)
        .padding(.vertical, 6)
        .id("progress-card-\(forceRefreshID)")
        .onReceive(viewModel.$todayRepetitions) { newCount in
            // Update the display count whenever the view model's count changes

            // Only animate if the count increased
            if newCount > displayCount {
                withAnimation {
                    animateProgress = true
                }

                // Reset animation after a delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    animateProgress = false
                }
            }

            // Update the display count
            displayCount = newCount

            // Force refresh the view
            forceRefreshID = UUID()
        }
        // Listen for notifications about count changes
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RepetitionCountChanged"))) { notification in
            // Extract count from notification
            if let userInfo = notification.userInfo,
               let count = userInfo["count"] as? Int {

                // Only update if the count is different from what we know
                if count != displayCount {
                    // Update immediately to ensure the UI reflects the change
                    DispatchQueue.main.async {
                        // Only animate if the count increased
                        if count > displayCount {
                            withAnimation {
                                animateProgress = true
                            }

                            // Reset animation after a delay
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                animateProgress = false
                            }
                        }

                        // Update the display count
                        displayCount = count

                        // Update the view model to keep everything in sync
                        viewModel.updateRepetitionCount(count)

                        // Force refresh the view
                        forceRefreshID = UUID()
                    }
                }
            }
        }
        .onAppear {
            // Initialize display count from view model
            displayCount = viewModel.todayRepetitions

            // Force view model to load data
            Task {
                await viewModel.loadData()

                // Update display count after data load
                DispatchQueue.main.async {
                    displayCount = viewModel.todayRepetitions
                    forceRefreshID = UUID()
                }
            }
        }
    }
}

#Preview {
    ZStack {
        Color.blue.opacity(0.3).edgesIgnoringSafeArea(.all)
        VStack {
            // Use the legacy initializer for preview
            RepetitionProgressCardView(
                currentCount: 5,
                totalCount: 100,
                currentDay: 1,
                totalDays: 7,
                onTap: {}
            )

            // Show an affirmation card below to demonstrate layout
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.8))
                .frame(height: 120)
                .padding(.horizontal)
                .overlay(
                    Text("I am confident and capable in everything I do")
                        .font(.headline)
                        .multilineTextAlignment(.center)
                        .padding()
                )
        }
    }
}

import AVFoundation
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes
import SwiftUI

#if os(iOS) || os(tvOS)
    import UIKit
#elseif os(macOS)
    import AppKit
#endif

/// A view for speaking and recording affirmations with a modern UI
/// This version uses the FixedSpeakAffirmationViewModel to address repetition counter issues
@available(iOS 17.0, macOS 14.0, *)
public struct FixedSpeakAffirmationView: View {
    // MARK: - Properties

    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: ThemeManager
    @StateObject private var viewModel: FixedSpeakAffirmationViewModel
    @StateObject private var trackingViewModel: RepetitionTrackingViewModel
    @State private var showingConfirmation = false
    @State private var forceRefreshID = UUID()  // Used to force view refresh
    @State private var progressCardId = UUID()  // Used to force progress card refresh

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: any RepetitionServiceProtocol,
        affirmationService: any AffirmationServiceProtocol
    ) {
        // Create the main view model
        let speakViewModel = FixedSpeakAffirmationViewModel(
            affirmation: affirmation,
            repetitionService: repetitionService,
            audioService: Self.createAudioService(),
            affirmationService: affirmationService
        )

        // Create the tracking view model
        let streakService: StreakServiceProtocol
        do {
            streakService = try ServiceFactory.shared.getStreakService()
        } catch {
            // Fallback to a preview service if the real one fails
            print("Error getting streak service: \(error.localizedDescription)")
            streakService = PreviewServiceFactory.shared.getStreakService()
        }

        let repTrackingViewModel = RepetitionTrackingViewModel(
            affirmationService: affirmationService,
            repetitionService: repetitionService,
            streakService: streakService,
            audioService: Self.createAudioService(),
            affirmation: affirmation
        )

        // Set the tracking view model in the main view model
        speakViewModel.trackingViewModel = repTrackingViewModel

        // Initialize the state objects
        _viewModel = StateObject(wrappedValue: speakViewModel)
        _trackingViewModel = StateObject(wrappedValue: repTrackingViewModel)
    }

    private static func createAudioService() -> AudioRecordingServiceProtocol {
        #if os(iOS)
            return AudioRecordingService(audioFileManager: AudioFileManager.shared)
        #else
            return AudioRecordingService()
        #endif
    }

    // MARK: - Helper Methods

    // Function to determine what message to display in the status box
    private func getStatusMessage() -> String {
        if viewModel.isRecording {
            if !viewModel.partialRecognitionText.isEmpty {
                return viewModel.partialRecognitionText
            } else {
                return "Listening..."
            }
        } else {
            return "Tap to start recording"
        }
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            // Background
            BlueBackgroundGradient()
                .ignoresSafeArea()

            VStack(spacing: 20) {
                // Top bar with close button
                HStack {
                    Button(action: {
                        showingConfirmation = true
                    }) {
                        Image(systemName: "xmark")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding(10)
                            .background(Circle().fill(Color.black.opacity(0.3)))
                    }
                    .padding(.leading)

                    Spacer()

                    // Debug mode toggle (only in development builds)
                    #if DEBUG
                        VStack(alignment: .leading, spacing: 2) {
                            Toggle(
                                "Testing Mode: \(viewModel.debugBypassSpeechRecognition ? "ON" : "OFF")",
                                isOn: $viewModel.debugBypassSpeechRecognition
                            )
                            .toggleStyle(SwitchToggleStyle(tint: Color.blue))
                            .padding(.horizontal)

                            if viewModel.debugBypassSpeechRecognition {
                                Text("Testing mode bypasses speech verification")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                    .padding(.horizontal)

                                // Add direct test buttons
                                HStack {
                                    // Direct Test +1 button using shared counter
                                    Button(action: {
                                        print(
                                            "🧪 DIRECT TEST: Manually triggering repetition increment using shared counter"
                                        )
                                        print(
                                            "🧪 Current count before increment: VM=\(viewModel.todayRepetitions), Tracking=\(trackingViewModel.todayRepetitions)"
                                        )

                                        Task {
                                            // Get the debug repetition service
                                            let factory = ServiceFactory.shared
                                            let debugService = try factory.getRepetitionServiceForMode(debugMode: true) as? DebugRepetitionService
                                            if let debugService = debugService {
                                                // Use the debug service to record a repetition
                                                let result = try await debugService.recordRepetition(for: viewModel.affirmation)
                                                let newCount = result.updatedAffirmation.currentRepetitions
                                                print(
                                                    "🧪 Incremented count to \(newCount) using debug service"
                                                )

                                                // Update both view models
                                                await MainActor.run {
                                                    viewModel.todayRepetitions = newCount
                                                    trackingViewModel.updateRepetitionCount(newCount)

                                                    print(
                                                        "🧪 Updated counts: VM=\(viewModel.todayRepetitions), Tracking=\(trackingViewModel.todayRepetitions)"
                                                    )

                                                    // Force UI refresh
                                                    forceRefreshID = UUID()
                                                    progressCardId = UUID()
                                                    print("🧪 Forced UI refresh")
                                                }
                                            }
                                        }
                                    }) {
                                        Text("Shared Counter +1")
                                            .font(.caption)
                                            .padding(6)
                                            .background(Color.green)
                                            .foregroundColor(.white)
                                            .cornerRadius(4)
                                    }

                                    // Reset counter button
                                    Button(action: {
                                        print("🧪 DIRECT TEST: Resetting repetition count")

                                        Task {
                                            // Get the debug repetition service
                                            let factory = ServiceFactory.shared
                                            if let debugService = try factory.getRepetitionServiceForMode(debugMode: true) as? DebugRepetitionService {
                                                // Reset the count using the debug service
                                                debugService.resetCount(for: viewModel.affirmation.id)
                                                print("🧪 Reset count using debug service")

                                                // Update both view models
                                                await MainActor.run {
                                                    viewModel.todayRepetitions = 0
                                                    trackingViewModel.updateRepetitionCount(0)

                                                    print(
                                                        "🧪 Reset counts: VM=\(viewModel.todayRepetitions), Tracking=\(trackingViewModel.todayRepetitions)"
                                                    )

                                                    // Force UI refresh
                                                    forceRefreshID = UUID()
                                                    progressCardId = UUID()
                                                    print("🧪 Forced UI refresh")
                                                }
                                            }
                                        }
                                    }) {
                                        Text("Reset Count")
                                            .font(.caption)
                                            .padding(6)
                                            .background(Color.red)
                                            .foregroundColor(.white)
                                            .cornerRadius(4)
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                        .background(
                            RoundedRectangle(cornerRadius: 8).fill(Color.black.opacity(0.2))
                        )
                        .onChange(of: viewModel.debugBypassSpeechRecognition) {
                            oldValue, newValue in
                            print("FixedSpeakAffirmationView: Testing mode changed to \(newValue)")

                            // Update the tracking view model with the appropriate service
                            Task {
                                let factory = ServiceFactory.shared
                                do {
                                    // Get the appropriate repetition service based on the new debug mode
                                    let service = try factory.getRepetitionServiceForMode(debugMode: newValue)
                                    print(
                                        "FixedSpeakAffirmationView: Got \(newValue ? "debug" : "standard") repetition service: \(type(of: service))"
                                    )

                                    // Update the tracking view model
                                    trackingViewModel.updateRepetitionService(service)
                                    print(
                                        "FixedSpeakAffirmationView: Updated tracking view model with \(newValue ? "debug" : "standard") service"
                                    )
                                } catch {
                                    print(
                                        "FixedSpeakAffirmationView: Error getting repetition service: \(error)"
                                    )
                                }
                            }
                        }
                    #endif
                }
                .padding(.top, 10)

                Spacer(minLength: 40)

                // Affirmation card
                AffirmationTextView(
                    text: viewModel.affirmation.text,
                    showRepeatExactly: true,
                    onRepeatExactly: {
                        // Use playRecording method
                        Task {
                            await viewModel.playRecording()
                        }
                    },
                    isRecording: viewModel.isRecording
                )
                .padding()
                .blueCard()
                .padding(.horizontal)

                // Fixed spacing instead of flexible Spacer
                Spacer().frame(height: 30)

                // Repetition progress - always visible but with opacity change during recording
                RepetitionProgressCardView(
                    viewModel: trackingViewModel,
                    onTap: {
                        // Optionally, you can trigger a haptic or other action here
                        print(
                            "RepetitionProgressCardView tapped, current count: \(trackingViewModel.todayRepetitions) for affirmation \(viewModel.affirmation.id)"
                        )

                        // Provide haptic feedback
                        #if os(iOS)
                            let generator = UIImpactFeedbackGenerator(style: .medium)
                            generator.impactOccurred()
                        #endif

                        Task {
                            // Force increment the repetition count
                            print(
                                "FixedSpeakAffirmationView: Progress card tapped, incrementing count"
                            )
                            await viewModel.forceIncrementRepetition()

                            // Update the tracking view model
                            trackingViewModel.updateRepetitionCount(viewModel.todayRepetitions)
                            print(
                                "FixedSpeakAffirmationView: Updated tracking view model count to \(viewModel.todayRepetitions)"
                            )

                            // Force UI refresh with a delay to ensure the count update has propagated
                            try? await Task.sleep(nanoseconds: 500_000_000)  // 0.5 second delay

                            await MainActor.run {
                                // Post a notification to ensure all observers are updated
                                NotificationCenter.default.post(
                                    name: Notification.Name("RepetitionCountChanged"),
                                    object: nil,
                                    userInfo: [
                                        "affirmationId": viewModel.affirmation.id,
                                        "count": viewModel.todayRepetitions,
                                        "timestamp": Date().timeIntervalSince1970,
                                        "uuid": UUID().uuidString,
                                    ]
                                )
                                print(
                                    "FixedSpeakAffirmationView: Posted RepetitionCountChanged notification"
                                )

                                // Post another notification after a delay to ensure it's received
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                    NotificationCenter.default.post(
                                        name: Notification.Name("RepetitionCountChanged"),
                                        object: nil,
                                        userInfo: [
                                            "affirmationId": viewModel.affirmation.id,
                                            "count": viewModel.todayRepetitions,
                                            "timestamp": Date().timeIntervalSince1970,
                                            "uuid": UUID().uuidString,
                                        ]
                                    )
                                    print(
                                        "FixedSpeakAffirmationView: Posted delayed RepetitionCountChanged notification"
                                    )
                                }

                                // Force UI refresh
                                forceRefreshID = UUID()
                                progressCardId = UUID()
                                print(
                                    "FixedSpeakAffirmationView: Forced UI refresh after increment")
                            }
                        }
                    }
                )
                // Force view to update when count changes with a deterministic ID
                .id("progress-card-\(trackingViewModel.todayRepetitions)")
                // Reduce opacity during recording but keep visible
                .opacity(viewModel.isRecording ? 0.6 : 1.0)

                // Fixed spacing instead of flexible Spacer
                Spacer().frame(height: 30)

                // Recording button and status
                VStack(spacing: 15) {
                    // Simplified message text without background box
                    Text(getStatusMessage())
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .fixedSize(horizontal: false, vertical: true)
                        .frame(minWidth: 200, minHeight: 36)

                    // Debug controls hidden for cleaner UI
                    #if DEBUG
                        // Hidden debug controls - but we still have a hidden button for testing
                        Button(action: {
                            Task {
                                print("Hidden force increment button tapped")
                                await viewModel.forceIncrementRepetition()
                            }
                        }) {
                            Text("")
                                .frame(width: 0, height: 0)
                                .opacity(0)
                        }
                    #endif

                    // Recording button
                    ZStack {
                        Button(action: {
                            Task {
                                print(
                                    "🎤 Record button tapped, current state: \(viewModel.isRecording ? "recording" : "not recording")"
                                )

                                if viewModel.isRecording {
                                    // Stop recording if already recording
                                    print("🎤 Stopping recording...")
                                    await viewModel.stopRecording()

                                    // Add a delay to ensure all processing is complete
                                    try? await Task.sleep(nanoseconds: 800_000_000)  // 0.8 second delay

                                    // Check if we're in debug mode
                                    if viewModel.debugBypassSpeechRecognition {
                                        // In debug mode, we should manually increment the repetition count
                                        print("🎤 Debug mode is enabled, forcing increment...")
                                        await viewModel.forceIncrementRepetition()
                                    } else {
                                        // In normal mode, the stopRecording method should handle verification
                                        // and increment the count if the speech was recognized correctly
                                        print(
                                            "🎤 Normal mode - speech verification should have happened in stopRecording"
                                        )
                                    }

                                    // Update the tracking view model first
                                    trackingViewModel.updateRepetitionCount(
                                        viewModel.todayRepetitions)
                                    print(
                                        "🎤 Updated tracking view model count to \(viewModel.todayRepetitions)"
                                    )

                                    // Post a notification to ensure all observers are updated
                                    NotificationCenter.default.post(
                                        name: Notification.Name("RepetitionCountChanged"),
                                        object: nil,
                                        userInfo: [
                                            "affirmationId": viewModel.affirmation.id,
                                            "count": viewModel.todayRepetitions,
                                            "timestamp": Date().timeIntervalSince1970,
                                            "uuid": UUID().uuidString,
                                        ]
                                    )
                                    print(
                                        "🎤 Posted RepetitionCountChanged notification after recording"
                                    )

                                    // Post another notification after a delay to ensure it's received
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                        NotificationCenter.default.post(
                                            name: Notification.Name("RepetitionCountChanged"),
                                            object: nil,
                                            userInfo: [
                                                "affirmationId": viewModel.affirmation.id,
                                                "count": viewModel.todayRepetitions,
                                                "timestamp": Date().timeIntervalSince1970,
                                                "uuid": UUID().uuidString,
                                            ]
                                        )
                                        print(
                                            "🎤 Posted delayed RepetitionCountChanged notification")
                                    }

                                    // Force refresh the progress card and entire view
                                    await MainActor.run {
                                        progressCardId = UUID()
                                        forceRefreshID = UUID()
                                        print("🎤 Forced refresh after recording action")
                                    }
                                } else {
                                    // Start recording if not recording
                                    print("🎤 Starting recording...")
                                    viewModel.startRecording()
                                }
                            }
                        }) {
                            ZStack {
                                // Outer ring
                                Circle()
                                    .stroke(Color.blue.opacity(0.7), lineWidth: 4)
                                    .frame(width: 80, height: 80)

                                // Button background
                                Circle()
                                    .fill(viewModel.isRecording ? Color.red : Color.white)
                                    .frame(width: 70, height: 70)
                                    .shadow(radius: 5)

                                // Button icon
                                if viewModel.isRecording {
                                    // Stop icon (square)
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.white)
                                        .frame(width: 20, height: 20)
                                } else {
                                    // Record icon (red circle)
                                    Circle()
                                        .fill(Color.red)
                                        .frame(width: 60, height: 60)
                                }
                            }
                        }

                        // No overlay needed - removed
                    }

                }
                .padding(.bottom, 30)
            }
            .padding()

            // Alert for confirmation
            .alert("Exit Practice?", isPresented: $showingConfirmation) {
                Button("Cancel", role: .cancel) {}
                Button("Exit", role: .destructive) {
                    dismiss()
                }
            } message: {
                Text("Your progress for this session will be saved.")
            }

            // Alert for errors or success messages
            .alert(item: $viewModel.alertItem) { (alertItem: AlertItem) in
                Alert(
                    title: Text(alertItem.title),
                    message: Text(alertItem.message),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
        .onAppear {
            // Debug mode should be disabled by default for production builds
            viewModel.debugBypassSpeechRecognition = false
            print("📱 VIEW: Debug mode DISABLED for production")
            print("📱 Speech verification is required for counting repetitions")
            print("📱 Use the record button to start speech recognition")

            // Reset any existing repetition count to ensure a clean start
            Task {
                let factory = ServiceFactory.shared
                if let debugService = try factory.getRepetitionServiceForMode(debugMode: true) as? DebugRepetitionService {
                    // Reset the count using the debug service
                    debugService.resetCount(for: viewModel.affirmation.id)
                    print("📱 VIEW: Reset repetition count on app launch")

                    // Update both view models
                    await MainActor.run {
                        viewModel.todayRepetitions = 0
                        trackingViewModel.updateRepetitionCount(0)
                        print("📱 VIEW: Reset view model counts to 0")
                    }
                }
            }

            // Force UI refresh to ensure debug mode setting is properly applied
            forceRefreshID = UUID()
            progressCardId = UUID()

            // Initialize the view models
            Task {
                // Initialize the main view model
                await viewModel.initialize()

                // Get the appropriate repetition service based on debug mode
                let factory = ServiceFactory.shared
                do {
                    // Get the repetition service based on current debug mode setting
                    let service = try factory.getRepetitionServiceForMode(
                        debugMode: viewModel.debugBypassSpeechRecognition)
                    print("📱 VIEW: Got repetition service: \(type(of: service))")

                    // Update the tracking view model with the service
                    trackingViewModel.updateRepetitionService(service)
                    print("📱 VIEW: Updated tracking view model with appropriate service")
                } catch {
                    print("📱 VIEW: Error getting repetition service: \(error)")
                }

                // Initialize the tracking view model
                await trackingViewModel.loadData()

                print("📱 VIEW: Initialized both view models")
                print("📱 Main viewModel.todayRepetitions: \(viewModel.todayRepetitions)")
                print("📱 TrackingViewModel.todayRepetitions: \(trackingViewModel.todayRepetitions)")

                // Force refresh the view to ensure it reflects the current state
                DispatchQueue.main.async {
                    forceRefreshID = UUID()
                    progressCardId = UUID()
                    print("📱 Generated new IDs after initialization")
                }
            }
        }
        .onDisappear {
            // Clean up resources when the view disappears
            print("📱 VIEW: onDisappear called - cleaning up resources")

            // Clean up view model resources
            Task {
                await viewModel.cleanup()
                print("📱 Called viewModel.cleanup()")
            }

            // Log final state
            print("📱 Final state - todayRepetitions: \(viewModel.todayRepetitions)")
            print(
                "📱 Final state - trackingViewModel.todayRepetitions: \(trackingViewModel.todayRepetitions)"
            )
        }
        .id(forceRefreshID)  // Force the entire view to refresh when this changes
    }
}

// MARK: - Preview

@available(iOS 17.0, macOS 14.0, *)
struct FixedSpeakAffirmationView_Previews: PreviewProvider {
    static var previews: some View {
        FixedSpeakAffirmationView(
            affirmation: AffirmationStub(
                text: "I am confident and capable in everything I do",
                category: .health
            ),
            repetitionService: FullMockRepetitionService(),
            affirmationService: MockAffirmationService()
        )
        .environmentObject(ThemeManager.shared)
    }
}

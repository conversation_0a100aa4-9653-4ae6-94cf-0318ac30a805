import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/// A stub implementation of the AffirmationProtocol for UI development and testing
@available(iOS 17.0, macOS 14.0, *)
public struct AffirmationStub: AffirmationProtocol, Identifiable, Equatable {
    // MARK: - Properties

    public let id: UUID
    public var text: String
    public var category: AffirmationCategory
    public var recordingURL: URL?
    public var createdAt: Date
    public var updatedAt: Date
    public var currentCycleDay: Int
    public var cycleStartDate: Date?
    public var completedCycles: Int
    public var currentRepetitions: Int
    public var dailyProgress: [Date: Int]
    public var lastRepetitionDate: Date?
    public var energyLevel: Double
    public var moodRating: Int?
    public var notes: String?
    public var isFavorite: Bool
    public var playCount: Int
    public var hasActiveCycle: Bool
    public var isCurrentCycleComplete: Bool
    public var todayProgress: Double
    public var cycleProgress: Double
    public var hasTodayQuotaMet: Bool
    public var longestStreak: Int

    // MARK: - Computed Properties

    public var hasRecording: Bool {
        recordingURL != nil
    }

    public var canPerformRepetition: Bool {
        !hasTodayQuotaMet && hasActiveCycle
    }

    // MARK: - Initialization

    public init(
        id: UUID = UUID(),
        text: String,
        category: AffirmationCategory,
        recordingURL: URL? = nil,
        isFavorite: Bool = false,
        todayProgress: Double = 0.0,
        cycleProgress: Double = 0.0,
        longestStreak: Int = 0
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.recordingURL = recordingURL
        self.createdAt = Date()
        self.updatedAt = Date()
        self.currentCycleDay = 1
        self.cycleStartDate = Date()
        self.completedCycles = 0

        // Calculate the repetition count from todayProgress
        // IMPORTANT: Using floor() instead of round() to prevent rounding issues
        // This ensures we get exact integer increments and prevents jumps by 5
        self.currentRepetitions = Int(floor(todayProgress * 100.0))

        self.dailyProgress = [:]
        self.lastRepetitionDate = nil
        self.energyLevel = 0.5
        self.moodRating = nil
        self.notes = nil
        self.isFavorite = isFavorite
        self.playCount = 0
        self.hasActiveCycle = true
        self.isCurrentCycleComplete = false
        self.todayProgress = todayProgress
        self.cycleProgress = cycleProgress
        self.hasTodayQuotaMet = todayProgress >= 1.0

        // Set up daily progress for today
        let today = Calendar.current.startOfDay(for: Date())
        // Use the same floor() method for consistency with currentRepetitions
        self.dailyProgress[today] = Int(floor(todayProgress * 100.0))

        // Set the longest streak
        self.longestStreak = longestStreak
    }

    // MARK: - AffirmationProtocol Methods

    public func recordRepetition() throws {
        // This is a stub implementation, so it doesn't actually do anything
    }

    public func updateEnergyLevel(_ level: Double) {
        // This is a stub implementation, so it doesn't actually do anything
    }

    public func recordMood(_ rating: Int, notes: String?) {
        // This is a stub implementation, so it doesn't actually do anything
    }

    // MARK: - Equatable

    public static func == (lhs: AffirmationStub, rhs: AffirmationStub) -> Bool {
        lhs.id == rhs.id
    }
}

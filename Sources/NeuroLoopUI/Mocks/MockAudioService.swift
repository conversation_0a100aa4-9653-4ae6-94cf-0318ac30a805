import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import Combine

/// A mock implementation of the AudioRecordingServiceProtocol for UI development and testing
@available(iOS 17.0, macOS 14.0, *)
public class MockAudioService: AudioRecordingServiceProtocol {
    // MARK: - Properties

    @Published public var isRecording = false
    @Published public var recordingTime: TimeInterval = 0
    @Published public var recordingPower: Double = 0
    @Published public var recordingURL: URL? = nil
    @Published public var isPlaying = false
    @Published public var playbackProgress: Double = 0
    @Published public var playbackTime: TimeInterval = 0
    @Published public var error: Error? = nil

    // Private properties for recording simulation
    private var recordingStartTime: Date? = nil
    private var recordingTimer: Timer? = nil

    // MARK: - Publishers

    public var isRecordingPublisher: Published<Bool>.Publisher { $isRecording }
    public var recordingTimePublisher: Published<TimeInterval>.Publisher { $recordingTime }
    public var recordingPowerPublisher: Published<Double>.Publisher { $recordingPower }
    public var recordingURLPublisher: Published<URL?>.Publisher { $recordingURL }
    public var isPlayingPublisher: Published<Bool>.Publisher { $isPlaying }
    public var playbackProgressPublisher: Published<Double>.Publisher { $playbackProgress }
    public var playbackTimePublisher: Published<TimeInterval>.Publisher { $playbackTime }
    public var errorPublisher: Published<Error?>.Publisher { $error }

    // MARK: - Initialization

    public init() {}

    // MARK: - AudioRecordingServiceProtocol Methods

    public func startRecording() async throws {
        isRecording = true
        recordingTime = 0
        recordingStartTime = Date()

        // Start a timer to simulate recording time
        recordingTimer?.invalidate()

        // Use a simple approach that doesn't require a timer for the mock
        // This avoids actor isolation issues with the timer callback
        // In a real implementation, we would use MainActor.run inside the timer callback

        // Instead of a timer, we'll just set a fixed recording time when stopRecording is called
        // This is sufficient for our testing purposes
    }

    public func stopRecording() async throws -> URL {
        // Stop the timer if it exists
        recordingTimer?.invalidate()
        recordingTimer = nil

        // Set a fixed recording time based on how long the recording button was held
        // For testing purposes:
        // - If held for less than 1 second, set to 0.5 seconds (too short)
        // - If held for 1+ seconds, set to 3.0 seconds (long enough)
        if let startTime = recordingStartTime {
            let actualDuration = Date().timeIntervalSince(startTime)
            if actualDuration < 1.0 {
                // Very short press - simulate a tap without speaking
                recordingTime = 0.5
            } else {
                // Normal press - simulate speaking for 3 seconds
                recordingTime = 3.0
            }
        } else {
            // Fallback if no start time was recorded
            recordingTime = 3.0
        }

        // Reset recording state
        isRecording = false
        recordingStartTime = nil

        // Return a mock URL
        return URL(string: "file:///mock-recording.m4a")!
    }

    public func deleteRecording() {
        // Do nothing in the mock implementation
    }

    public func startPlayback() async throws {
        isPlaying = true
    }

    public func pausePlayback() {
        isPlaying = false
    }

    public func stopPlayback() {
        isPlaying = false
    }

    public func deleteRecording(at url: URL) async throws {
        // Do nothing in the mock implementation
    }

    public func getRecordingDuration(for url: URL) async throws -> TimeInterval {
        // Return a mock duration
        return 5.0
    }

    public func getRecordingWaveform(for url: URL) async throws -> [Float] {
        // Return a mock waveform
        return [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    }

    /// Diagnostic method to test microphone access and audio session setup
    public func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double) {
        // For the mock implementation, always return success with simulated values
        return (true, "Mock microphone test successful", -30.0)
    }
}

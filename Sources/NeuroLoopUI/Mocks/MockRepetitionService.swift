import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

/// A mock implementation of the RepetitionServiceProtocol for UI development and testing
@available(iOS 17.0, macOS 14.0, *)
@MainActor
public class FullMockRepetitionService: RepetitionServiceProtocol, @unchecked Sendable {
    // MARK: - Properties

    private var affirmations: [UUID: any AffirmationProtocol] = [:]

    // MARK: - Initialization

    public init() {}

    // MARK: - RepetitionServiceProtocol Methods

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws -> RepetitionResult {
        // Get the current repetition count
        let currentRepetitions = affirmation.currentRepetitions

        // Increment by exactly 1
        let newRepetitions = currentRepetitions + 1

        // Calculate the exact new progress values based on the new repetition count
        // IMPORTANT: Use exact division to ensure precise conversion between repetitions and progress
        let newTodayProgress = min(1.0, Double(newRepetitions) / 100.0)
        let newCycleProgress = min(1.0, Double(newRepetitions) / 100.0)

        print("[MockRepetitionService] Recording repetition - currentRepetitions: \(currentRepetitions) -> \(newRepetitions)")
        print("[MockRepetitionService] New todayProgress: \(newTodayProgress) (calculated from exact repetition count)")
        print("[MockRepetitionService] VERIFICATION - newRepetitions: \(newRepetitions), floor(newTodayProgress * 100): \(Int(floor(newTodayProgress * 100.0)))")

        // Create a copy of the affirmation with updated progress
        let updatedAffirmation = await AffirmationStub(
            id: affirmation.id,
            text: affirmation.text,
            category: affirmation.category,
            recordingURL: affirmation.recordingURL,
            isFavorite: affirmation.isFavorite,
            todayProgress: newTodayProgress,
            cycleProgress: newCycleProgress
        )

        print("[MockRepetitionService] Verified updated repetitions: \(updatedAffirmation.currentRepetitions)")

        // Double-check that we've incremented by exactly 1
        if updatedAffirmation.currentRepetitions != currentRepetitions + 1 {
            print("[MockRepetitionService] WARNING: Repetition count did not increment by exactly 1!")
            print("[MockRepetitionService] Expected: \(currentRepetitions + 1), Actual: \(updatedAffirmation.currentRepetitions)")
        }

        // Store the updated affirmation
        await Task { affirmations[affirmation.id] = updatedAffirmation }.value

        return RepetitionResult(
            success: true,
            error: nil,
            updatedAffirmation: updatedAffirmation,
            isQuotaMet: updatedAffirmation.todayProgress >= 1.0,
            isCycleComplete: updatedAffirmation.cycleProgress >= 1.0
        )
    }

    public func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        // Get the stored affirmation if available, otherwise use the provided one
        let storedAffirmation = affirmations[affirmation.id] ?? affirmation

        // Calculate progress based on the affirmation's current state
        // For the mock service, we'll increment by 1 each time instead of by 5
        let totalRepetitions = 100 // Changed from 10 to 100 to match the UI

        // Calculate currentRepetitions based on todayProgress (0.0 to 1.0)
        // IMPORTANT: Use floor() to ensure consistent conversion and prevent jumps
        let currentRepetitions = Int(floor(storedAffirmation.todayProgress * 100.0))
        print("[MockRepetitionService] getProgress - todayProgress: \(storedAffirmation.todayProgress), calculated currentRepetitions: \(currentRepetitions)")
        let currentDay = 1
        let totalDays = 7

        return ProgressInfo(
            todayProgress: storedAffirmation.todayProgress,
            cycleProgress: storedAffirmation.cycleProgress,
            currentDay: currentDay,
            totalDays: totalDays,
            currentRepetitions: currentRepetitions,
            totalRepetitions: totalRepetitions,
            hasTodayQuotaMet: storedAffirmation.todayProgress >= 1.0,
            isCycleComplete: storedAffirmation.cycleProgress >= 1.0,
            hasActiveCycle: storedAffirmation.hasActiveCycle
        )
    }

    public func validateStreaks() async throws -> StreakValidationResult {
        // Mock implementation that always returns success
        return StreakValidationResult(
            success: true,
            error: nil,
            validatedAffirmations: 0,
            brokenStreaks: 0
        )
    }

    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        // Reset the affirmation's progress
        let updatedAffirmation = await AffirmationStub(
            id: affirmation.id,
            text: affirmation.text,
            category: affirmation.category,
            recordingURL: affirmation.recordingURL,
            isFavorite: affirmation.isFavorite,
            todayProgress: 0.0,
            cycleProgress: 0.0
        )

        // Store the updated affirmation
        await Task { affirmations[affirmation.id] = updatedAffirmation }.value

        return CycleResult(
            success: true,
            error: nil,
            updatedAffirmation: updatedAffirmation
        )
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        // Start a new cycle for the affirmation
        let updatedAffirmation = await AffirmationStub(
            id: affirmation.id,
            text: affirmation.text,
            category: affirmation.category,
            recordingURL: affirmation.recordingURL,
            isFavorite: affirmation.isFavorite,
            todayProgress: 0.0,
            cycleProgress: 0.0
        )

        // Store the updated affirmation
        await Task { affirmations[affirmation.id] = updatedAffirmation }.value

        return CycleResult(
            success: true,
            error: nil,
            updatedAffirmation: updatedAffirmation
        )
    }

    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        // Start a session for the affirmation
        return try await startCycle(for: affirmation)
    }

    public func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        return StreakInfo(
            currentStreak: 1,
            longestStreak: 1,
            completedCycles: 0,
            hasActiveCycle: true,
            cycleStartDate: Date(),
            lastRepetitionDate: Date()
        )
    }

    public func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        return !affirmation.hasTodayQuotaMet && affirmation.hasActiveCycle
    }

    public func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval? {
        return nil
    }

    public func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        return false
    }
}

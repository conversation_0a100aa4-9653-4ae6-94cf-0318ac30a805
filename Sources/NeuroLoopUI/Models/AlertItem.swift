import Foundation
import SwiftUI

/// A model for alert items
public struct AlertItem: Identifiable {
    public let id = UUID()
    public let title: String
    public let message: String

    /// Initializes a new alert item
    /// - Parameters:
    ///   - title: The title of the alert
    ///   - message: The message of the alert
    public init(
        title: String,
        message: String
    ) {
        self.title = title
        self.message = message
    }
}

import Foundation

public struct StreakCalendarDay: Identifiable {
    public let id: UUID
    public let date: Date
    public let repetitions: Int
    public let isComplete: Bool
    public let progress: Double
    
    public init(
        id: UUID = UUID(),
        date: Date,
        repetitions: Int,
        isComplete: Bool,
        progress: Double
    ) {
        self.id = id
        self.date = date
        self.repetitions = repetitions
        self.isComplete = isComplete
        self.progress = progress
    }
}

#if DEBUG
extension StreakCalendarDay {
    static var sample: StreakCalendarDay {
        StreakCalendarDay(
            date: Date(),
            repetitions: 100,
            isComplete: true,
            progress: 1.0
        )
    }
    
    static var samples: [StreakCalendarDay] {
        (0..<30).map { i in
            let date = Calendar.current.date(byAdding: .day, value: -i, to: Date())!
            let isComplete = Bool.random()
            let repetitions = isComplete ? 100 : Int.random(in: 0...99)
            let progress = Double(repetitions) / 100.0
            
            return StreakCalendarDay(
                date: date,
                repetitions: repetitions,
                isComplete: isComplete,
                progress: progress
            )
        }
    }
}
#endif 
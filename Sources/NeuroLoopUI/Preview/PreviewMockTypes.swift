import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

@MainActor
public struct PreviewMockPurchaseManager: PurchaseManagerSendable {
    public init() {}

    public func purchasePremium() async throws {}
    public func restorePurchases() async throws {}
}

public struct PreviewMockDataExportService: DataExportServiceProtocol {
    public init() {}

    public func exportData() async throws -> URL {
        URL(fileURLWithPath: "")
    }

    public func deleteAllData() async throws {}
}

public struct PreviewMockSyncService: SyncServiceProtocol {
    public init() {}

    public func syncNow() async throws {}
    public var syncStatus: SyncStatus { .idle }
    public var syncStatusPublisher: AnyPublisher<SyncStatus, Never> {
        Just(.idle).eraseToAnyPublisher()
    }
    public var lastSyncError: Error? { nil }
    public var lastSyncDate: Date? { nil }
    public func setAutomaticSyncEnabled(_ enabled: Bool) async {}
    public var isAutomaticSyncEnabled: Bool { false }
}

public class PreviewMockHapticManager: HapticGenerating {
    public init() {}

    public func playSuccess() async {}
    public func playError() async {}
    public func playWarning() async {}
    public func playSelection() async {}
    public func playImpact(style: ImpactStyle) async {}

    // Additional methods
    public func playCelebrationPattern() async {}
    public func playAffirmationCompletionPattern() async {}
    public func mediumImpact() async {}
}

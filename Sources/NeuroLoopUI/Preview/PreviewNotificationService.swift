import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import UserNotifications

/**
 * PreviewNotificationService
 *
 * A preview implementation of NotificationServiceProtocol for use in SwiftUI previews and testing.
 * This implementation provides realistic simulation of notification scheduling and handling.
 *
 * ## Usage Example
 * ```
 * let service = PreviewServiceFactory.shared.getNotificationService()
 * let viewModel = NotificationSettingsViewModel(notificationService: service)
 * ```
 *
 * ## Preview Data
 * This implementation provides the following test data:
 * - Simulated notification scheduling
 * - Mock authorization status
 * - Preview notification handling
 *
 * ## Actor Isolation
 * This implementation is isolated to MainActor and uses @unchecked Sendable for preview purposes.
 */

public struct NotificationRequest {
    public let id: String
    public let title: String
    public let body: String
    public let date: Date

    public init(id: String, title: String, body: String, date: Date) {
        self.id = id
        self.title = title
        self.body = body
        self.date = date
    }
}

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class PreviewNotificationService: NotificationServiceProtocol, @unchecked Sendable {
    @Published public private(set) var isAuthorized = false
    @Published public private(set) var pendingNotifications: [NotificationRequest] = []

    // MARK: - Private Properties

    private var scheduledNotifications: [String: Date] = [:]
    private var authorizationStatus: UNAuthorizationStatus = .authorized

    // MARK: - Initialization

    public init() {}

    public var isAuthorizedPublisher: AnyPublisher<Bool, Never> {
        $isAuthorized.eraseToAnyPublisher()
    }

    public var lastError: Error?
    public var lastErrorPublisher: AnyPublisher<Error?, Never> {
        Just(lastError).eraseToAnyPublisher()
    }

    // MARK: - Public Methods

    nonisolated public func requestAuthorization(completion: @escaping (Bool, Error?) -> Void) {
        Task { @MainActor in
            isAuthorized = true
            completion(true, nil)
        }
    }

    nonisolated public func checkAuthorizationStatus(
        completion: @escaping (UNAuthorizationStatus) -> Void
    ) {
        Task { @MainActor in
            completion(isAuthorized ? .authorized : .notDetermined)
        }
    }

    nonisolated public func scheduleDailyReminder(at date: Date, message: String) {
        Task { @MainActor in
            let request = NotificationRequest(
                id: "dailyReminder",
                title: "Daily Reminder",
                body: message,
                date: date
            )
            pendingNotifications.append(request)
        }
    }

    nonisolated public func cancelDailyReminder() {
        Task { @MainActor in
            pendingNotifications.removeAll { $0.id == "dailyReminder" }
        }
    }

    nonisolated public func scheduleMilestoneNotification(for days: Int, message: String) {
        Task { @MainActor in
            let request = NotificationRequest(
                id: "milestone-\(days)",
                title: "Milestone Achieved",
                body: message,
                date: Date()
            )
            pendingNotifications.append(request)
        }
    }

    nonisolated public func cancelMilestoneNotifications() {
        Task { @MainActor in
            pendingNotifications.removeAll { $0.id.starts(with: "milestone-") }
        }
    }

    nonisolated public func scheduleStreakProtectionAlert(for date: Date, message: String) {
        Task { @MainActor in
            let request = NotificationRequest(
                id: "streakProtection-\(date.timeIntervalSince1970)",
                title: "Streak Protection",
                body: message,
                date: date
            )
            pendingNotifications.append(request)
        }
    }

    nonisolated public func cancelStreakProtectionAlerts() {
        Task { @MainActor in
            pendingNotifications.removeAll { $0.id.starts(with: "streakProtection-") }
        }
    }

    nonisolated public func handleNotificationResponse(_ response: UNNotificationResponse) {
        // Preview implementation - no-op
    }
}

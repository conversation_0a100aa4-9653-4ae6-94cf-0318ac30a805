import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

public struct PreviewHomeView: View {
    private let repository: AffirmationRepositoryProtocol

    public init(repository: AffirmationRepositoryProtocol) {
        self.repository = repository
    }

    public var body: some View {
        VStack {
            Text("Preview Home")
                .font(.title)
            Text("This is a preview home view for UI testing")
                .foregroundColor(.secondary)
        }
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct PreviewHomeView_Previews: PreviewProvider {
        static var previews: some View {
            Text("Preview not available")
            // Use a simple preview instead of AsyncPreview
            // PreviewHomeView(repository: MockAffirmationRepository())
        }
    }
#endif

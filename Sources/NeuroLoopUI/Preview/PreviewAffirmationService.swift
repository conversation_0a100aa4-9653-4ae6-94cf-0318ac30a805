import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

@MainActor
public final class PreviewAffirmationService: AffirmationServiceProtocol {
    private var affirmations: [UUID: any AffirmationProtocol] = [:]
    private var categories: [AffirmationCategory] = [
        .health,
        .success,
        .relationships,
        .personalGrowth,
        .confidence,
        .mindfulness,
    ]

    public init() {}

    nonisolated public func fetchAffirmations() async throws -> [any AffirmationProtocol] {
        await Task { @MainActor in
            return Array(affirmations.values)
        }.value
    }

    nonisolated public func fetchAffirmation(id: UUID) async throws -> (any AffirmationProtocol)? {
        await Task { @MainActor in
            return affirmations[id]
        }.value
    }

    nonisolated public func createAffirmation(
        text: String,
        category: AffirmationCategory,
        recordingURL: URL?
    ) async throws -> any AffirmationProtocol {
        await Task { @MainActor in
            let affirmation = await AffirmationStub(
                text: text,
                category: category,
                recordingURL: recordingURL
            )
            affirmations[affirmation.id] = affirmation
            return affirmation
        }.value
    }

    nonisolated public func updateAffirmation(_ affirmation: any AffirmationProtocol) async throws {
        await Task { @MainActor in
            affirmations[affirmation.id] = affirmation
        }.value
    }

    nonisolated public func deleteAffirmation(id: UUID) async throws {
        await Task { @MainActor in
            _ = affirmations.removeValue(forKey: id)
        }.value
    }

    nonisolated public func fetchAffirmations(category: AffirmationCategory) async throws
        -> [any AffirmationProtocol]
    {
        await Task { @MainActor in
            return Array(affirmations.values.filter { $0.category == category })
        }.value
    }

    nonisolated public func fetchFavoriteAffirmations() async throws -> [any AffirmationProtocol] {
        await Task { @MainActor in
            return Array(affirmations.values.filter { $0.isFavorite })
        }.value
    }

    nonisolated public func fetchCurrentAffirmation() async throws -> (any AffirmationProtocol)? {
        await Task { @MainActor in
            return affirmations.values.first { $0.hasActiveCycle }
        }.value
    }

    nonisolated public func toggleFavorite(_ affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        await Task { @MainActor in
            let updatedAffirmation = await AffirmationStub(
                id: affirmation.id,
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                isFavorite: !affirmation.isFavorite,
                todayProgress: affirmation.todayProgress,
                cycleProgress: affirmation.cycleProgress
            )
            affirmations[affirmation.id] = updatedAffirmation
            return updatedAffirmation
        }.value
    }

    nonisolated public func startCycle(for affirmation: any AffirmationProtocol) async throws {
        await Task { @MainActor in
            let updatedAffirmation = await AffirmationStub(
                id: affirmation.id,
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                isFavorite: affirmation.isFavorite,
                todayProgress: 0.0,  // Reset progress for new cycle
                cycleProgress: 0.0   // Reset progress for new cycle
            )
            affirmations[affirmation.id] = updatedAffirmation
        }.value
    }

    nonisolated public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> any AffirmationProtocol
    {
        await Task { @MainActor in
            // Calculate the exact new repetition count by incrementing by exactly 1
            let currentRepetitions = affirmation.currentRepetitions
            let newRepetitions = currentRepetitions + 1

            // Calculate the exact new progress values based on the new repetition count
            // This ensures we increment by exactly 1 each time
            let newTodayProgress = min(1.0, Double(newRepetitions) / 100.0)
            let newCycleProgress = min(1.0, Double(newRepetitions) / 100.0)

            print("[PreviewAffirmationService] Recording repetition - currentRepetitions: \(currentRepetitions) -> \(newRepetitions)")
            print("[PreviewAffirmationService] New todayProgress: \(newTodayProgress) (calculated from exact repetition count)")

            let updatedAffirmation = await AffirmationStub(
                id: affirmation.id,
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                isFavorite: affirmation.isFavorite,
                todayProgress: newTodayProgress,
                cycleProgress: newCycleProgress
            )

            print("[PreviewAffirmationService] Verified updated repetitions: \(updatedAffirmation.currentRepetitions)")

            // Double-check that we've incremented by exactly 1
            if updatedAffirmation.currentRepetitions != currentRepetitions + 1 {
                print("[PreviewAffirmationService] WARNING: Repetition count did not increment by exactly 1!")
                print("[PreviewAffirmationService] Expected: \(currentRepetitions + 1), Actual: \(updatedAffirmation.currentRepetitions)")
            }

            affirmations[affirmation.id] = updatedAffirmation
            return updatedAffirmation
        }.value
    }

    nonisolated public func updateEnergyLevel(
        _ level: Double, for affirmation: any AffirmationProtocol
    ) async throws -> any AffirmationProtocol {
        await Task { @MainActor in
            let updatedAffirmation = await AffirmationStub(
                id: affirmation.id,
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                isFavorite: affirmation.isFavorite,
                todayProgress: affirmation.todayProgress,
                cycleProgress: affirmation.cycleProgress
            )
            // Note: We can't directly set energyLevel with the simplified initializer
            // In a real implementation, we would need to modify the AffirmationStub class
            affirmations[affirmation.id] = updatedAffirmation
            return updatedAffirmation
        }.value
    }

    nonisolated public func recordMood(
        _ rating: Int, notes: String?, for affirmation: any AffirmationProtocol
    ) async throws -> any AffirmationProtocol {
        await Task { @MainActor in
            let updatedAffirmation = await AffirmationStub(
                id: affirmation.id,
                text: affirmation.text,
                category: affirmation.category,
                recordingURL: affirmation.recordingURL,
                isFavorite: affirmation.isFavorite,
                todayProgress: affirmation.todayProgress,
                cycleProgress: affirmation.cycleProgress
            )
            // Note: We can't directly set mood rating and notes with the simplified initializer
            // In a real implementation, we would need to modify the AffirmationStub class
            affirmations[affirmation.id] = updatedAffirmation
            return updatedAffirmation
        }.value
    }

    nonisolated public func getStatistics() async throws -> AffirmationStatistics {
        return await Task { @MainActor in
            var statistics = AffirmationStatistics()
            statistics.totalAffirmations = affirmations.count
            statistics.completedCycles = affirmations.values.reduce(0) { $0 + $1.completedCycles }
            statistics.totalRepetitions = affirmations.values.reduce(0) { $0 + $1.currentRepetitions }
            statistics.activeAffirmations = affirmations.values.filter { $0.hasActiveCycle }.count
            statistics.favoriteAffirmations = affirmations.values.filter { $0.isFavorite }.count

            var categoryCounts: [AffirmationCategory: Int] = [:]
            for affirmation in affirmations.values {
                categoryCounts[affirmation.category, default: 0] += 1
            }
            statistics.categoryDistribution = categoryCounts

            return statistics
        }.value
    }
}

import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes
import UserNotifications

@MainActor
public final class PreviewServiceFactory {
    public static let shared = PreviewServiceFactory()

    private init() {}

    // MARK: - Core Services

    public func getAffirmationService() -> AffirmationServiceProtocol {
        PreviewMockAffirmationService()
    }

    public func getRepetitionService() -> RepetitionServiceProtocol {
        return PreviewRepetitionService()
    }

    public func getStreakService() -> StreakServiceProtocol {
        PreviewStreakService()
    }

    public func getAudioRecordingService() -> AudioRecordingServiceProtocol {
        PreviewAudioRecordingService()
    }

    @available(iOS 17.0, macOS 14.0, *)
    public func getNotificationService() -> NotificationServiceProtocol {
        PreviewNotificationService()
    }

    // MARK: - Premium & Sync Services

    @available(iOS 17.0, macOS 14.0, *)
    public func getPurchaseManager() -> PurchaseManagerSendable {
        PreviewMockPurchaseManager()
    }

    @available(iOS 17.0, macOS 14.0, *)
    public func getDataExportService() -> DataExportServiceProtocol {
        PreviewMockDataExportService()
    }

    public func getSyncService() -> SyncServiceProtocol {
        PreviewMockSyncService()
    }

    // MARK: - UI Services

    public func getHapticManager() -> HapticGenerating {
        PreviewMockHapticManager()
    }

    public func getThemeManager() -> ThemeManager {
        ThemeManager.shared
    }
}

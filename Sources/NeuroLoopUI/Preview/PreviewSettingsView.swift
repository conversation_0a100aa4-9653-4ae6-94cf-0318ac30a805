import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

public struct PreviewSettingsView: View {
    public init() {}

    public var body: some View {
        List {
            Section("Preview Settings") {
                Text("Theme")
                Text("Notifications")
                Text("Account")
            }
        }
        #if os(iOS)
            .listStyle(.insetGrouped)
        #else
            .listStyle(.automatic)
        #endif
    }
}

#if DEBUG
    struct PreviewSettingsView_Previews: PreviewProvider {
        static var previews: some View {
            PreviewSettingsView()
        }
    }
#endif

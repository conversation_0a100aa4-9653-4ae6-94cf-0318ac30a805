import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces

/**
 * PreviewAudioRecordingService
 *
 * A preview implementation of AudioRecordingServiceProtocol for use in SwiftUI previews and testing.
 * This implementation provides realistic simulation of audio recording and playback functionality.
 *
 * ## Usage Example
 * ```
 * let service = PreviewServiceFactory.shared.getAudioRecordingService()
 * let viewModel = AudioRecordingViewModel(audioRecordingService: service)
 * ```
 *
 * ## Preview Data
 * This implementation provides the following test data:
 * - Simulated recording with realistic power levels
 * - Simulated playback with progress updates
 * - Sample waveform data for visualization
 *
 * ## Actor Isolation
 * This implementation is isolated to MainActor and uses @unchecked Sendable for preview purposes.
 */

@MainActor
public final class PreviewAudioRecordingService: AudioRecordingServiceProtocol {
    @Published public private(set) var isRecording = false
    @Published public private(set) var recordingTime: TimeInterval = 0
    @Published public private(set) var recordingPower: Double = 0
    @Published public private(set) var recordingURL: URL? = nil
    @Published public private(set) var isPlaying = false
    @Published public private(set) var playbackProgress: Double = 0
    @Published public private(set) var playbackTime: TimeInterval = 0
    @Published public private(set) var error: Error? = nil

    private var recordingTimer: Timer?
    private var playbackTimer: Timer?
    private var simulatedDuration: TimeInterval = 30.0

    public init() {}

    public var isRecordingPublisher: Published<Bool>.Publisher { $isRecording }
    public var recordingTimePublisher: Published<TimeInterval>.Publisher { $recordingTime }
    public var recordingPowerPublisher: Published<Double>.Publisher { $recordingPower }
    public var recordingURLPublisher: Published<URL?>.Publisher { $recordingURL }
    public var isPlayingPublisher: Published<Bool>.Publisher { $isPlaying }
    public var playbackProgressPublisher: Published<Double>.Publisher { $playbackProgress }
    public var playbackTimePublisher: Published<TimeInterval>.Publisher { $playbackTime }
    public var errorPublisher: Published<Error?>.Publisher { $error }

    public func startRecording() async throws {
        isRecording = true
        recordingTime = 0
        recordingPower = 0
        error = nil

        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) {
            [weak self] _ in
            Task { @MainActor in
                guard let self = self else { return }
                self.recordingTime += 0.1
                self.recordingPower = Double.random(in: 0.1...1.0)
            }
        }
    }

    public func stopRecording() async throws -> URL {
        recordingTimer?.invalidate()
        recordingTimer = nil
        isRecording = false
        error = nil
        return URL(fileURLWithPath: "")
    }

    public func deleteRecording() {
        recordingURL = nil
    }

    public func startPlayback() async throws {
        isPlaying = true
        playbackTime = 0
        playbackProgress = 0
        error = nil

        playbackTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) {
            [weak self] _ in
            Task { @MainActor in
                guard let self = self else { return }
                self.playbackTime += 0.1
                self.playbackProgress = self.playbackTime / self.simulatedDuration
                if self.playbackProgress >= 1.0 {
                    self.stopPlayback()
                }
            }
        }
    }

    public func pausePlayback() {
        playbackTimer?.invalidate()
        playbackTimer = nil
        isPlaying = false
    }

    public func stopPlayback() {
        playbackTimer?.invalidate()
        playbackTimer = nil
        isPlaying = false
        playbackTime = 0
        playbackProgress = 0
    }

    public func deleteRecording(at url: URL) async throws {
        recordingURL = nil
    }

    public func getRecordingDuration(for url: URL) async throws -> TimeInterval {
        return simulatedDuration
    }

    public func getRecordingWaveform(for url: URL) async throws -> [Float] {
        return Array(repeating: Float.random(in: 0...1), count: 100)
    }

    /// Diagnostic method to test microphone access and audio session setup
    public func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double) {
        return (true, "Preview microphone test successful", -30.0)
    }
}

import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

public final class PreviewStreakService: StreakServiceProtocol {
    private var streakData: [UUID: StreakInfo] = [:]

    public init() {}

    public func validateStreaks() async throws -> StreakValidationResult {
        return StreakValidationResult(
            success: true,
            error: nil,
            validatedAffirmations: 1,
            brokenStreaks: 0
        )
    }

    public func getStreakStatistics() async throws -> StreakStatistics {
        return StreakStatistics()
    }

    public func getStreakCalendarData(
        for affirmation: any AffirmationProtocol, numberOfDays: Int
    ) async -> [NeuroLoopInterfaces.StreakCalendarDay] {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        return (0..<numberOfDays).map { dayOffset in
            guard let date = calendar.date(byAdding: .day, value: -dayOffset, to: today) else {
                return NeuroLoopInterfaces.StreakCalendarDay(
                    date: today,
                    repetitions: 0,
                    isComplete: false,
                    progress: 0.0
                )
            }

            let repetitions = Int.random(in: 0...5)
            let isComplete = repetitions >= 3
            let progress = Double(repetitions) / 5.0

            return NeuroLoopInterfaces.StreakCalendarDay(
                date: date,
                repetitions: repetitions,
                isComplete: isComplete,
                progress: progress
            )
        }
    }

    public func isStreakAtRisk(for affirmation: any AffirmationProtocol) async -> Bool {
        return false
    }

    public func updateStreak(for affirmation: any AffirmationProtocol) async throws {
        // Preview implementation - no-op
    }

    @MainActor
    public func getStreakInfo(for affirmation: any AffirmationProtocol) async -> StreakInfo {
        return streakData[affirmation.id]
            ?? StreakInfo(
                currentStreak: 3,
                longestStreak: 7,
                completedCycles: 1,
                hasActiveCycle: true,
                cycleStartDate: Date(),
                lastRepetitionDate: Date()
            )
    }

    public func getStreakCalendar(for affirmation: any AffirmationProtocol) async
        -> [NeuroLoopInterfaces.StreakCalendarDay]
    {
        return await getStreakCalendarData(for: affirmation, numberOfDays: 30)
    }

    public func validateStreak(for affirmation: any AffirmationProtocol) async -> Bool {
        return false
    }

    public func resetStreak(for affirmation: any AffirmationProtocol) async throws {
        // Preview implementation - no-op
    }
}

import NeuroLoopInterfaces
import NeuroLoopTypes
import SwiftUI

public struct PreviewAffirmationsView: View {
    private let repository: AffirmationRepositoryProtocol

    public init(repository: AffirmationRepositoryProtocol) {
        self.repository = repository
    }

    public var body: some View {
        List {
            ForEach(0..<3) { _ in
                PreviewAffirmationRow()
            }
        }
        .listStyle(.plain)
    }
}

private struct PreviewAffirmationRow: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Preview Affirmation")
                .font(.headline)
            Text("This is a preview affirmation for UI testing")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 8)
    }
}

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct PreviewAffirmationsView_Previews: PreviewProvider {
        static var previews: some View {
            Text("Preview not available")
            // Use a simple preview instead of AsyncPreview
            // PreviewAffirmationsView(repository: MockAffirmationRepository())
        }
    }
#endif

import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

@MainActor
public final class PreviewRepetitionService: RepetitionServiceProtocol {
    private var progressData: [UUID: ProgressInfo] = [:]
    private var streakData: [UUID: StreakInfo] = [:]
    private var repetitionTimes: [UUID: Date] = [:]

    public init() {}

    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws
        -> RepetitionResult
    {
        repetitionTimes[affirmation.id] = Date()
        return RepetitionResult(
            success: true,
            updatedAffirmation: affirmation,
            isQuotaMet: true,
            isCycleComplete: false
        )
    }

    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }

    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }

    nonisolated public func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        Task { @MainActor in
            return progressData[affirmation.id]
                ?? ProgressInfo(
                    todayProgress: 0.5,
                    cycleProgress: 0.5,
                    currentDay: 3,
                    totalDays: 7,
                    currentRepetitions: 7,
                    totalRepetitions: 15,
                    hasTodayQuotaMet: false,
                    isCycleComplete: false,
                    hasActiveCycle: true
                )
        }
        return ProgressInfo(
            todayProgress: 0.5,
            cycleProgress: 0.5,
            currentDay: 3,
            totalDays: 7,
            currentRepetitions: 7,
            totalRepetitions: 15,
            hasTodayQuotaMet: false,
            isCycleComplete: false,
            hasActiveCycle: true
        )
    }

    nonisolated public func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        Task { @MainActor in
            return streakData[affirmation.id]
                ?? StreakInfo(
                    currentStreak: 3,
                    longestStreak: 7,
                    completedCycles: 1,
                    hasActiveCycle: true,
                    cycleStartDate: Date(),
                    lastRepetitionDate: Date()
                )
        }
        return StreakInfo(
            currentStreak: 3,
            longestStreak: 7,
            completedCycles: 1,
            hasActiveCycle: true,
            cycleStartDate: Date(),
            lastRepetitionDate: Date()
        )
    }

    nonisolated public func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        Task { @MainActor in
            guard let lastRepetition = repetitionTimes[affirmation.id] else { return true }
            return Date().timeIntervalSince(lastRepetition) >= 3600  // 1 hour cooldown
        }
        return true
    }

    nonisolated public func timeUntilNextRepetition(for affirmation: any AffirmationProtocol)
        -> TimeInterval?
    {
        Task { @MainActor in
            guard let lastRepetition = repetitionTimes[affirmation.id] else {
                return nil as TimeInterval?
            }
            let timeSinceLast = Date().timeIntervalSince(lastRepetition)
            return max(0, 3600 - timeSinceLast)  // 1 hour cooldown
        }
        return nil
    }

    nonisolated public func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        Task { @MainActor in
            guard let lastRepetition = repetitionTimes[affirmation.id] else { return false }
            return Date().timeIntervalSince(lastRepetition) > 86400  // 24 hours
        }
        return false
    }

    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws
        -> CycleResult
    {
        return CycleResult(
            success: true,
            updatedAffirmation: affirmation
        )
    }
}

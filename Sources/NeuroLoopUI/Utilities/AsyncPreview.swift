import SwiftUI

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    public struct AsyncPreview<Content: View>: View {
        let content: () async -> Content

        @State private var loadedContent: Content?

        public init(content: @escaping () async -> Content) {
            self.content = content
        }

        public var body: some View {
            ZStack {
                if let loadedContent {
                    loadedContent
                } else {
                    ProgressView()
                        .onAppear {
                            Task {
                                loadedContent = await content()
                            }
                        }
                }
            }
        }
    }
#endif

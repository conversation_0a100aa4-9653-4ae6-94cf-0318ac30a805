#!/bin/bash

# Script to generate App Store screenshots for different device sizes
# Usage: ./generate_screenshots.sh

# Create screenshots directory if it doesn't exist
mkdir -p Screenshots

# List available devices
echo "Available devices:"
xcrun simctl list devices

# iPhone 6.5" Display (iPhone 14 Pro Max)
echo "Taking screenshots for iPhone 14 Pro Max..."
xcrun simctl boot "iPhone 14 Pro Max" || true
xcrun simctl io booted screenshot Screenshots/iphone_14_pro_max_home.png
xcrun simctl io booted screenshot Screenshots/iphone_14_pro_max_affirmation.png
xcrun simctl io booted screenshot Screenshots/iphone_14_pro_max_theme.png
xcrun simctl io booted screenshot Screenshots/iphone_14_pro_max_stats.png
xcrun simctl io booted screenshot Screenshots/iphone_14_pro_max_premium.png

# iPhone 5.5" Display (iPhone 8 Plus)
echo "Taking screenshots for iPhone 8 Plus..."
xcrun simctl boot "iPhone 8 Plus" || true
xcrun simctl io booted screenshot Screenshots/iphone_8_plus_home.png
xcrun simctl io booted screenshot Screenshots/iphone_8_plus_affirmation.png
xcrun simctl io booted screenshot Screenshots/iphone_8_plus_theme.png
xcrun simctl io booted screenshot Screenshots/iphone_8_plus_stats.png
xcrun simctl io booted screenshot Screenshots/iphone_8_plus_premium.png

# iPad Pro 12.9" Display
echo "Taking screenshots for iPad Pro 12.9..."
xcrun simctl boot "iPad Pro (12.9-inch) (6th generation)" || true
xcrun simctl io booted screenshot Screenshots/ipad_pro_12_9_home.png
xcrun simctl io booted screenshot Screenshots/ipad_pro_12_9_affirmation.png
xcrun simctl io booted screenshot Screenshots/ipad_pro_12_9_theme.png
xcrun simctl io booted screenshot Screenshots/ipad_pro_12_9_stats.png
xcrun simctl io booted screenshot Screenshots/ipad_pro_12_9_premium.png

# iPad 10.5" Display
echo "Taking screenshots for iPad Pro 10.5..."
xcrun simctl boot "iPad Pro (10.5-inch)" || true
xcrun simctl io booted screenshot Screenshots/ipad_pro_10_5_home.png
xcrun simctl io booted screenshot Screenshots/ipad_pro_10_5_affirmation.png
xcrun simctl io booted screenshot Screenshots/ipad_pro_10_5_theme.png
xcrun simctl io booted screenshot Screenshots/ipad_pro_10_5_stats.png
xcrun simctl io booted screenshot Screenshots/ipad_pro_10_5_premium.png

echo "Screenshots generated successfully in the Screenshots directory" 
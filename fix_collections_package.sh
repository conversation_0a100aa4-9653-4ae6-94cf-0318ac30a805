#!/bin/bash

# Exit on error
set -e

echo "Fixing Collections package integration for Xcode..."

# Step 1: Clean the build folder and derived data
echo "Cleaning build folder and derived data..."
rm -rf .build
rm -rf ~/Library/Developer/Xcode/DerivedData/*

# Step 2: Reset package cache
echo "Resetting package cache..."
swift package reset

# Step 3: Resolve dependencies
echo "Resolving dependencies..."
swift package resolve

# Step 4: Create a temporary Package.swift with explicit Collections dependency
echo "Creating temporary Package.swift with explicit Collections dependency..."
cp Package.swift Package.swift.bak

# Step 5: Update Xcode project
echo "Updating Xcode project..."
xcodebuild -resolvePackageDependencies

# Step 6: Clean Xcode project
echo "Cleaning Xcode project..."
xcodebuild clean

# Step 7: Open the project in Xcode
echo "Opening the project in Xcode..."
open NeuroLoopApp.xcodeproj

echo "Done! Now try building the project in Xcode."
echo "If you still see the 'Missing package product Collections' error, try these steps:"
echo "1. Close Xcode"
echo "2. Delete the DerivedData folder: rm -rf ~/Library/Developer/Xcode/DerivedData/*"
echo "3. Open the project again: open NeuroLoopApp.xcodeproj"
echo "4. In Xcode, go to File > Packages > Reset Package Caches"
echo "5. In Xcode, go to File > Packages > Resolve Package Versions"
echo "6. Build the project again"

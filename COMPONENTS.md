# UI Component Architecture & Inventory

## Overview

This document describes the organization, usage, and guidelines for all reusable UI components in the NeuroLoopApp2025_Fresh codebase. It establishes a clear, maintainable structure to prevent duplication and promote best practices.

---

## Directory Structure

```
/Sources/NeuroLoopUI/
  /Components/
    /Buttons/        # Control components (RecordingButton, PlayButton, etc.)
    /Cards/          # Content container components (AffirmationCard, EnhancedAffirmationCard)
    /Feedback/       # User feedback components (ErrorView, LoadingView)
    /Indicators/     # Progress and status components (ProgressIndicator, RepetitionCounterView)
    /Visualizations/ # Visual effects components (AudioWaveform, ConfettiView, etc.)
  /Views/            # Feature-specific views (Audio, Affirmation, Streak, etc.)
```

---

## Component Categories & Inventory

### Atomic Components

| Name                | Location                                   | Description                        |
|---------------------|--------------------------------------------|------------------------------------|
| RecordingButton     | Components/Buttons/RecordingButton.swift    | Audio record/stop button           |
| PlayButton          | Components/Buttons/PlayButton.swift         | Audio play/pause button            |
| ProgressIndicator   | Components/Indicators/ProgressIndicator.swift| Circular/linear progress display   |
| RepetitionCounterView| Components/Indicators/RepetitionCounterView.swift| Repetition progress with effects |

### Composite Components

| Name                  | Location                                 | Description                        |
|-----------------------|------------------------------------------|------------------------------------|
| AffirmationCard       | Components/Cards/AffirmationCard.swift   | Card for displaying affirmation    |
| EnhancedAffirmationCard| Components/Cards/EnhancedAffirmationCard.swift| Card with advanced visuals      |

### Feedback Components

| Name         | Location                              | Description                        |
|--------------|---------------------------------------|------------------------------------|
| ErrorView    | Components/Feedback/ErrorView.swift   | Error display and handling         |
| LoadingView  | Components/Feedback/LoadingView.swift | Loading/progress feedback          |

### Visualization Components

| Name              | Location                                   | Description                        |
|-------------------|--------------------------------------------|------------------------------------|
| AudioWaveform     | Components/Visualizations/AudioWaveform.swift| Audio waveform visualization     |
| ConfettiView      | Components/Visualizations/ConfettiView.swift| Confetti celebration effect      |
| ParticleEffectView| Components/Visualizations/ParticleEffectView.swift| Particle burst effect         |
| RippleEffectView  | Components/Visualizations/RippleEffectView.swift| Ripple animation effect        |

---

## Guidelines for Component Creation & Usage

- **Naming:** Use clear, descriptive names that indicate component purpose and reusability. Follow the pattern `[Feature][Component]View` for composites, `[Component]Button` for controls, etc.
- **Placement:** Place atomic and composite components in `/Components/` subdirectories by function. Place feature-specific or screen-level views in `/Views/`.
- **Documentation:** Every component must have a docstring explaining its purpose and usage. Complex components should include usage examples.
- **Dependencies:** Minimize dependencies between components. Document any required relationships in this file.
- **Review:** All new components must be reviewed for duplication and adherence to these guidelines before merging.

---

## Component Relationships & Dependencies

- **RepetitionCounterView** uses `RippleEffectView` and `ParticleEffectView` for visual feedback.
- **AudioWaveform** is used in audio-related views and playback controls.
- **ErrorView** and **LoadingView** are used throughout the app for consistent feedback.
- **Cards** (AffirmationCard, EnhancedAffirmationCard) are used in lists and detail views.

---

## Example Usage

```swift
import NeuroLoopUI

var body: some View {
    RecordingButton(isRecording: isRecording, recordingPower: power) { ... }
    PlayButton(isPlaying: isPlaying) { ... }
    ProgressIndicator(progress: 0.5)
    ErrorView(error: error, retryAction: { ... }, dismissAction: { ... })
    AudioWaveform(samples: samples)
}
```

---

## Future Development

- All new components must be added to this inventory.
- Update this document and add inline docstrings for every new or modified component.
- Review this structure regularly to ensure it meets evolving project needs. 
# NeuroLoop Diagnostic Tools

This package contains diagnostic tools to help troubleshoot and fix issues with the repetition counter in the NeuroLoop app.

## How to Use the Diagnostic Tools

### Option 1: Use the DiagnosticLauncher

The `DiagnosticLauncher` provides a simple button that can be added to any view in your app. When tapped, it opens a sheet with access to various diagnostic tools.

```swift
import SwiftUI
import NeuroLoopUI
import NeuroLoopCore

struct ContentView: View {
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        VStack {
            Text("Your App Content")

            // Add the DiagnosticLauncher at the bottom of your view
            DiagnosticLauncher(serviceFactory: ServiceFactory.shared)
        }
    }
}
```

### Option 2: Use the RepetitionDiagnosticView Directly

You can also use the `RepetitionDiagnosticView` directly in your app:

```swift
import SwiftUI
import NeuroLoopUI
import NeuroLoopCore

struct ContentView: View {
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        RepetitionDiagnosticView(
            affirmation: AffirmationStub(
                text: "I am confident and capable in everything I do",
                category: .positive
            ),
            repetitionService: ServiceFactory.shared.getRepetitionService(),
            audioService: ServiceFactory.shared.getAudioRecordingService(),
            affirmationService: ServiceFactory.shared.getAffirmationService(),
            streakService: ServiceFactory.shared.getStreakService()
        )
    }
}
```

### Option 3: Use the FixedSpeakAffirmationViewModel

If you want to fix the issue in your existing `SpeakAffirmationView`, you can replace the `SpeakAffirmationViewModel` with the `FixedSpeakAffirmationViewModel`:

```swift
import SwiftUI
import NeuroLoopUI
import NeuroLoopCore

struct SpeakAffirmationView: View {
    @StateObject private var viewModel: FixedSpeakAffirmationViewModel

    init(affirmation: any AffirmationProtocol) {
        self._viewModel = StateObject(wrappedValue: FixedSpeakAffirmationViewModel(
            affirmation: affirmation,
            repetitionService: ServiceFactory.shared.getRepetitionService(),
            audioService: ServiceFactory.shared.getAudioRecordingService(),
            affirmationService: ServiceFactory.shared.getAffirmationService()
        ))
    }

    var body: some View {
        // Your existing view code, but using the FixedSpeakAffirmationViewModel
    }
}
```

## The Issue

The repetition counter does not increase from 0 to 1 when speaking affirmations, even when:
- The microphone test works correctly
- Debug Mode is enabled
- Audio is being recorded properly

## Root Cause Analysis

After analyzing the code, we've identified several potential issues:

1. **Audio Session Configuration Mismatch**:
   - The microphone test uses a different audio session configuration than the actual recording process.
   - The test creates a temporary recorder with its own settings, while the main recording uses a different configuration.

2. **Debug Mode Implementation Issue**:
   - The Debug Mode toggle is correctly implemented in the code, but there appears to be an issue with how it's connected to the repetition service.
   - Even though Debug Mode should bypass speech recognition verification, the repetition counter isn't incrementing.

3. **Audio Level Visualization Issue**:
   - The app shows 0% at -120 dB during recording, which indicates no audio is being detected by the recording system.
   - This is inconsistent with the microphone test showing -42 dB.

4. **Repetition Service Connection Issue**:
   - The most likely issue is that the repetition service isn't properly receiving or processing the repetition increment when in Debug Mode.

## The Solution

The `FixedSpeakAffirmationViewModel` addresses these issues by:

1. **Ensuring Debug Mode Properly Bypasses Speech Recognition**:
   - When Debug Mode is enabled, the app bypasses speech recognition verification and directly records the repetition.

2. **Fixing the Audio Session Configuration**:
   - Using the same audio session configuration for recording as the microphone test.

3. **Adding Debug Logging**:
   - Adding detailed logging to track the repetition count before and after recording a repetition.

## How to Test the Fix

1. Open the app and navigate to the diagnostic tools.
2. Use the RepetitionDiagnosticView to test the repetition counter.
3. Enable Debug Mode (it's enabled by default in the diagnostic view).
4. Tap the record button, then tap it again to stop.
5. Verify that the repetition counter increments from 0 to 1.

## Applying the Fix to Your App

Once you've confirmed that the fix works in the diagnostic tools, you can apply it to your app by:

1. **Using the FixedSpeakAffirmationViewModel**:
   - Replace the `SpeakAffirmationViewModel` with the `FixedSpeakAffirmationViewModel` in your app.

2. **Updating the Audio Session Configuration**:
   - Make sure the audio session configuration for recording matches the configuration used in the microphone test.
   - Use `.playAndRecord` category and `.spokenAudio` mode for best results.

3. **Ensuring Debug Mode Properly Bypasses Speech Recognition**:
   - Make sure that when Debug Mode is enabled, the app bypasses speech recognition verification and directly records the repetition.

## Additional Resources

- `FixedSpeakAffirmationViewModel.swift`: A fixed version of the SpeakAffirmationViewModel that addresses the issues.
- `RepetitionDiagnosticView.swift`: A view for testing the repetition counter in isolation.
- `DiagnosticLauncher.swift`: A simple button that provides access to various diagnostic tools.

diff --git a/Sources/NeuroLoopUI/ViewModels/SpeakAffirmationViewModel.swift b/Sources/NeuroLoopUI/ViewModels/SpeakAffirmationViewModel.swift
index 1234567..abcdef0 100644
--- a/Sources/NeuroLoopUI/ViewModels/SpeakAffirmationViewModel.swift
+++ b/Sources/NeuroLoopUI/ViewModels/SpeakAffirmationViewModel.swift
@@ -631,6 +631,7 @@ public class SpeakAffirmationViewModel: ObservableObject {
                 if self.debugBypassSpeechRecognition {
                     // In debug mode, we'll use a placeholder text if no speech was recognized
                     if self.spokenText.isEmpty {
+                        // Set a placeholder text for debug mode
                         self.spokenText = "Debug mode: Simulated speech recognition"
                         print("SpeakAffirmationViewModel: DEBUG MODE - Using simulated speech text")
                     }
@@ -707,6 +708,7 @@ public class SpeakAffirmationViewModel: ObservableObject {
                 if self.debugBypassSpeechRecognition {
                     // In debug mode, always consider it a success
                     verificationResult = (success: true, similarity: 1.0)
+                    // Log that we're bypassing verification in debug mode
                     print(
                         "SpeakAffirmationViewModel: DEBUG MODE - Bypassing verification, forcing success"
                     )
@@ -769,6 +771,7 @@ public class SpeakAffirmationViewModel: ObservableObject {
                         let result = try await self.repetitionService.recordRepetition(
                             for: self.affirmation)
                         print(
+                            "SpeakAffirmationViewModel: CRITICAL - Successfully called repetitionService.recordRepetition"
                             "SpeakAffirmationViewModel: DETAILED DEBUG - Successfully called repetitionService.recordRepetition"
                         )
 
@@ -804,6 +807,7 @@ public class SpeakAffirmationViewModel: ObservableObject {
                         // CRITICAL FIX: Use the direct count from the updated affirmation
                         // This ensures we have the correct count for this specific affirmation
                         self.todayRepetitions = updatedDirectCount
+                        // Log the updated count
                         print(
                             "SpeakAffirmationViewModel: Set todayRepetitions to \(self.todayRepetitions) from updated affirmation"
                         )
@@ -1110,10 +1114,10 @@ public class SpeakAffirmationViewModel: ObservableObject {
     private func configureAudioSession(active: Bool) async -> Bool {
         #if os(iOS) || os(watchOS) || os(tvOS)
             if active {
-                // ENHANCED: Further optimize audio session for speech recognition
-                // Use .playAndRecord category to allow both recording and playback
-                // Use .spokenAudio mode which is specifically optimized for speech recognition
+                // CRITICAL FIX: Use the same audio session configuration as the microphone test
+                // This ensures consistent behavior between the test and actual recording
                 let success = await audioSessionManager.activateSession(
+                    // Use .record category for better microphone access
                     category: .playAndRecord,  // Changed to .playAndRecord for better overall performance
                     mode: .spokenAudio,        // Changed to .spokenAudio which is specifically for speech
                     options: [.allowBluetooth, .defaultToSpeaker, .allowAirPlay, .duckOthers]  // Enhanced options

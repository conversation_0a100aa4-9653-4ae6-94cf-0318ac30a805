import Foundation

/// Configuration for App Store submission
struct AppStoreConfiguration {
    // MARK: - App Information
    static let appName = "Neuro Loop 100"
    static let bundleIdentifier = "com.neuroloop.app"
    static let version = "1.0.0"
    static let build = "1"
    
    // MARK: - App Store Metadata
    static let shortDescription = "Transform your mindset with 100 repetitions over 7 days"
    static let fullDescription = """
    Neuro Loop 100 is a powerful affirmation practice app that helps you transform your mindset through focused repetition. The app guides you through 100 repetitions of your chosen affirmation over 7 days, creating lasting neural pathways for positive change.

    Key Features:
    • 100 repetitions over 7 days for maximum impact
    • Beautiful, customizable themes with gradient support
    • Progress tracking and streak monitoring
    • Detailed statistics and insights
    • CloudKit sync across all your devices

    Premium Features:
    • Custom gradient themes
    • Advanced statistics and insights
    • Cross-device sync
    • Ad-free experience

    Start your journey to positive change today with Neuro Loop 100.
    """
    
    static let keywords = "affirmation, mindset, positive thinking, self-improvement, meditation, personal growth, habit formation, mental health, daily practice, transformation"
    
    static let promotionalText = "Transform your mindset with 100 repetitions over 7 days. Start your journey to positive change today."
    
    // MARK: - URLs
    static let supportURL = "https://neuroloop.app/support"
    static let marketingURL = "https://neuroloop.app"
    static let privacyPolicyURL = "https://neuroloop.app/privacy"
    
    // MARK: - In-App Purchase Configuration
    struct Subscription {
        static let monthly = SubscriptionTier(
            id: "com.neuroloop.app.premium.monthly",
            name: "Monthly Premium",
            price: 4.99,
            period: "month",
            features: [
                "Unlimited Affirmations",
                "Advanced Progress Analytics",
                "Custom Gradient Themes",
                "Cross-Device Sync",
                "Extended Audio Recording",
                "Priority Support",
                "Ad-Free Experience"
            ]
        )
        
        static let yearly = SubscriptionTier(
            id: "com.neuroloop.app.premium.yearly",
            name: "Yearly Premium",
            price: 39.99,
            period: "year",
            features: [
                "All Monthly Premium Features",
                "33% Savings",
                "Exclusive Premium Themes",
                "Advanced Streak Analytics",
                "Custom Affirmation Categories",
                "Data Export & Backup",
                "Early Access to New Features"
            ]
        )
    }
}

// MARK: - Supporting Types
struct SubscriptionTier {
    let id: String
    let name: String
    let price: Decimal
    let period: String
    let features: [String]
} 
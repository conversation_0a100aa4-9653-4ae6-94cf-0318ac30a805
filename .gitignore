# macOS clutter
.DS_Store
.AppleDouble
.LSOverride

# Xcode build artifacts
DerivedData/
*.xcuserstate
*.xcuserdata/
*.xcworkspace/
*.xcodeproj/xcuserdata/
*.xcodeproj/project.xcworkspace/
*.xcodeproj/*.xcuserdatad
*.xcodeproj/xcshareddata/xcschemes/*

# SwiftPM & Build
.build/
.swiftpm/xcode/
Package.resolved

# Logs & test output
*.log
*.txt
test_output.log
test-results-ios.txt

# Screenshots, backups, misc
Screenshots/
*.bak/
*.tmp

# IDE settings
.vscode/

# Ignore SpecStory metadata (keep config if needed)
.specstory/.what-is-this.md

# KEEP the following AI files (do NOT ignore)
!.cursor-ai
!.augment-ai
!.cursor/
!.augment/


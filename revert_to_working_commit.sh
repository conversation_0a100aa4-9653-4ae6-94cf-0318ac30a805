#!/bin/bash

# Exit on error
set -e

echo "Reverting to a working commit..."

# Step 1: Stash any current changes
echo "Stashing current changes..."
git stash

# Step 2: Checkout the working commit
echo "Checking out commit 00237c3 (working preview on both devices)..."
git checkout 00237c3

# Step 3: Clean the build folder and derived data
echo "Cleaning build folder and derived data..."
rm -rf .build
rm -rf ~/Library/Developer/Xcode/DerivedData/*

# Step 4: Open the project in Xcode
echo "Opening the project in Xcode..."
open NeuroLoopApp.xcodeproj

echo "Done! The project has been reverted to a working commit."
echo "If you want to go back to the latest commit, run: git checkout main"

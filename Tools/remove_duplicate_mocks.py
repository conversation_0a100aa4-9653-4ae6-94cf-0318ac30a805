#!/usr/bin/env python3
"""
remove_duplicate_mocks.py

Automated script to remove duplicate mock class definitions from Swift test files.

Usage:
    python3 remove_duplicate_mocks.py <swift_file1> <swift_file2> ...

- Removes class definitions for known mocks (see MOCK_CLASSES below)
- Preserves unrelated code and custom structs
- Makes a backup of each file before modifying
- Prints a summary of changes
"""
import sys
import re
import shutil
from pathlib import Path

MOCK_CLASSES = [
    'MockAffirmationService',
    'MockRepetitionService',
    'MockStreakService',
    'MockNotificationService',
    'MockAudioService',
    'MockAffirmation',
]

def remove_mock_classes_from_file(filepath):
    with open(filepath, 'r') as f:
        code = f.read()

    original_code = code
    changes = []

    for mock in MOCK_CLASSES:
        # Regex to match final class or struct definition and its body (non-greedy)
        pattern = re.compile(r'(final\s+class|struct)\s+' + re.escape(mock) + r'\b[\s\S]*?^\}', re.MULTILINE)
        matches = list(pattern.finditer(code))
        for match in matches:
            start_line = code[:match.start()].count('\n') + 1
            end_line = code[:match.end()].count('\n') + 1
            changes.append(f"Removed {mock} at lines {start_line}-{end_line}")
        code = pattern.sub('', code)

    if code != original_code:
        # Backup original file
        backup_path = str(filepath) + '.bak'
        shutil.copy(filepath, backup_path)
        with open(filepath, 'w') as f:
            f.write(code)
        print(f"Updated {filepath} (backup at {backup_path}):")
        for change in changes:
            print('  -', change)
    else:
        print(f"No changes made to {filepath}")

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("Usage: python3 remove_duplicate_mocks.py <swift_file1> <swift_file2> ...")
        sys.exit(1)
    for file in sys.argv[1:]:
        remove_mock_classes_from_file(file) 
import SwiftUI
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

/**
 * DiagnosticApp
 *
 * A standalone diagnostic app for troubleshooting audio and repetition counting issues.
 * This app provides a simple interface to test the core functionality of the NeuroLoop app
 * without the complexity of the full app.
 */

@available(iOS 17.0, macOS 14.0, *)
struct DiagnosticApp: View {
    @State private var selectedTab = 0
    
    // Services
    private let audioService: AudioRecordingServiceProtocol
    private let repetitionService: RepetitionServiceProtocol
    private let affirmationService: AffirmationServiceProtocol
    
    // Test affirmation
    @State private var testAffirmation: AffirmationStub = AffirmationStub(
        text: "I am confident and capable in everything I do",
        currentRepetitions: 0
    )
    
    // View models
    private let originalViewModel: SpeakAffirmationViewModel
    private let fixedViewModel: FixedSpeakAffirmationViewModel
    
    init() {
        // Create services
        let audioService = AudioRecordingService()
        let affirmationService = PreviewAffirmationService()
        let baseRepetitionService = PreviewRepetitionService(affirmationService: affirmationService)
        let debugRepetitionService = DebugRepetitionService(wrappedService: baseRepetitionService)
        
        self.audioService = audioService
        self.affirmationService = affirmationService
        self.repetitionService = debugRepetitionService
        
        // Create view models
        self.originalViewModel = SpeakAffirmationViewModel(
            affirmation: testAffirmation,
            repetitionService: baseRepetitionService,
            audioService: audioService,
            affirmationService: affirmationService
        )
        
        self.fixedViewModel = FixedSpeakAffirmationViewModel(
            affirmation: testAffirmation,
            repetitionService: debugRepetitionService,
            audioService: audioService,
            affirmationService: affirmationService
        )
    }
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Diagnostic View
            DiagnosticView(
                audioService: audioService,
                repetitionService: repetitionService,
                affirmationService: affirmationService
            )
            .tabItem {
                Label("Diagnostics", systemImage: "stethoscope")
            }
            .tag(0)
            
            // Original Implementation
            OriginalImplementationView(viewModel: originalViewModel)
                .tabItem {
                    Label("Original", systemImage: "exclamationmark.triangle")
                }
                .tag(1)
            
            // Fixed Implementation
            FixedImplementationView(viewModel: fixedViewModel)
                .tabItem {
                    Label("Fixed", systemImage: "checkmark.circle")
                }
                .tag(2)
        }
        .onAppear {
            // Initialize view models
            Task {
                await originalViewModel.initialize()
                await fixedViewModel.initialize()
            }
        }
    }
}

// Original Implementation View
@available(iOS 17.0, macOS 14.0, *)
struct OriginalImplementationView: View {
    @ObservedObject var viewModel: SpeakAffirmationViewModel
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Original Implementation")
                .font(.largeTitle)
                .bold()
            
            // Affirmation card
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.blue)
                    .shadow(radius: 5)
                
                VStack {
                    Text("\"I am confident and capable in everything I do\"")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding()
                }
                .padding()
            }
            .frame(height: 150)
            .padding(.horizontal)
            
            // Repetition progress
            ZStack {
                Circle()
                    .stroke(Color.blue.opacity(0.3), lineWidth: 10)
                    .frame(width: 100, height: 100)
                
                Circle()
                    .trim(from: 0, to: CGFloat(viewModel.todayRepetitions) / 100.0)
                    .stroke(Color.blue, lineWidth: 10)
                    .frame(width: 100, height: 100)
                    .rotationEffect(.degrees(-90))
                
                VStack {
                    Text("\(viewModel.todayRepetitions)")
                        .font(.title)
                        .bold()
                    
                    Text("of 100")
                        .font(.caption)
                }
            }
            .frame(height: 120)
            
            // Recording button
            Button(action: {
                if viewModel.isRecording {
                    viewModel.stopRecording()
                } else {
                    viewModel.startRecording()
                }
            }) {
                ZStack {
                    Circle()
                        .fill(viewModel.isRecording ? Color.red : Color.white)
                        .frame(width: 70, height: 70)
                        .shadow(radius: 5)
                    
                    if viewModel.isRecording {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.white)
                            .frame(width: 20, height: 20)
                    }
                }
            }
            .padding()
            
            // Audio level
            if viewModel.isRecording {
                Text("Audio Level: \(Int(viewModel.currentAudioLevel * 100))%")
                
                // Audio level bar
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 20)
                        
                        Rectangle()
                            .fill(Color.blue)
                            .frame(width: geometry.size.width * CGFloat(viewModel.currentAudioLevel), height: 20)
                    }
                    .cornerRadius(10)
                }
                .frame(height: 20)
                .padding(.horizontal)
            }
            
            // Debug mode toggle
            Toggle("Debug Mode", isOn: $viewModel.debugBypassSpeechRecognition)
                .padding()
            
            // Reset button
            Button("Reset Counter") {
                viewModel.resetRepetitionCount()
            }
            .padding()
        }
        .padding()
    }
}

// Fixed Implementation View
@available(iOS 17.0, macOS 14.0, *)
struct FixedImplementationView: View {
    @ObservedObject var viewModel: FixedSpeakAffirmationViewModel
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Fixed Implementation")
                .font(.largeTitle)
                .bold()
            
            // Affirmation card
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.green)
                    .shadow(radius: 5)
                
                VStack {
                    Text("\"I am confident and capable in everything I do\"")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding()
                }
                .padding()
            }
            .frame(height: 150)
            .padding(.horizontal)
            
            // Repetition progress
            ZStack {
                Circle()
                    .stroke(Color.green.opacity(0.3), lineWidth: 10)
                    .frame(width: 100, height: 100)
                
                Circle()
                    .trim(from: 0, to: CGFloat(viewModel.todayRepetitions) / 100.0)
                    .stroke(Color.green, lineWidth: 10)
                    .frame(width: 100, height: 100)
                    .rotationEffect(.degrees(-90))
                
                VStack {
                    Text("\(viewModel.todayRepetitions)")
                        .font(.title)
                        .bold()
                    
                    Text("of 100")
                        .font(.caption)
                }
            }
            .frame(height: 120)
            
            // Recording button
            Button(action: {
                if viewModel.isRecording {
                    viewModel.stopRecording()
                } else {
                    viewModel.startRecording()
                }
            }) {
                ZStack {
                    Circle()
                        .fill(viewModel.isRecording ? Color.red : Color.white)
                        .frame(width: 70, height: 70)
                        .shadow(radius: 5)
                    
                    if viewModel.isRecording {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.white)
                            .frame(width: 20, height: 20)
                    }
                }
            }
            .padding()
            
            // Audio level
            if viewModel.isRecording {
                Text("Audio Level: \(Int(viewModel.currentAudioLevel * 100))%")
                
                // Audio level bar
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 20)
                        
                        Rectangle()
                            .fill(Color.green)
                            .frame(width: geometry.size.width * CGFloat(viewModel.currentAudioLevel), height: 20)
                    }
                    .cornerRadius(10)
                }
                .frame(height: 20)
                .padding(.horizontal)
            }
            
            // Debug mode toggle
            Toggle("Debug Mode", isOn: $viewModel.debugBypassSpeechRecognition)
                .padding()
            
            // Reset button
            Button("Reset Counter") {
                viewModel.resetRepetitionCount()
            }
            .padding()
        }
        .padding()
    }
}

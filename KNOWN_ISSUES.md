# Known Issues

## SwiftData & Swift 6.1 Actor Isolation Blocker

### Overview
Swift 6.1 introduces stricter concurrency enforcement, particularly around `@MainActor` isolation. SwiftData's macro-generated code for `@Model` types is not yet compatible with these new requirements, resulting in build-time errors that block conformance to the `PersistentModel` protocol.

### Common Error Messages
```swift
Main actor-isolated property persistentBackingData cannot satisfy nonisolated requirement from protocol PersistentModel
Main actor-isolated static property schemaMetadata cannot satisfy nonisolated requirement from protocol PersistentModel
Main actor-isolated initializer init(backingData:) cannot satisfy nonisolated requirement from protocol PersistentModel
Redundant conformance of [Model] to protocol PersistentModel
```

### Affected Files
- `Sources/NeuroLoopModels/Affirmation.swift`
- Any file using SwiftData `@Model` types with protocol conformance

### Technical Details
- SwiftData macros generate code with `@MainActor` isolation for properties and initializers.
- The `PersistentModel` protocol requires nonisolated access, which is not possible with the current macro output.
- This results in a build failure when attempting to conform a model to `PersistentModel`.

### Workarounds Attempted
- `@preconcurrency` extension for model conformance: **Ineffective**
- Adding `-swift-version 6.0` to build settings: **Ineffective**
- No known workaround resolves the issue as of May 2025.

### Current Status (as of May 2025)
| Area         | Status                                                      |
|:------------ |:-----------------------------------------------------------|
| Swift 6.1    | ✅ Released with stricter actor isolation checks           |
| SwiftData    | 🚧 Not yet updated to support these checks                 |
| Workarounds  | ❌ All known workarounds ineffective                       |
| Apple Status | 📣 Reported in Swift forums and radar                      |
| Fix ETA      | 🔜 Pending future Xcode/SwiftData update                   |

### Next Steps
- Consider downgrading to Xcode 15.3 for development if feasible.
- Evaluate if a temporary stub implementation is possible for affected features.
- Decide if App Store submission should be delayed until an official fix is available.

> **Note:** This is a known issue awaiting an official fix from Apple. Track updates in Swift forums and Xcode release notes.

## Temporary Solution: Downgrade to Xcode 15.3 for Development

### Why This Approach Was Chosen
Swift 6.1 introduces stricter actor isolation checks that are not yet supported by SwiftData's macro-generated code. This results in blocking build errors for any project using SwiftData models with protocol conformance. As a temporary measure, the team has decided to downgrade to Xcode 15.3, which uses Swift 6.0 and does not enforce these stricter checks. This allows development to continue while we await an official fix from Apple.

> **Note:** This is a temporary solution. The team should switch back to the latest Xcode version as soon as SwiftData is updated to support Swift 6.1 actor isolation.

---

### Step-by-Step Setup Instructions

#### 1. Download and Install Xcode 15.3
- Download Xcode 15.3 from the [Apple Developer Downloads](https://developer.apple.com/download/all/) page (Apple ID required).
- Open the downloaded `.xip` file to extract the app.
- Move the extracted app to your `/Applications` folder.
- Rename the app to `Xcode 15.3.app` to distinguish it from other versions.

#### 2. Configure Your Environment to Use Xcode 15.3
- For local development, open your project with `Xcode 15.3.app`.
- In Terminal, set the active developer directory:

```bash
sudo xcode-select -s /Applications/Xcode\ 15.3.app/Contents/Developer
```
- In Xcode, go to **Preferences > Locations** and set the Command Line Tools to Xcode 15.3.

#### 3. Managing Multiple Xcode Versions
- You can keep multiple versions of Xcode in `/Applications` (e.g., `Xcode.app`, `Xcode 15.3.app`).
- Use `xcode-select` to switch between them as needed.
- Always verify which version is active before building or running scripts.

#### 4. Troubleshooting Tips
- If you see errors about missing simulators, open Xcode 15.3 and install the required simulators via **Preferences > Components**.
- If you get permission errors, ensure Xcode 15.3 is in `/Applications` and you have run it at least once.
- If you see build errors about Swift version, clean the build folder (**Shift+Cmd+K**) and rebuild.

---

### Verification Process

#### 1. Verify Xcode Version
- In Terminal, run:

```bash
xcodebuild -version
```
- Output should include:
  - `Xcode 15.3`
  - `Build version ...`

#### 2. Check Swift Version
- In Terminal, run:

```bash
swift --version
```
- Output should include:
  - `Apple Swift version 6.0`

#### 3. Confirm Build Succeeds
- Open the project in Xcode 15.3.
- Clean the build folder (**Shift+Cmd+K**).
- Build the project (**Cmd+B**).
- Ensure there are no SwiftData actor isolation errors.
- Run the app and verify SwiftData features work as expected.

---

> **Reminder:** This is a temporary workaround. Monitor Swift and Xcode release notes for updates, and plan to migrate back to the latest Xcode as soon as SwiftData is updated.

## Temporary Solution: In-Memory SwiftData Stubbing (MemoryStorageService)

### Why This Approach Was Chosen
With Swift 6.1, stricter actor isolation checks broke compatibility with SwiftData's macro-generated code. Downgrading Xcode was not always feasible, so a temporary in-memory stub was implemented to allow continued development and testing while maintaining the app's core functionality.

### Architecture Overview
- **DTOs:** Plain Swift structs mirror all SwiftData models, conforming to the same protocols but are `Sendable` and actor-safe.
- **MemoryStorageService:** An in-memory, actor-isolated service that mimics the interface of the SwiftData repository. Stores DTOs in arrays/collections.
- **Feature Flag:** The `USING_SWIFTDATA_STUB` constant in `ServiceFactory.swift` toggles between the stub and real SwiftData implementation.
- **Protocol Conformance:** All repositories and ViewModels use protocols, so switching implementations is seamless.

### How to Toggle Implementations
To switch between the in-memory stub and SwiftData:
1. Open `Sources/NeuroLoopCore/Services/ServiceFactory.swift`.
2. Set the `USING_SWIFTDATA_STUB` constant:
   ```swift
   let USING_SWIFTDATA_STUB = true // Use in-memory stub
   // or
   let USING_SWIFTDATA_STUB = false // Use real SwiftData
   ```
3. Rebuild the app. All repositories and ViewModels will use the selected implementation.

### Limitations of the Stub
- Data is not persisted between app launches (in-memory only).
- Not suitable for production or real user data.
- Some advanced SwiftData features (e.g., predicates, relationships) may not be fully mimicked.
- Designed for development, testing, and UI validation only.

### Migration Plan: Returning to SwiftData
When Apple releases a fix for SwiftData/Swift 6.1:
1. Set `USING_SWIFTDATA_STUB = false` in `ServiceFactory.swift`.
2. Remove or archive the `MemoryStorageService` and DTOs if no longer needed.
3. Test all app flows with real SwiftData models.
4. Review and update any code that references DTOs or stub-specific logic.
5. Use the testing checklist below to validate the migration.

#### Post-Migration Testing Checklist
- [ ] CRUD operations work with real SwiftData
- [ ] UI updates correctly with SwiftData-backed data
- [ ] Data persists between launches
- [ ] All advanced features (sorting, filtering, relationships) function as expected
- [ ] No references to stub/DTOs remain in production code

#### Rollback Procedure
If migration to SwiftData fails:
- Revert `USING_SWIFTDATA_STUB` to `true` to use the in-memory stub
- Investigate and resolve issues before retrying migration

> **Note:** Keep this documentation up to date as SwiftData and Swift evolve. Remove the stub only after confirming full compatibility and stability with the official SwiftData fix. 
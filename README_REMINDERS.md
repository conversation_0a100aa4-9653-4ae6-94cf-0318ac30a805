# 🧠 NeuroLoopApp Daily Workflow Reminder

## Quick Start Guide

👣 STEP 1: Start with AUGMENT
1. Open TASKS.md to check current status
2. Ask Augment: "What should I do next based on the current TASKS.md?"
3. Get task breakdown and architectural guidance

📩 Then ask Augment:  
"Can you write a task brief for Cursor.ai so I can copy it over?"

👨‍💻 STEP 2: Switch to CURSOR
1. Paste the task brief from Augment
2. Let Cursor implement the feature
3. Follow implementation guidelines from .cursor-ai

✅ When Cursor is done:  
1. Ask <PERSON><PERSON><PERSON>: "What should I ask Augment to do next?"
2. Get handoff notes and next steps
3. Update TASKS.md with progress

## Important Files
- 📋 TASKS.md - Current tasks and handoff status
- 🔧 .cursor-ai - Cursor's implementation guidelines
- 🏗️ .augment-ai - Augment's planning guidelines

## Best Practices
1. Always check TASKS.md first
2. Follow the handoff protocol
3. Update task status immediately
4. Keep implementation notes clear
5. Document architectural decisions

## Common Commands
```bash
# Start with Augment
"Based on TASKS.md, what's the next step?"

# Get task brief
"Can you write a task brief for Cursor.ai?"

# Switch to <PERSON>urs<PERSON>
"Here's the task brief from Augment: [paste]"

# When done
"What should I ask Aug<PERSON> to do next?"
```

## Troubleshooting
- If stuck, check TASKS.md for current status
- Review last handoff notes
- Verify task requirements are clear
- Check for any blockers or issues 
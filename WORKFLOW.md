# NeuroLoop App Development Workflow

## 🎯 Purpose
This document serves as the central coordination point between Cursor.ai and Augment AI for the NeuroLoop app development. It contains workflow rules, templates, and task tracking to ensure smooth collaboration between both AI assistants.

## 🔄 Workflow Rules

### 1. Task Management
- All tasks must be documented in this file
- Each task must have a clear status indicator
- Tasks should be atomic and implementation-ready
- Include handoff notes between assistants

### 2. Role Definitions

#### Cursor.ai (Code Engineer)
- Primary responsibility: Implementation and debugging
- Focus areas:
  - Writing and modifying code
  - Refactoring
  - Testing
  - Debugging
  - Code optimization

#### Augment AI (Project Strategist)
- Primary responsibility: Planning and architecture
- Focus areas:
  - Feature breakdown
  - Architecture decisions
  - Task planning
  - Progress tracking
  - Documentation

### 3. Communication Protocol
- Always include a handoff summary when switching between assistants
- Document any assumptions or decisions made
- Keep implementation notes clear and concise
- Update task status immediately after completion

## 📋 Task Tracking

### Current Tasks
1. ⏳ [Task Name] - [Assigned to: Cursor/Augment]
   - Description: [Brief description]
   - Status: [In Progress/Completed]
   - Last Update: [Date]
   - Notes: [Any relevant notes]

### Completed Tasks
1. ✅ [Task Name] - [Completed by: Cursor/Augment]
   - Completion Date: [Date]
   - Summary: [Brief summary of what was done]

## 📝 Templates

### Cursor.ai Handoff Template
```markdown
# Cursor.ai Handoff

Last Completed:
- [Brief description of what was just completed]

Next Steps for Augment:
- [Clear instructions for what Augment should do next]

Technical Notes:
- [Any important technical details or decisions made]
```

### Augment AI Handoff Template
```markdown
# Augment AI Handoff

Last Completed:
- [Brief description of what was just completed]

Next Steps for Cursor:
- [Clear instructions for what Cursor should do next]

Architectural Notes:
- [Any important architectural decisions or considerations]
```

## 🔍 Current Status
[Update this section with the current project status and next steps]

## 📌 Best Practices
1. Always check this file before starting a new task
2. Update task status immediately after completion
3. Include clear handoff notes when switching between assistants
4. Document any important decisions or assumptions
5. Keep implementation notes concise but informative

## 🚀 Getting Started
1. Review the current tasks and status
2. Check the last handoff notes
3. Follow the appropriate template for your role
4. Update this document as you progress 
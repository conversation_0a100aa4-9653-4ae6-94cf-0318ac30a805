import AVFoundation
import Combine
import Foundation
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopModels
import NeuroLoopTypes
import OSLog
import Speech
import Swift<PERSON>

/**
 * FixedSpeakAffirmationViewModel
 *
 * A modified version of SpeakAffirmationViewModel that addresses issues with
 * Debug Mode and repetition counting. This version ensures that:
 *
 * 1. Debug Mode properly bypasses speech recognition
 * 2. The repetition counter increments correctly
 * 3. Audio levels are properly detected and displayed
 */

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public class FixedSpeakAffirmationViewModel: ObservableObject {
    // MARK: - Published Properties

    @Published private(set) var affirmation: any AffirmationProtocol
    @Published public var todayRepetitions: Int = 0
    @Published private(set) var totalRepetitions: Int = 100
    @Published private(set) var currentDay: Int = 1
    @Published private(set) var isRecording: Bool = false
    @Published private(set) var isPlaying: Bool = false
    @Published private(set) var spokenText: String = ""
    @Published public var partialRecognitionText: String = ""
    @Published var alertItem: AlertItem?
    @Published public var currentAudioLevel: Double = 0.0

    // Debug flag to bypass speech recognition for testing
    @Published public var debugBypassSpeechRecognition: Bool = false {
        didSet {
            print(
                "FixedSpeakAffirmationViewModel: Debug bypass mode set to \(debugBypassSpeechRecognition)"
            )
        }
    }

    // MARK: - Properties

    private let repetitionService: RepetitionServiceProtocol
    public let audioService: AudioRecordingServiceProtocol
    private let affirmationService: AffirmationServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    // Speech Recognition Properties
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()

    // Audio session manager
    private let audioSessionManager = AudioSessionManager.shared

    // MARK: - Initialization

    public init(
        affirmation: any AffirmationProtocol,
        repetitionService: RepetitionServiceProtocol,
        audioService: AudioRecordingServiceProtocol,
        affirmationService: AffirmationServiceProtocol
    ) {
        self.affirmation = affirmation
        self.repetitionService = repetitionService
        self.audioService = audioService
        self.affirmationService = affirmationService

        // Print service types for debugging
        print("FixedSpeakAffirmationViewModel: Initialized with services:")
        print("- Repetition Service: \(type(of: repetitionService))")
        print("- Audio Service: \(type(of: audioService))")
        print("- Affirmation Service: \(type(of: affirmationService))")
    }

    public func initialize() async {
        // Initialize with current progress
        await loadProgress()

        // Check microphone permission status on initialization
        let permissionGranted = await audioSessionManager.checkMicrophonePermission()
        print("FixedSpeakAffirmationViewModel: Microphone permission granted: \(permissionGranted)")
    }

    // MARK: - Public Methods

    /// Starts recording the user's spoken affirmation
    public func startRecording() {
        print("FixedSpeakAffirmationViewModel: startRecording called")

        Task {
            do {
                // Clear previous spoken text
                spokenText = ""
                partialRecognitionText = ""

                // Start recording
                try await audioService.startRecording()
                isRecording = true

                // Start monitoring audio levels
                startMonitoringAudioLevels()

                // Start speech recognition if not in debug mode
                if !debugBypassSpeechRecognition {
                    await startSpeechRecognition()
                } else {
                    print(
                        "FixedSpeakAffirmationViewModel: Debug mode enabled, skipping speech recognition"
                    )
                }
            } catch {
                print(
                    "FixedSpeakAffirmationViewModel: Error starting recording: \(error.localizedDescription)"
                )
                isRecording = false
                alertItem = AlertItem(
                    title: "Recording Error",
                    message: "Could not start recording: \(error.localizedDescription)"
                )
            }
        }
    }

    /// Monitors audio levels to check if microphone is picking up sound
    private func startMonitoringAudioLevels() {
        Task {
            while isRecording {
                // Get the current audio level
                let averagePower = audioService.recordingPower

                // Convert dB to linear scale for visualization (normalized between 0 and 1)
                let normalizedPower = max(0.0, min(1.0, (averagePower + 60) / 60))

                // Log audio levels periodically
                print(
                    "FixedSpeakAffirmationViewModel: Audio level: \(averagePower) dB, normalized: \(normalizedPower)"
                )

                // Update the audio level for visualization
                await MainActor.run {
                    self.currentAudioLevel = normalizedPower
                }

                // Check more frequently for smoother visualization
                try? await Task.sleep(nanoseconds: 100_000_000)  // 0.1 seconds
            }
        }
    }

    /// Stops recording and processes the spoken affirmation
    public func stopRecording() {
        print("FixedSpeakAffirmationViewModel: stopRecording called")

        Task {
            // Stop recording
            do {
                let recordingURL = try await audioService.stopRecording()
                print("FixedSpeakAffirmationViewModel: Recording stopped, URL: \(recordingURL)")

                // Stop speech recognition if not in debug mode
                if !debugBypassSpeechRecognition {
                    await stopSpeechRecognition()

                    // Add a small delay to ensure we have the final recognition result
                    try? await Task.sleep(nanoseconds: 500_000_000)  // 0.5 seconds

                    print("FixedSpeakAffirmationViewModel: Final spoken text: '\(spokenText)'")
                    print("FixedSpeakAffirmationViewModel: Target text: '\(affirmation.text)'")
                }

                isRecording = false

                if !spokenText.isEmpty {
                    // Verify the spoken text matches the affirmation
                    let verificationResult = verifyAffirmation(
                        spokenText: spokenText,
                        targetText: affirmation.text
                    )

                    if verificationResult.success {
                        print(
                            "FixedSpeakAffirmationViewModel: Verification successful, recording repetition"
                        )
                        await recordRepetition()
                    } else {
                        print(
                            "FixedSpeakAffirmationViewModel: Verification failed with similarity \(verificationResult.similarity), not counting repetition"
                        )
                        alertItem = AlertItem(
                            title: "Not Quite Right",
                            message: "Please try again and speak the complete affirmation clearly."
                        )
                    }
                } else {
                    print("FixedSpeakAffirmationViewModel: No speech recognized")
                    alertItem = AlertItem(
                        title: "No Speech Detected",
                        message:
                            "We couldn't recognize what you said. Please try again and speak clearly."
                    )
                }
            } catch {
                print(
                    "FixedSpeakAffirmationViewModel: Error stopping recording: \(error.localizedDescription)"
                )
                isRecording = false
                alertItem = AlertItem(
                    title: "Recording Error",
                    message: "Error stopping recording: \(error.localizedDescription)"
                )
            }
        }
    }

    /// Records a repetition for the current affirmation
    private func recordRepetition() async {
        print("FixedSpeakAffirmationViewModel: Recording repetition")

        do {
            // Store the current repetition count for verification
            let beforeCount = affirmation.currentRepetitions
            print("FixedSpeakAffirmationViewModel: Before repetition - count: \(beforeCount)")

            // Record the repetition
            let result = try await repetitionService.recordRepetition(for: affirmation)

            // Update the affirmation with the result
            affirmation = result.updatedAffirmation

            // Get the updated repetition count
            let afterCount = affirmation.currentRepetitions
            print(
                "FixedSpeakAffirmationViewModel: After repetition - count: \(afterCount), difference: \(afterCount - beforeCount)"
            )

            // Update the UI
            todayRepetitions = afterCount

            // Force UI update
            objectWillChange.send()

            // Show success message
            alertItem = AlertItem(
                title: "Great Job!",
                message: "Repetition count increased to \(todayRepetitions) out of 100."
            )
        } catch {
            print(
                "FixedSpeakAffirmationViewModel: Error recording repetition: \(error.localizedDescription)"
            )
            alertItem = AlertItem(
                title: "Error",
                message: "Could not record repetition: \(error.localizedDescription)"
            )
        }
    }

    /// Resets the repetition count to zero for testing purposes
    public func resetRepetitionCount() {
        todayRepetitions = 0
        print("FixedSpeakAffirmationViewModel: Reset repetition count to 0")
        objectWillChange.send()
    }

    // MARK: - Speech Recognition Methods

    private func startSpeechRecognition() async {
        print("FixedSpeakAffirmationViewModel: Starting speech recognition")

        // Initialize speech recognizer
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))

        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("FixedSpeakAffirmationViewModel: Speech recognizer not available")
            return
        }

        // Configure audio session
        let audioSessionSuccess = await configureAudioSession(active: true)
        if !audioSessionSuccess {
            print("FixedSpeakAffirmationViewModel: Failed to configure audio session")
            return
        }

        // Create recognition request
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            print("FixedSpeakAffirmationViewModel: Failed to create recognition request")
            return
        }

        // Configure recognition request
        recognitionRequest.shouldReportPartialResults = true

        // Set contextual strings to help with recognition
        recognitionRequest.contextualStrings = [affirmation.text]

        // Install tap on audio engine input
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) {
            [weak self] buffer, _ in
            self?.recognitionRequest?.append(buffer)
        }

        // Start recognition task
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) {
            [weak self] result, error in
            guard let self = self else { return }

            if let error = error {
                print(
                    "FixedSpeakAffirmationViewModel: Speech recognition error: \(error.localizedDescription)"
                )
                return
            }

            if let result = result {
                let recognizedText = result.bestTranscription.formattedString
                print("FixedSpeakAffirmationViewModel: Recognized text: \(recognizedText)")

                Task { @MainActor in
                    self.spokenText = recognizedText
                    self.partialRecognitionText = recognizedText
                }
            }
        }

        // Start audio engine
        do {
            try audioEngine.start()
            print("FixedSpeakAffirmationViewModel: Audio engine started")
        } catch {
            print(
                "FixedSpeakAffirmationViewModel: Failed to start audio engine: \(error.localizedDescription)"
            )
        }
    }

    private func stopSpeechRecognition() async {
        print("FixedSpeakAffirmationViewModel: Stopping speech recognition")

        // Cancel recognition task
        recognitionTask?.cancel()
        recognitionTask = nil

        // End recognition request
        recognitionRequest?.endAudio()
        recognitionRequest = nil

        // Stop audio engine
        if audioEngine.isRunning {
            audioEngine.stop()
            audioEngine.inputNode.removeTap(onBus: 0)
        }

        // Reset audio engine
        audioEngine.reset()

        // Deactivate audio session
        _ = await configureAudioSession(active: false)
    }

    /// Configures the audio session for recording
    private func configureAudioSession(active: Bool) async -> Bool {
        if active {
            return await audioSessionManager.activateSession(
                category: .playAndRecord,
                mode: .spokenAudio,
                options: [.allowBluetooth, .defaultToSpeaker]
            )
        } else {
            return await audioSessionManager.deactivateSession()
        }
    }

    // MARK: - Private Methods

    /// Loads the current progress for the affirmation
    private func loadProgress() async {
        let progress = repetitionService.getProgress(for: affirmation)

        await MainActor.run {
            todayRepetitions = progress.currentRepetitions
            totalRepetitions = progress.totalRepetitions
            currentDay = progress.currentDay

            print(
                "FixedSpeakAffirmationViewModel: Loaded progress - repetitions: \(todayRepetitions)/\(totalRepetitions), day: \(currentDay)"
            )
        }
    }

    /// Verifies if the spoken affirmation matches the target affirmation text
    private func verifyAffirmation(spokenText: String, targetText: String) -> (
        success: Bool, similarity: Double
    ) {
        // Simple implementation for testing
        let normalizedSpoken = spokenText.lowercased().trimmingCharacters(
            in: .whitespacesAndNewlines)
        let normalizedTarget = targetText.lowercased().trimmingCharacters(
            in: .whitespacesAndNewlines)

        let similarity = calculateSimilarity(between: normalizedSpoken, and: normalizedTarget)
        let success = similarity >= 0.6  // Lowered to 60% similarity threshold

        print(
            "FixedSpeakAffirmationViewModel: Verification - similarity: \(similarity), success: \(success)"
        )
        print("FixedSpeakAffirmationViewModel: Normalized spoken: '\(normalizedSpoken)'")
        print("FixedSpeakAffirmationViewModel: Normalized target: '\(normalizedTarget)'")
        return (success, similarity)
    }

    /// Calculates the similarity between two strings (0.0 to 1.0)
    private func calculateSimilarity(between s1: String, and s2: String) -> Double {
        // Simple word overlap calculation
        let words1 = Set(s1.components(separatedBy: .whitespacesAndNewlines))
        let words2 = Set(s2.components(separatedBy: .whitespacesAndNewlines))

        let intersection = words1.intersection(words2).count
        let union = words1.union(words2).count

        return union > 0 ? Double(intersection) / Double(union) : 0.0
    }
}

# NeuroLoop Diagnostic Tools

This package contains diagnostic tools to help troubleshoot and fix issues with the repetition counter in the NeuroLoop app.

## The Issue

The repetition counter does not increase from 0 to 1 when speaking affirmations, even when:
- The microphone test works correctly
- Debug Mode is enabled
- Audio is being recorded properly

## Root Cause Analysis

After analyzing the code, we've identified several potential issues:

1. **Audio Session Configuration Mismatch**:
   - The microphone test uses a different audio session configuration than the actual recording process.
   - The test creates a temporary recorder with its own settings, while the main recording uses a different configuration.

2. **Debug Mode Implementation Issue**:
   - The Debug Mode toggle is correctly implemented in the code, but there appears to be an issue with how it's connected to the repetition service.
   - Even though Debug Mode should bypass speech recognition verification, the repetition counter isn't incrementing.

3. **Audio Level Visualization Issue**:
   - The app shows 0% at -120 dB during recording, which indicates no audio is being detected by the recording system.
   - This is inconsistent with the microphone test showing -42 dB.

4. **Repetition Service Connection Issue**:
   - The most likely issue is that the repetition service isn't properly receiving or processing the repetition increment when in Debug Mode.

## How to Use These Tools

### 1. Diagnostic View

The Diagnostic View provides detailed information about the current state of the app and allows testing various components in isolation:

- **Service Information**: Shows the types of services being used and their current state.
- **Audio Testing**: Tests the microphone and displays audio levels during recording.
- **Repetition Testing**: Tests the repetition service by directly incrementing the counter.

### 2. Original Implementation

This tab shows the original implementation with the issue:

- The Debug Mode toggle is present but doesn't properly bypass speech recognition.
- The repetition counter doesn't increment even when Debug Mode is enabled.
- Audio levels may not be displayed correctly.

### 3. Fixed Implementation

This tab shows the fixed implementation:

- The Debug Mode toggle properly bypasses speech recognition.
- The repetition counter increments correctly when Debug Mode is enabled.
- Audio levels are displayed correctly.

## How to Fix the Issue in Your App

1. **Use the Debug Repetition Service**:
   - The `DebugRepetitionService` wraps your existing repetition service and ensures that repetitions are always counted correctly.
   - This is a temporary solution to help diagnose the issue.

2. **Fix the SpeakAffirmationViewModel**:
   - Use the `FixedSpeakAffirmationViewModel` as a reference for the correct implementation.
   - Key changes:
     - Ensure Debug Mode properly bypasses speech recognition verification
     - Fix the audio session configuration to match the microphone test
     - Ensure the repetition service is correctly incrementing the counter

3. **Update the Audio Session Configuration**:
   - Make sure the audio session configuration for recording matches the configuration used in the microphone test.
   - Use `.playAndRecord` category and `.spokenAudio` mode for best results.

## Specific Code Changes

1. **In SpeakAffirmationViewModel.swift**:

   - Ensure Debug Mode properly bypasses speech recognition:
   ```swift
   if debugBypassSpeechRecognition {
       // Skip speech recognition verification
       // Directly record the repetition
       let result = try await repetitionService.recordRepetition(for: affirmation)
       affirmation = result.updatedAffirmation
       todayRepetitions = affirmation.currentRepetitions
   }
   ```

   - Fix the audio session configuration:
   ```swift
   private func configureAudioSession(active: Bool) async -> Bool {
       if active {
           return await audioSessionManager.activateSession(
               category: .playAndRecord,
               mode: .spokenAudio,
               options: [.allowBluetooth, .defaultToSpeaker]
           )
       } else {
           return await audioSessionManager.deactivateSession()
       }
   }
   ```

2. **Add Debug Logging**:
   - Add debug logging to track the repetition count before and after recording a repetition:
   ```swift
   print("Before repetition - count: \(affirmation.currentRepetitions)")
   let result = try await repetitionService.recordRepetition(for: affirmation)
   print("After repetition - count: \(result.updatedAffirmation.currentRepetitions)")
   ```

## Testing Your Fix

1. Enable Debug Mode in the app.
2. Record an affirmation (just tap the record button and then stop).
3. Verify that the repetition counter increments from 0 to 1.
4. Disable Debug Mode and test with actual speech recognition.

## Additional Resources

- `DebugRepetitionService.swift`: A special implementation of the repetition service that always succeeds and increments the counter.
- `DiagnosticView.swift`: A view for testing the microphone and repetition service in isolation.
- `FixedSpeakAffirmationViewModel.swift`: A fixed version of the SpeakAffirmationViewModel that addresses the issues.

//
//  NeuroLoopAppTestsLaunchTests.swift
//  NeuroLoopAppTests
//
//  Created by W Dedewo on 05/05/2025.
//

import XCTest

final class NeuroLoopAppTestsLaunchTests: XCTestCase {

    override class var runsForEachTargetApplicationUIConfiguration: Bool {
        true
    }

    override func setUpWithError() throws {
        continueAfterFailure = false
    }

    @MainActor
    func testLaunch() throws {
        let app = XCUIApplication()
        app.launch()

        // Wait for app to be ready
        let expectation = XCTNSPredicateExpectation(
            predicate: NSPredicate(format: "exists == true"),
            object: app.windows.firstMatch
        )
        _ = XCTWaiter.wait(for: [expectation], timeout: 5.0)

        // Take screenshot after app is ready
        let attachment = XCTAttachment(screenshot: app.screenshot())
        attachment.name = "Launch Screen"
        attachment.lifetime = .keepAlways
        add(attachment)
    }
}

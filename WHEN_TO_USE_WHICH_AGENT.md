# NeuroLoopApp: When to Use Augment vs <PERSON><PERSON>or

Use this cheat sheet to decide whether a task should go to <PERSON><PERSON> (planner) or <PERSON><PERSON><PERSON> (implementer).

| Situation                                              | Use **Augment** 🧠 | Use **Cursor** 👨‍💻 |
|--------------------------------------------------------|------------------|--------------------|
| Breaking down a feature into steps                     | ✅ Yes           | ❌ No              |
| Designing architecture (e.g. modules, view models)     | ✅ Yes           | ❌ No              |
| Debugging build errors caused by **module structure**  | ✅ First         | ✅ Then            |
| Fixing import errors, unused variables, syntax issues  | ❌ No            | ✅ Yes             |
| Implementing SwiftUI views, services, or models        | ❌ No            | ✅ Yes             |
| Writing or editing Swift code                          | ❌ Never         | ✅ Always          |
| Updating or writing `TASKS.md`                         | ✅ Yes           | ✅ Yes (after task)|
| Making changes to `.swift` files                       | ❌ No            | ✅ Yes             |
| Updating `IMPLEMENTATION_PLAN.md`                      | ✅ Yes           | ❌ No              |
| Creating new Swift files                               | ❌ No            | ✅ Yes (when asked)|

---

🧠 Augment Reminders:
- Only plan and update `TASKS.md`
- Never write or edit Swift files
- If a build issue arises, analyze and hand off the fix to Cursor

👨‍💻 Cursor Reminders:
- Only implement, refactor, and debug code
- Always mark task completion and hand off to Augment via `TASKS.md`
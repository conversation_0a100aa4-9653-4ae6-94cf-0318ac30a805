#!/bin/bash

# Exit on error
set -e

echo "Fixing Xcode project integration for Collections package..."

# Step 1: Clean the build folder
echo "Cleaning build folder..."
rm -rf .build

# Step 2: Reset package cache
echo "Resetting package cache..."
swift package reset

# Step 3: Resolve dependencies
echo "Resolving dependencies..."
swift package resolve

# Step 4: Update Xcode project
echo "Updating Xcode project..."
xcodebuild -resolvePackageDependencies

# Step 5: Clean Xcode project
echo "Cleaning Xcode project..."
xcodebuild clean

echo "Done! Now try opening the project in Xcode again."

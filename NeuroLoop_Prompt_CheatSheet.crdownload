# 🧠 NeuroLoopApp Agent Prompt Cheat Sheet

A quick-reference guide to the key prompts you can reuse with Augment and Cursor to streamline your dual-agent workflow.

---

## 🔍 **1. Visual Review Prompt** (Use with Cursor)
**Purpose:** Ask <PERSON>ursor to build, preview, or run the app so you can inspect the UI visually.

```
I’d like to visually review the current state of the app in the iOS Simulator (iPhone 16 Pro Max).

Here’s what I need:
1. ✅ Before doing anything, make sure Xcode is fully closed. Always restart Xcode after any major build failure or after modifying SPM, imports, or package structure.
2. Build the app using the Swift Package Manager setup (`Package.swift`)
3. Ensure the build runs cleanly in Xcode after restarting
4. Launch the app in the iOS Simulator (iPhone 16 Pro Max)
5. If a full build isn’t possible yet, generate SwiftUI previews for key views like `HomeView`, `ContentView`, `AffirmationCard`, and `ProgressIndicator`
6. Let me know how to test navigation and UI flows manually in the simulator
7. Report any build blockers, missing modules, or preview errors that prevent a visual review

Also confirm whether the simulator can be launched directly from this SPM setup or if we need a workaround like creating a minimal `.xcodeproj` just for testing.
```

---

## 🛠️ **2. Build Error Resolution Prompt** (Use with Cursor)
**Purpose:** When you encounter build errors like StoreKit or import issues.

```
The app is failing to build due to Swift-level implementation errors, mostly in `PremiumService.swift` and `LibraryViewModelProtocol.swift`.

Please fix the following issues:
- `'Transaction' has no member 'sync'`
- `'SubscriptionStatus' is ambiguous`
- `Product.SubscriptionInfo.Status?` has no members like `subscribed`, `inGracePeriod`, etc.
- Import conflict in `LibraryViewModelProtocol.swift`

These appear to be StoreKit 2 or visibility-related issues. Please correct the implementation and ensure it builds cleanly in Xcode using the SPM setup.
```

---

## 🔁 **3. Role Reset Prompt** (Use with either agent)
**Purpose:** Reinforce the dual-agent workflow rules and boundaries.

```
🧠 Reminder: We're using a dual-agent workflow. Please stick to your assigned role.

Refer to your respective rule files: `.augment-ai` for Augment and `.cursor-ai` for Cursor.

Augment:
- You are the architect and planner.
- Only update `TASKS.md` and `IMPLEMENTATION_PLAN.md`.
- Do NOT write or edit Swift code or `.swift` files.
- Do NOT create extra `.md` files — only use `TASKS.md` and `TASKS_ARCHIVE.md`.

Cursor:
- You are the implementation engineer.
- Only implement tasks defined by Augment in `TASKS.md`.
- You may read `IMPLEMENTATION_PLAN.md` for context, but NEVER modify it.
- Never plan features or restructure architecture.

📌 All coordination and task flow must happen through `TASKS.md`.
```

---

## 🚨 **4. Escalation Reminder** (Rule added to `.cursor-ai`)
**Purpose:** Automatically hand off to Augment if Cursor encounters architecture-level issues.

> Cursor now knows:
> - If it encounters architectural questions, design decisions, or scope creep — it must stop.
> - Cursor will now log these in `TASKS.md` with:
>
> ```
> 🟢 Handoff to Augment:
> - Issue encountered: [description]
> - Current status: [what Cursor was working on]
> - Notes: [reason for escalation]
> ```

---

## 📈 **5. Progress Request Prompt** (Use with Augment)
**Purpose:** Ask Augment to give you a high-level project completion summary.

```
Can you give me a progress report for the NeuroLoopApp?

Please include:
- A visual breakdown of completion status (e.g., percent done, progress bar)
- How much of the app is implemented vs. still pending
- Which milestones are complete, in progress, or not started
- An estimate of how close we are to being ready for full testing
- Format it cleanly in Markdown

Use `TASKS.md` and `IMPLEMENTATION_PLAN.md` as your source.
```

---

Keep this file handy and paste these prompts anytime you need quick alignment from either agent.
{"name": "xcode build server", "version": "0.2", "bspVersion": "2.0", "languages": ["c", "cpp", "objective-c", "objective-cpp", "swift"], "argv": ["/usr/local/bin/xcode-build-server"], "workspace": "/Users/<USER>/Desktop/NeuroLoopApp2025_Fresh/NeuroLoopApp.xcodeproj/project.xcworkspace", "build_root": "/Users/<USER>/Library/Developer/Xcode/DerivedData/NeuroLoopApp-akzromwvbrrxzneeqwekesxwyeri", "scheme": "NeuroLoopApp", "kind": "xcode"}
import Foundation
import NeuroLoopInterfaces
import NeuroLoopTypes

/**
 * DebugRepetitionService
 *
 * A special debug implementation of RepetitionServiceProtocol that always succeeds
 * and increments the repetition counter regardless of speech recognition.
 * This is used to diagnose issues with the repetition counting system.
 */

@available(iOS 17.0, macOS 14.0, *)
@MainActor
public final class DebugRepetitionService: RepetitionServiceProtocol, @unchecked Sendable {
    // MARK: - Private Properties
    
    private let wrappedService: RepetitionServiceProtocol
    private var debugRepetitionCount: [UUID: Int] = [:]
    
    // MARK: - Initialization
    
    public init(wrappedService: RepetitionServiceProtocol) {
        self.wrappedService = wrappedService
        print("DebugRepetitionService: Initialized with wrapped service of type \(type(of: wrappedService))")
    }
    
    // MARK: - Repetition Methods
    
    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws -> RepetitionResult {
        print("DebugRepetitionService: Recording repetition for affirmation \(affirmation.id)")
        
        // Get current count
        let currentCount = debugRepetitionCount[affirmation.id] ?? 0
        
        // Increment by 1
        debugRepetitionCount[affirmation.id] = currentCount + 1
        
        // Create a modified affirmation with the updated repetition count
        let updatedAffirmation = AffirmationStub(
            id: affirmation.id,
            text: affirmation.text,
            category: affirmation.category,
            recordingURL: affirmation.recordingURL,
            createdAt: affirmation.createdAt,
            updatedAt: Date(),
            currentCycleDay: affirmation.currentCycleDay,
            cycleStartDate: affirmation.cycleStartDate,
            completedCycles: affirmation.completedCycles,
            currentRepetitions: currentCount + 1, // Use the incremented count
            dailyProgress: affirmation.dailyProgress,
            lastRepetitionDate: Date(),
            energyLevel: affirmation.energyLevel,
            moodRating: affirmation.moodRating,
            notes: affirmation.notes,
            isFavorite: affirmation.isFavorite,
            playCount: affirmation.playCount,
            hasActiveCycle: affirmation.hasActiveCycle,
            isCurrentCycleComplete: false,
            todayProgress: Double(currentCount + 1) / 100.0,
            cycleProgress: affirmation.cycleProgress,
            hasTodayQuotaMet: (currentCount + 1) >= 100
        )
        
        print("DebugRepetitionService: Incremented repetition count from \(currentCount) to \(currentCount + 1)")
        
        return RepetitionResult(
            success: true,
            updatedAffirmation: updatedAffirmation,
            isQuotaMet: (currentCount + 1) >= 100,
            isCycleComplete: false
        )
    }
    
    // Forward all other methods to the wrapped service
    
    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return try await wrappedService.startCycle(for: affirmation)
    }
    
    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return try await wrappedService.startSession(for: affirmation)
    }
    
    public nonisolated func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        return MainActor.assumeIsolated {
            let currentCount = self.debugRepetitionCount[affirmation.id] ?? 0
            
            return ProgressInfo(
                todayProgress: Double(currentCount) / 100.0,
                cycleProgress: Double(affirmation.currentCycleDay) / 7.0,
                currentDay: affirmation.currentCycleDay,
                totalDays: 7,
                currentRepetitions: currentCount,
                totalRepetitions: 100,
                hasTodayQuotaMet: currentCount >= 100,
                isCycleComplete: affirmation.currentCycleDay >= 7 && currentCount >= 100,
                hasActiveCycle: affirmation.hasActiveCycle
            )
        }
    }
    
    public nonisolated func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        return wrappedService.getStreakInfo(for: affirmation)
    }
    
    public nonisolated func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool {
        return true // Always allow repetitions in debug mode
    }
    
    public nonisolated func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval? {
        return nil // No time restriction in debug mode
    }
    
    public nonisolated func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool {
        return wrappedService.isCycleBroken(for: affirmation)
    }
    
    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return try await wrappedService.restartBrokenCycle(for: affirmation)
    }
}

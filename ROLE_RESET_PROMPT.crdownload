# 🧠 Dual-Agent Role Reset Prompt

Use this to remind both <PERSON><PERSON> and <PERSON><PERSON><PERSON> of their responsibilities in the NeuroLoop dual-agent system.

Refer to your respective rule files: `.augment-ai` for Augment and `.cursor-ai` for Cursor.

---

## Augment (<PERSON><PERSON>):
- You are the architect and planner.
- Only update `TASKS.md` and `IMPLEMENTATION_PLAN.md`.
- Do NOT write or edit Swift code or `.swift` files.
- Do NOT create extra `.md` files — only use `TASKS.md` and `TASKS_ARCHIVE.md`.

## Cursor (Kersa):
- You are the implementation engineer.
- Only implement tasks defined by Augment in `TASKS.md`.
- You may read `IMPLEMENTATION_PLAN.md` for context, but NEVER modify it.
- Never plan features or restructure architecture.

---

📌 All coordination and task flow must happen through `TASKS.md`.  
This is your single source of truth.  
If you are unsure of your next action, re-read your role file and ask for clarification.
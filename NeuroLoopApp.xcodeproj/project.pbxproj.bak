// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		22884F2B2DC8F3340016AD7C /* NeuroLoopApp in Frameworks */ = {isa = PBXBuildFile; productRef = 22884F2A2DC8F3340016AD7C /* NeuroLoopApp */; };
		22884F2D2DC8F3340016AD7C /* NeuroLoopCore in Frameworks */ = {isa = PBXBuildFile; productRef = 22884F2C2DC8F3340016AD7C /* NeuroLoopCore */; };
		22884F2F2DC8F3340016AD7C /* NeuroLoopInterfaces in Frameworks */ = {isa = PBXBuildFile; productRef = 22884F2E2DC8F3340016AD7C /* NeuroLoopInterfaces */; };
		22884F312DC8F3340016AD7C /* NeuroLoopModels in Frameworks */ = {isa = PBXBuildFile; productRef = 22884F302DC8F3340016AD7C /* NeuroLoopModels */; };
		22EC92442DCAD62100285A00 /* NeuroLoopShared in Frameworks */ = {isa = PBXBuildFile; productRef = 22EC92432DCAD62100285A00 /* NeuroLoopShared */; };
		22EC92462DCAD80800285A00 /* NeuroLoopUI in Frameworks */ = {isa = PBXBuildFile; productRef = 22EC92452DCAD80800285A00 /* NeuroLoopUI */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		221E29112DC9327A0088A808 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 229AA0822DC8F1970059965F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 229AA0892DC8F1970059965F;
			remoteInfo = NeuroLoopApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		22EC923F2DCAD2E300285A00 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		221E290B2DC9327A0088A808 /* NeuroLoopAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NeuroLoopAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		229AA08A2DC8F1970059965F /* NeuroLoopApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NeuroLoopApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		22884F362DC90B9B0016AD7C /* Exceptions for "NeuroLoopApp" folder in "NeuroLoopApp" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 229AA0892DC8F1970059965F /* NeuroLoopApp */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		221E28FF2DC932190088A808 /* NeuroLoopAppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NeuroLoopAppTests;
			sourceTree = "<group>";
		};
		221E290C2DC9327A0088A808 /* NeuroLoopAppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NeuroLoopAppTests;
			sourceTree = "<group>";
		};
		229AA08C2DC8F1970059965F /* NeuroLoopApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				22884F362DC90B9B0016AD7C /* Exceptions for "NeuroLoopApp" folder in "NeuroLoopApp" target */,
			);
			path = NeuroLoopApp;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		221E29082DC9327A0088A808 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		229AA0872DC8F1970059965F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22884F2B2DC8F3340016AD7C /* NeuroLoopApp in Frameworks */,
				22884F2D2DC8F3340016AD7C /* NeuroLoopCore in Frameworks */,
				22EC92462DCAD80800285A00 /* NeuroLoopUI in Frameworks */,
				22EC92442DCAD62100285A00 /* NeuroLoopShared in Frameworks */,
				22884F312DC8F3340016AD7C /* NeuroLoopModels in Frameworks */,
				22884F2F2DC8F3340016AD7C /* NeuroLoopInterfaces in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		229AA0812DC8F1970059965F = {
			isa = PBXGroup;
			children = (
				229AA08C2DC8F1970059965F /* NeuroLoopApp */,
				221E28FF2DC932190088A808 /* NeuroLoopAppTests */,
				221E290C2DC9327A0088A808 /* NeuroLoopAppTests */,
				22EC92402DCAD45A00285A00 /* Frameworks */,
				229AA08B2DC8F1970059965F /* Products */,
			);
			sourceTree = "<group>";
		};
		229AA08B2DC8F1970059965F /* Products */ = {
			isa = PBXGroup;
			children = (
				229AA08A2DC8F1970059965F /* NeuroLoopApp.app */,
				221E290B2DC9327A0088A808 /* NeuroLoopAppTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		22EC92402DCAD45A00285A00 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		221E290A2DC9327A0088A808 /* NeuroLoopAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 221E29132DC9327A0088A808 /* Build configuration list for PBXNativeTarget "NeuroLoopAppTests" */;
			buildPhases = (
				221E29072DC9327A0088A808 /* Sources */,
				221E29082DC9327A0088A808 /* Frameworks */,
				221E29092DC9327A0088A808 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				221E29122DC9327A0088A808 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				221E290C2DC9327A0088A808 /* NeuroLoopAppTests */,
			);
			name = NeuroLoopAppTests;
			packageProductDependencies = (
			);
			productName = NeuroLoopAppTests;
			productReference = 221E290B2DC9327A0088A808 /* NeuroLoopAppTests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		229AA0892DC8F1970059965F /* NeuroLoopApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 229AA0952DC8F1990059965F /* Build configuration list for PBXNativeTarget "NeuroLoopApp" */;
			buildPhases = (
				229AA0862DC8F1970059965F /* Sources */,
				229AA0872DC8F1970059965F /* Frameworks */,
				229AA0882DC8F1970059965F /* Resources */,
				22EC923F2DCAD2E300285A00 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				229AA08C2DC8F1970059965F /* NeuroLoopApp */,
			);
			name = NeuroLoopApp;
			packageProductDependencies = (
				22884F2A2DC8F3340016AD7C /* NeuroLoopApp */,
				22884F2C2DC8F3340016AD7C /* NeuroLoopCore */,
				22884F2E2DC8F3340016AD7C /* NeuroLoopInterfaces */,
				22884F302DC8F3340016AD7C /* NeuroLoopModels */,
				22EC92432DCAD62100285A00 /* NeuroLoopShared */,
				22EC92452DCAD80800285A00 /* NeuroLoopUI */,
			);
			productName = NeuroLoopApp;
			productReference = 229AA08A2DC8F1970059965F /* NeuroLoopApp.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		229AA0822DC8F1970059965F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					221E290A2DC9327A0088A808 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 229AA0892DC8F1970059965F;
					};
					229AA0892DC8F1970059965F = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 229AA0852DC8F1970059965F /* Build configuration list for PBXProject "NeuroLoopApp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 229AA0812DC8F1970059965F;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				22884F292DC8F3340016AD7C /* XCLocalSwiftPackageReference "../NeuroLoopApp2025_Fresh" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 229AA08B2DC8F1970059965F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				229AA0892DC8F1970059965F /* NeuroLoopApp */,
				221E290A2DC9327A0088A808 /* NeuroLoopAppTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		221E29092DC9327A0088A808 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		229AA0882DC8F1970059965F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		221E29072DC9327A0088A808 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		229AA0862DC8F1970059965F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		221E29122DC9327A0088A808 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 229AA0892DC8F1970059965F /* NeuroLoopApp */;
			targetProxy = 221E29112DC9327A0088A808 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		221E29142DC9327A0088A808 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.neuroloop.NeuroLoopApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NeuroLoopApp;
			};
			name = Debug;
		};
		221E29152DC9327A0088A808 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.neuroloop.NeuroLoopApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NeuroLoopApp;
			};
			name = Release;
		};
		229AA0932DC8F1990059965F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		229AA0942DC8F1990059965F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		229AA0962DC8F1990059965F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NeuroLoopApp/NeuroLoopApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 48A5949N5D;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NeuroLoopApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.neuroloop.NeuroLoop;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		229AA0972DC8F1990059965F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NeuroLoopApp/NeuroLoopApp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 48A5949N5D;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NeuroLoopApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.neuroloop.NeuroLoop;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		221E29132DC9327A0088A808 /* Build configuration list for PBXNativeTarget "NeuroLoopAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				221E29142DC9327A0088A808 /* Debug */,
				221E29152DC9327A0088A808 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		229AA0852DC8F1970059965F /* Build configuration list for PBXProject "NeuroLoopApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				229AA0932DC8F1990059965F /* Debug */,
				229AA0942DC8F1990059965F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		229AA0952DC8F1990059965F /* Build configuration list for PBXNativeTarget "NeuroLoopApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				229AA0962DC8F1990059965F /* Debug */,
				229AA0972DC8F1990059965F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		22884F292DC8F3340016AD7C /* XCLocalSwiftPackageReference "../NeuroLoopApp2025_Fresh" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../NeuroLoopApp2025_Fresh;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		22884F2A2DC8F3340016AD7C /* NeuroLoopApp */ = {
			isa = XCSwiftPackageProductDependency;
			productName = NeuroLoopApp;
		};
		22884F2C2DC8F3340016AD7C /* NeuroLoopCore */ = {
			isa = XCSwiftPackageProductDependency;
			productName = NeuroLoopCore;
		};
		22884F2E2DC8F3340016AD7C /* NeuroLoopInterfaces */ = {
			isa = XCSwiftPackageProductDependency;
			productName = NeuroLoopInterfaces;
		};
		22884F302DC8F3340016AD7C /* NeuroLoopModels */ = {
			isa = XCSwiftPackageProductDependency;
			productName = NeuroLoopModels;
		};
		22EC92432DCAD62100285A00 /* NeuroLoopShared */ = {
			isa = XCSwiftPackageProductDependency;
			package = 22884F292DC8F3340016AD7C /* XCLocalSwiftPackageReference "../NeuroLoopApp2025_Fresh" */;
			productName = NeuroLoopShared;
		};
		22EC92452DCAD80800285A00 /* NeuroLoopUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 22884F292DC8F3340016AD7C /* XCLocalSwiftPackageReference "../NeuroLoopApp2025_Fresh" */;
			productName = NeuroLoopUI;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 229AA0822DC8F1970059965F /* Project object */;
}

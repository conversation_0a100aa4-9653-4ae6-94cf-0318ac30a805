#!/usr/bin/env swift

import Foundation

// Mock the basic types we need for testing
protocol AffirmationProtocol {
    var id: UUID { get }
    var text: String { get }
    var currentRepetitions: Int { get }
}

struct RepetitionResult {
    let success: Bool
    let updatedAffirmation: any AffirmationProtocol
    let isQuotaMet: Bool
    let isCycleComplete: Bool
}

// Simple mock affirmation
class MockAffirmation: AffirmationProtocol {
    let id = UUID()
    let text = "I am confident and capable"
    private let _currentRepetitions: Int
    
    init(currentRepetitions: Int = 0) {
        self._currentRepetitions = currentRepetitions
    }
    
    var currentRepetitions: Int { _currentRepetitions }
}

// Mock affirmation with updated count
class MockAffirmationWithCount: AffirmationProtocol {
    private let originalAffirmation: any AffirmationProtocol
    private let _currentRepetitions: Int
    
    init(originalAffirmation: any AffirmationProtocol, newRepetitionCount: Int) {
        self.originalAffirmation = originalAffirmation
        self._currentRepetitions = newRepetitionCount
    }
    
    var id: UUID { originalAffirmation.id }
    var text: String { originalAffirmation.text }
    var currentRepetitions: Int { _currentRepetitions }
}

// Fixed repetition service (like our RootViewMockRepetitionService)
class FixedMockRepetitionService {
    private var repetitionCounts: [UUID: Int] = [:]
    
    func recordRepetition(for affirmation: any AffirmationProtocol) -> RepetitionResult {
        // Get current count for this affirmation
        let currentCount = repetitionCounts[affirmation.id] ?? affirmation.currentRepetitions
        let newCount = currentCount + 1
        
        // Store the new count
        repetitionCounts[affirmation.id] = newCount
        
        print("FixedMockRepetitionService: Incremented count from \(currentCount) to \(newCount)")
        
        // Create a new mock affirmation with the updated count
        let updatedAffirmation = MockAffirmationWithCount(
            originalAffirmation: affirmation,
            newRepetitionCount: newCount
        )
        
        return RepetitionResult(
            success: true,
            updatedAffirmation: updatedAffirmation,
            isQuotaMet: false,
            isCycleComplete: false
        )
    }
}

// Test the fix
print("🧪 Testing Repetition Counter Fix")
print("================================")

let affirmation = MockAffirmation(currentRepetitions: 5)
let service = FixedMockRepetitionService()

print("Initial affirmation count: \(affirmation.currentRepetitions)")

// Test multiple repetitions
for i in 1...3 {
    print("\n--- Test \(i) ---")
    let result = service.recordRepetition(for: affirmation)
    let updatedCount = result.updatedAffirmation.currentRepetitions
    
    print("After recording repetition \(i):")
    print("- Success: \(result.success)")
    print("- Updated count: \(updatedCount)")
    print("- Expected count: \(5 + i)")
    print("- ✅ Correct: \(updatedCount == 5 + i)")
    
    // Simulate the alert message that would be shown
    let alertMessage = "Repetition count increased to \(updatedCount) out of 100."
    print("- Alert message: \"\(alertMessage)\"")
}

print("\n🎉 Test completed! The fix should work correctly.")
print("The repetition service now properly increments the count and returns an updated affirmation object.")

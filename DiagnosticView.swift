import SwiftUI
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopTypes

/**
 * DiagnosticView
 *
 * A diagnostic view for troubleshooting audio and repetition counting issues.
 * This view provides detailed information about the current state of the app
 * and allows testing various components in isolation.
 */

@available(iOS 17.0, macOS 14.0, *)
struct DiagnosticView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var audioTestResult: String = "Not tested"
    @State private var repetitionTestResult: String = "Not tested"
    @State private var serviceInfo: String = "Not checked"
    @State private var audioLevels: [Double] = []
    @State private var isRecording = false
    @State private var recordingPower: Double = -120.0
    
    // Services
    private let audioService: AudioRecordingServiceProtocol
    private let repetitionService: RepetitionServiceProtocol
    private let affirmationService: AffirmationServiceProtocol
    
    // Test affirmation
    @State private var testAffirmation: AffirmationStub = AffirmationStub(
        text: "Test affirmation for diagnostics",
        currentRepetitions: 0
    )
    
    init(
        audioService: AudioRecordingServiceProtocol,
        repetitionService: RepetitionServiceProtocol,
        affirmationService: AffirmationServiceProtocol
    ) {
        self.audioService = audioService
        self.repetitionService = repetitionService
        self.affirmationService = affirmationService
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("Diagnostic Tools")
                    .font(.largeTitle)
                    .bold()
                
                // Service Information
                GroupBox(label: Text("Service Information").bold()) {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Audio Service: \(type(of: audioService))")
                        Text("Repetition Service: \(type(of: repetitionService))")
                        Text("Affirmation Service: \(type(of: affirmationService))")
                        
                        Button("Check Service Details") {
                            checkServiceDetails()
                        }
                        .buttonStyle(.borderedProminent)
                        
                        Text(serviceInfo)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
                
                // Audio Testing
                GroupBox(label: Text("Audio Testing").bold()) {
                    VStack(alignment: .leading, spacing: 10) {
                        Button("Test Microphone") {
                            testMicrophone()
                        }
                        .buttonStyle(.borderedProminent)
                        
                        Text("Result: \(audioTestResult)")
                        
                        Button(isRecording ? "Stop Recording" : "Start Recording") {
                            if isRecording {
                                stopRecording()
                            } else {
                                startRecording()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .foregroundColor(isRecording ? .red : .blue)
                        
                        if isRecording {
                            Text("Recording Power: \(String(format: "%.1f", recordingPower)) dB")
                            
                            // Audio level visualization
                            HStack(spacing: 2) {
                                ForEach(audioLevels.indices, id: \.self) { index in
                                    Rectangle()
                                        .fill(Color.blue)
                                        .frame(width: 3, height: CGFloat(audioLevels[index] * 50))
                                }
                            }
                            .frame(height: 50, alignment: .bottom)
                            .background(Color.black.opacity(0.1))
                        }
                    }
                    .padding()
                }
                
                // Repetition Testing
                GroupBox(label: Text("Repetition Testing").bold()) {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Current Count: \(testAffirmation.currentRepetitions)")
                        
                        Button("Increment Repetition") {
                            incrementRepetition()
                        }
                        .buttonStyle(.borderedProminent)
                        
                        Text("Result: \(repetitionTestResult)")
                    }
                    .padding()
                }
            }
            .padding()
        }
        .onAppear {
            checkServiceDetails()
        }
    }
    
    private func checkServiceDetails() {
        serviceInfo = """
        Audio Service Details:
        - Type: \(type(of: audioService))
        - Is Recording: \(audioService.isRecording)
        - Recording Power: \(audioService.recordingPower) dB
        
        Repetition Service Details:
        - Type: \(type(of: repetitionService))
        - Progress: \(repetitionService.getProgress(for: testAffirmation).currentRepetitions)/\(repetitionService.getProgress(for: testAffirmation).totalRepetitions)
        - Can Perform Repetition: \(repetitionService.canPerformRepetition(for: testAffirmation))
        """
    }
    
    private func testMicrophone() {
        Task {
            let result = await audioService.testMicrophoneAccess()
            await MainActor.run {
                audioTestResult = "Success: \(result.success), Level: \(String(format: "%.1f", result.audioLevel)) dB, Message: \(result.message)"
            }
        }
    }
    
    private func startRecording() {
        Task {
            do {
                try await audioService.startRecording()
                await MainActor.run {
                    isRecording = true
                    startMonitoringAudioLevels()
                }
            } catch {
                await MainActor.run {
                    audioTestResult = "Recording error: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func stopRecording() {
        Task {
            do {
                let url = try await audioService.stopRecording()
                await MainActor.run {
                    isRecording = false
                    audioTestResult = "Recording stopped, URL: \(url.lastPathComponent)"
                }
            } catch {
                await MainActor.run {
                    isRecording = false
                    audioTestResult = "Stop recording error: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func startMonitoringAudioLevels() {
        Task {
            while isRecording {
                let power = audioService.recordingPower
                await MainActor.run {
                    recordingPower = power
                    audioLevels.append(max(0.0, min(1.0, (power + 60) / 60)))
                    if audioLevels.count > 50 {
                        audioLevels.removeFirst()
                    }
                }
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
            }
        }
    }
    
    private func incrementRepetition() {
        Task {
            do {
                let result = try await repetitionService.recordRepetition(for: testAffirmation)
                await MainActor.run {
                    testAffirmation = result.updatedAffirmation as! AffirmationStub
                    repetitionTestResult = "Success: \(result.success), New Count: \(testAffirmation.currentRepetitions)"
                }
            } catch {
                await MainActor.run {
                    repetitionTestResult = "Error: \(error.localizedDescription)"
                }
            }
        }
    }
}

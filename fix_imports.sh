#!/bin/bash

# Fix redundant imports in NeuroLoopUI files
find Sources/NeuroLoopUI/Views -name "*.swift" -type f -exec sed -i '' 's/import NeuroLoopUI//g' {} \;

# Check for files that might need AffirmationProtocol import
grep -r "AffirmationProtocol" --include="*.swift" Sources/ | grep -v "import NeuroLoopInterfaces" | awk -F: '{print $1}' | sort | uniq | while read -r file; do
  if grep -q "AffirmationProtocol" "$file"; then
    if ! grep -q "import NeuroLoopInterfaces" "$file"; then
      echo "Adding NeuroLoopInterfaces import to $file"
      sed -i '' '1s/^/import NeuroLoopInterfaces\n/' "$file"
    fi
  fi
done

echo "Import fixes completed!"

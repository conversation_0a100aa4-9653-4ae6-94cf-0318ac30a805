# Core Concept Rule

## Purpose

This rule ensures that all development work aligns with NeuroLoopApp's core concept of subconscious reprogramming through 100 repetitions over 7 days.

## Key Requirements

All code changes must:

1. Support the 100-repetition mechanism
2. Respect the 7-day cycle
3. Enhance mindful practice
4. Contribute to subconscious reprogramming

## Implementation Guidelines

### Constants

Always use these core constants:

```swift
static let DAILY_REPETITIONS = 100
static let CYCLE_DAYS = 7
```

### Feature Requirements

Every feature must:

- Track progress toward 100 repetitions
- Support the 7-day cycle
- Include mindfulness-enhancing elements
- Provide meaningful feedback for each repetition

### UI/UX Guidelines

- Focus on the repetition counter
- Show clear cycle progress (day 1-7)
- Minimize distractions
- Include calming visual and haptic feedback

### Code Documentation

All relevant files must include:

```swift
/**
 * Part of NeuroLoopApp's core mission:
 * Subconscious reprogramming through 100 repetitions over 7 days
 */
```

## Validation Checklist

Before submitting code, verify:

- [ ] Uses correct repetition (100) and cycle (7) constants
- [ ] Supports the core repetition mechanism
- [ ] Enhances the 7-day cycle process
- [ ] Includes appropriate feedback mechanisms
- [ ] Documentation references core concept

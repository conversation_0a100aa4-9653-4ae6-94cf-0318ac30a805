# Safe File Deletion Protocol

## Context
- Added after premature attempt to delete a file without proper analysis
- Ensures thorough evaluation before any file deletion
- Prevents loss of functionality or breaking changes
- Maintains codebase integrity

## Critical Rules

- NEVER call delete_file tool without first:
  1. Reading and analyzing the complete file contents
  2. Documenting the file's current usage and dependencies
  3. Having a clear migration or deprecation plan
  4. Confirming with the user if deletion is appropriate
  5. Explaining the full impact of deletion
- If file appears redundant, MUST still:
  1. Compare with potential duplicate files
  2. Document unique features in each version
  3. Create and implement merge strategy if needed
  4. Verify no functionality loss
- ALWAYS explain deletion rationale to user before proceeding
- When in doubt, preserve the file and ask the user for guidance

## Examples

<example>
✅ Good Approach:
1. Found potentially redundant QuoteCategory.h
2. Read and analyzed complete file
3. Compared with QuoteCategory.swift
4. Documented unique features:
   - Objective-C compatibility
   - Additional tracking properties
5. Proposed merge strategy to user
6. Waited for user confirmation
</example>

<example type="invalid">
❌ Poor Approach:
1. Saw QuoteCategory.h seemed redundant
2. Immediately attempted deletion
3. Did not analyze contents
4. Did not check dependencies
5. Did not propose migration strategy
</example> 
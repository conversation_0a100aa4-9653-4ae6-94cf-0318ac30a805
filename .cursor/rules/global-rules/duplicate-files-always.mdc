---
description: 
globs: 
alwaysApply: true
---

# Duplicate Files Handling Protocol

## Context
- Added after encountering issues with duplicate file handling in the codebase
- Reinforces methodical approach to file deduplication
- Prevents loss of valuable functionality through hasty deletions
- Ensures best practices are preserved across codebase

## Critical Rules

- NEVER delete a duplicate file without first examining its contents thoroughly
- ALWAYS compare functionality between duplicate files before taking action
- Document unique features and responsibilities of each duplicate file
- Create a merge strategy before modifying any files
- Follow this sequence strictly:
  1. Examine both files completely
  2. List unique features of each file
  3. Document dependencies and usages
  4. Create merge strategy
  5. Implement merge
  6. Verify no functionality is lost
  7. Only then remove redundant file

## Examples

<example>
✅ Good Approach:
1. Found duplicate AffirmationMigrationService.swift
2. Examined both implementations
3. Documented unique features:
   - File A: Backup functionality
   - File B: SwiftData schema versioning
4. Created merged implementation
5. Verified functionality
6. Removed redundant file
</example>

<example type="invalid">
❌ Poor Approach:
1. Found duplicate file
2. Immediately deleted one without checking contents
3. Lost unique functionality
4. Had to restore from version control
</example> 
---
description: Standards for meaningful and consistent git commits
globs: .augment/rules/tool-rules/**/*.mdc
alwaysApply: false
---

# Git Commit Standards

## Context

- When committing code

## Critical Rules

- Format: `type: description`
- Use 72 character max title
- Include summary paragraph
- Tag issues where applicable

## Examples

<example>
✅ feat: add login system
✅ Summarized purpose and method
</example>

<example type="invalid">
❌ added some stuff
❌ no explanation
</example>

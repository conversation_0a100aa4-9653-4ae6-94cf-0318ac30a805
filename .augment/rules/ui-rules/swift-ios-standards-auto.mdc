---
description: Standards for Swift and iOS development
globs: .augment/rules/ui-rules/**/*.mdc
alwaysApply: false
---

# Swift/iOS Development Standards

## Context

- Swift / SwiftUI app development

## Critical Rules

- Use latest Swift
- Support Dynamic Type
- Implement accessibility
- Use Asset Catalogs

## Examples

<example>
✅ Used SwiftUI environment values
✅ Supported VoiceOver
</example>

<example type="invalid">
❌ Ignored Dynamic Type
❌ Hardcoded UI values
</example>

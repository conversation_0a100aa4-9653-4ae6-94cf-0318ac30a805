---
description: Protocols for safely handling file operations
globs: .augment/rules/global-rules/**/*.mdc
alwaysApply: true
---

# File Safety Protocols

## Context

- When modifying or deleting files
- When restructuring codebases

## Critical Rules

- Examine file contents before deletion
- Document usages and dependencies
- Backup critical files
- Confirm user intent for deletions

## Examples

<example>
✅ Backed up important files
✅ Verified no active usage
✅ Deleted after confirmations
</example>

<example type="invalid">
❌ Deleted critical files without backup
❌ Broke app functionality
</example>

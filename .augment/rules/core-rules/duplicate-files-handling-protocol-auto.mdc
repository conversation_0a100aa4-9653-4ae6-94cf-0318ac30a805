---
description: Guidelines for safely handling and resolving duplicate files
globs: .augment/rules/core-rules/**/*.mdc
alwaysApply: false
---

# Duplicate Files Handling Protocol

## Context

- When duplicate files exist
- Avoid loss during deduplication

## Critical Rules

- NEVER delete without examining both
- ALWAYS document unique features
- Merge carefully with structured plan

## Examples

<example>
✅ Examined two duplicates
✅ Documented unique features
✅ Merged carefully
✅ Deleted redundant file
</example>

<example type="invalid">
❌ Deleted file without examination
❌ Lost important functionality
</example>

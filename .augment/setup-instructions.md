# Setup Instructions for Augment

## 📋 Steps for Automating Rule Detection and Management

1. Monitor `.augment/rules/**` for any `.mdc` file changes.
2. Always validate that each rule has correct frontmatter fields:
   - `description`
   - `globs`
   - `alwaysApply`
3. Respect file endings:
   - `-manual.mdc` → load only on user request.
   - `-auto.mdc` → apply automatically for matching globs.
   - `-always.mdc` → load into every session.
   - `-agent.mdc` → load when agent logic demands.
4. Auto-rescan folder structure daily or after rule file changes.
5. Report successful rule pickup with:

```text
AutoRuleGen Success: path/rule-name.mdc
Rule Type: {Rule Type}
Summary: {Brief Summary}
```

6. For new languages or tools (e.g., `cs-rules/`, `go-rules/`), auto-create corresponding folder if absent.

# Augment Rules Overview

This directory contains all project-specific rules for Augment to operate efficiently.

## Organizational Folders

- `core-rules/` — Core agent behavior rules
- `my-rules/` — Personal rules (private usage)
- `global-rules/` — Rules always active in every chat
- `testing-rules/` — Testing specific rules
- `tool-rules/` — Tool-specific usage rules
- `ts-rules/` — TypeScript rules
- `py-rules/` — Python rules
- `ui-rules/` — HTML, CSS, React UI rules

> 📌 All rule files follow strict `.mdc` format with mandatory frontmatter (`description`, `globs`, `alwaysApply`).

## Key Standards

- No glob quotes or extension grouping `{}`
- Rule files named: `rule-name-{auto|manual|agent|always}.mdc`
- Keep rules concise, ideally < 50 lines
- Always provide valid and invalid examples
- Prefer modular rules over gigantic ones

## Auto-Pickup Behavior

When new rules are added under `.augment/rules/`, Augment automatically detects and applies them if properly structured.

## Post-Update Response Standard

Whenever a rule is created/updated, Augment should respond with:

```text
AutoRuleGen Success: path/rule-name.mdc
Rule Type: {Rule Type}
Summary: {Short Summary}
```

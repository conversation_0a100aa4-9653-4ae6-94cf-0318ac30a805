import XCTest
@testable import NeuroLoopCore
@testable import NeuroLoopModels
@testable import NeuroLoopInterfaces

final class InMemoryAffirmationRepositoryTests: XCTestCase {
    func testFetchAffirmations() async throws {
        // Given
        let repository = InMemoryAffirmationRepository()
        
        // When
        let affirmations = try await repository.fetchAffirmations()
        
        // Then
        XCTAssertFalse(affirmations.isEmpty, "Repository should be initialized with default affirmations")
    }
    
    func testCreateAffirmation() async throws {
        // Given
        let repository = InMemoryAffirmationRepository()
        let text = "I am creating a new affirmation"
        let category = AffirmationCategory.personal
        
        // When
        let newAffirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )
        
        // Then
        XCTAssertEqual(newAffirmation.text, text)
        XCTAssertEqual(newAffirmation.category, category)
        
        // Verify it was added to the repository
        let affirmations = try await repository.fetchAffirmations()
        XCTAssertTrue(affirmations.contains { $0.id == newAffirmation.id })
    }
    
    func testFetchAffirmationById() async throws {
        // Given
        let repository = InMemoryAffirmationRepository()
        let text = "Test affirmation for fetching by ID"
        let category = AffirmationCategory.confidence
        
        // When
        let newAffirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )
        
        let fetchedAffirmation = try await repository.fetchAffirmation(id: newAffirmation.id)
        
        // Then
        XCTAssertNotNil(fetchedAffirmation)
        XCTAssertEqual(fetchedAffirmation?.id, newAffirmation.id)
        XCTAssertEqual(fetchedAffirmation?.text, text)
    }
    
    func testUpdateAffirmation() async throws {
        // Given
        let repository = InMemoryAffirmationRepository()
        let text = "Original text"
        let updatedText = "Updated text"
        let category = AffirmationCategory.confidence
        
        // When
        let newAffirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        ) as! NeuroLoopCore.LegacyAffirmation
        
        var updatedAffirmation = newAffirmation
        updatedAffirmation.text = updatedText
        
        try await repository.updateAffirmation(updatedAffirmation)
        
        let fetchedAffirmation = try await repository.fetchAffirmation(id: newAffirmation.id)
        
        // Then
        XCTAssertEqual(fetchedAffirmation?.text, updatedText)
    }
    
    func testDeleteAffirmation() async throws {
        // Given
        let repository = InMemoryAffirmationRepository()
        let text = "Affirmation to delete"
        let category = AffirmationCategory.confidence
        
        // When
        let newAffirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )
        
        let initialCount = (try await repository.fetchAffirmations()).count
        
        try await repository.deleteAffirmation(id: newAffirmation.id)
        
        let finalCount = (try await repository.fetchAffirmations()).count
        let fetchedAffirmation = try await repository.fetchAffirmation(id: newAffirmation.id)
        
        // Then
        XCTAssertNil(fetchedAffirmation)
        XCTAssertEqual(finalCount, initialCount - 1)
    }
    
    func testFetchAffirmationsByCategory() async throws {
        // Given
        let repository = InMemoryAffirmationRepository()
        let category = AffirmationCategory.health
        
        // Create a few affirmations with the target category
        for i in 1...3 {
            _ = try await repository.createAffirmation(
                text: "Health affirmation \(i)",
                category: category,
                recordingURL: nil
            )
        }
        
        // Create some affirmations with a different category
        for i in 1...2 {
            _ = try await repository.createAffirmation(
                text: "Confidence affirmation \(i)",
                category: .confidence,
                recordingURL: nil
            )
        }
        
        // When
        let healthAffirmations = try await repository.fetchAffirmations(category: category)
        
        // Then
        XCTAssertEqual(healthAffirmations.count, 3)
        for affirmation in healthAffirmations {
            XCTAssertEqual(affirmation.category, category)
        }
    }
    
    func testStartCycle() async throws {
        // Given
        let repository = InMemoryAffirmationRepository()
        let text = "Affirmation for cycle test"
        let category = AffirmationCategory.confidence
        
        // When
        let newAffirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )
        
        try await repository.startCycle(for: newAffirmation)
        
        let updatedAffirmation = try await repository.fetchAffirmation(id: newAffirmation.id)
        
        // Then
        XCTAssertNotNil(updatedAffirmation)
        XCTAssertTrue(updatedAffirmation!.hasActiveCycle)
        XCTAssertEqual(updatedAffirmation!.currentCycleDay, 1)
        XCTAssertNotNil(updatedAffirmation!.cycleStartDate)
    }
    
    func testRecordRepetition() async throws {
        // Given
        let repository = InMemoryAffirmationRepository()
        let text = "Affirmation for repetition test"
        let category = AffirmationCategory.confidence
        
        // When
        let newAffirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )
        
        try await repository.startCycle(for: newAffirmation)
        
        let beforeRepetition = try await repository.fetchAffirmation(id: newAffirmation.id)
        let initialRepetitions = beforeRepetition!.currentRepetitions
        
        try await repository.recordRepetition(for: beforeRepetition!)
        
        let afterRepetition = try await repository.fetchAffirmation(id: newAffirmation.id)
        
        // Then
        XCTAssertEqual(afterRepetition!.currentRepetitions, initialRepetitions + 1)
        XCTAssertNotNil(afterRepetition!.lastRepetitionDate)
    }
}

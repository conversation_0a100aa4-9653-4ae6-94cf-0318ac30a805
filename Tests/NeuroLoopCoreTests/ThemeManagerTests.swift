import XCTest
import NeuroLoopShared
import NeuroLoopCore

final class ThemeManagerTests: XCTestCase {
    override func setUp() {
        super.setUp()
        // Clear UserDefaults before each test
        UserDefaults.standard.removeObject(forKey: "customThemes")
        UserDefaults.standard.removeObject(forKey: "currentThemeID")
    }

    func testAddAndUpdateAndDeleteCustomTheme() {
        let manager = ThemeManager.shared
        let theme = Theme(
            name: "Test Theme",
            primaryTextColor: CodableColor(.black),
            secondaryTextColor: CodableColor(.gray),
            backgroundColor: CodableColor(.white),
            cardBackgroundColor: CodableColor(.white),
            accentColor: CodableColor(.blue),
            shadowColor: CodableColor(.black.opacity(0.2)),
            errorColor: CodableColor(.red),
            successColor: CodableColor(.green),
            warningColor: CodableColor(.orange),
            borderColor: CodableColor(.gray.opacity(0.3)),
            disabledColor: CodableColor(.gray.opacity(0.5))
        )
        manager.addCustomTheme(theme)
        XCTAssertTrue(manager.customThemes.contains(where: { $0.id == theme.id }))
        // Update
        let updated = Theme(
            id: theme.id,
            name: "Updated Theme",
            isBuiltIn: false,
            primaryTextColor: CodableColor(.red),
            secondaryTextColor: CodableColor(.gray),
            backgroundColor: CodableColor(.white),
            cardBackgroundColor: CodableColor(.white),
            accentColor: CodableColor(.blue),
            shadowColor: CodableColor(.black.opacity(0.2)),
            errorColor: CodableColor(.red),
            successColor: CodableColor(.green),
            warningColor: CodableColor(.orange),
            borderColor: CodableColor(.gray.opacity(0.3)),
            disabledColor: CodableColor(.gray.opacity(0.5))
        )
        manager.updateCustomTheme(updated)
        XCTAssertTrue(manager.customThemes.contains(where: { $0.name == "Updated Theme" }))
        // Delete
        manager.deleteCustomTheme(updated)
        XCTAssertFalse(manager.customThemes.contains(where: { $0.id == updated.id }))
    }

    func testThemePersistence() {
        let manager = ThemeManager.shared
        let theme = Theme(
            name: "Persistent Theme",
            primaryTextColor: CodableColor(.black),
            secondaryTextColor: CodableColor(.gray),
            backgroundColor: CodableColor(.white),
            cardBackgroundColor: CodableColor(.white),
            accentColor: CodableColor(.blue),
            shadowColor: CodableColor(.black.opacity(0.2)),
            errorColor: CodableColor(.red),
            successColor: CodableColor(.green),
            warningColor: CodableColor(.orange),
            borderColor: CodableColor(.gray.opacity(0.3)),
            disabledColor: CodableColor(.gray.opacity(0.5))
        )
        manager.addCustomTheme(theme)
        manager.setTheme(theme)
        // Simulate reload
        let manager2 = ThemeManager.shared
        manager2.setTheme(theme)
        XCTAssertTrue(manager2.customThemes.contains(where: { $0.id == theme.id }))
        XCTAssertEqual(manager2.currentTheme.id, theme.id)
    }
} 
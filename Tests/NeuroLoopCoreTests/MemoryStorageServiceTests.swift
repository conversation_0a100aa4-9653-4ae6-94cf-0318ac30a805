import XCTest
@testable import NeuroLoopCore
@testable import NeuroLoopModels
@testable import NeuroLoopInterfaces

final class MemoryStorageServiceTests: XCTestCase {
    var service: MemoryStorageService!

    override func setUp() async throws {
        try await super.setUp()
        service = MemoryStorageService()
    }

    override func tearDown() async throws {
        service = nil
        try await super.tearDown()
    }

    // MARK: - Basic CRUD
    func testCreateAndFetchAffirmation() async throws {
        let text = "Test affirmation"
        let category = AffirmationCategory.confidence
        let created = try await service.createAffirmation(text: text, category: category, recordingURL: nil) as! AffirmationDTO
        let fetched = try await service.fetchAffirmation(id: created.id) as? AffirmationDTO
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.text, text)
        XCTAssertEqual(fetched?.category, category)
    }

    func testUpdateAffirmation() async throws {
        let created = try await service.createAffirmation(text: "Old text", category: .confidence, recordingURL: nil) as! AffirmationDTO
        var updated = created
        updated = AffirmationDTO(
            id: updated.id,
            text: "New text",
            category: updated.category,
            recordingURL: updated.recordingURL,
            createdAt: updated.createdAt,
            updatedAt: Date(),
            completedCycles: updated.completedCycles,
            currentRepetitions: updated.currentRepetitions,
            lastRepetitionDate: updated.lastRepetitionDate,
            energyLevel: updated.energyLevel,
            moodRating: updated.moodRating,
            notes: updated.notes,
            isFavorite: updated.isFavorite,
            playCount: updated.playCount,
            hasActiveCycle: updated.hasActiveCycle,
            currentCycleDay: updated.currentCycleDay,
            cycleStartDate: updated.cycleStartDate,
            cycles: updated.cycles
        )
        try await service.updateAffirmation(updated)
        let fetched = try await service.fetchAffirmation(id: updated.id) as? AffirmationDTO
        XCTAssertEqual(fetched?.text, "New text")
    }

    func testDeleteAffirmation() async throws {
        let created = try await service.createAffirmation(text: "To delete", category: .confidence, recordingURL: nil) as! AffirmationDTO
        try await service.deleteAffirmation(id: created.id)
        let fetched = try await service.fetchAffirmation(id: created.id) as? AffirmationDTO
        XCTAssertNil(fetched)
    }

    func testFetchAffirmationsByCategory() async throws {
        let cat1 = AffirmationCategory.confidence
        let cat2 = AffirmationCategory.mindfulness
        _ = try await service.createAffirmation(text: "A1", category: cat1, recordingURL: nil)
        _ = try await service.createAffirmation(text: "A2", category: cat2, recordingURL: nil)
        _ = try await service.createAffirmation(text: "A3", category: cat1, recordingURL: nil)
        let cat1Affirmations = try await service.fetchAffirmations(category: cat1).compactMap { $0 as? AffirmationDTO }
        XCTAssertEqual(cat1Affirmations.count, 2)
        XCTAssertTrue(cat1Affirmations.allSatisfy { $0.category == cat1 })
    }

    func testFetchFavoriteAffirmations() async throws {
        let a1 = try await service.createAffirmation(text: "A1", category: .confidence, recordingURL: nil) as! AffirmationDTO
        let a2 = try await service.createAffirmation(text: "A2", category: .confidence, recordingURL: nil) as! AffirmationDTO
        let favA1 = AffirmationDTO(
            id: a1.id,
            text: a1.text,
            category: a1.category,
            recordingURL: a1.recordingURL,
            createdAt: a1.createdAt,
            updatedAt: Date(),
            completedCycles: a1.completedCycles,
            currentRepetitions: a1.currentRepetitions,
            lastRepetitionDate: a1.lastRepetitionDate,
            energyLevel: a1.energyLevel,
            moodRating: a1.moodRating,
            notes: a1.notes,
            isFavorite: true,
            playCount: a1.playCount,
            hasActiveCycle: a1.hasActiveCycle,
            currentCycleDay: a1.currentCycleDay,
            cycleStartDate: a1.cycleStartDate,
            cycles: a1.cycles
        )
        try await service.updateAffirmation(favA1)
        let favorites = try await service.fetchFavoriteAffirmations().compactMap { $0 as? AffirmationDTO }
        XCTAssertEqual(favorites.count, 1)
        if let favoriteDTO = favorites.first {
            XCTAssertEqual(favoriteDTO.id, favA1.id)
        }
    }

    // MARK: - Cycle Management
    func testStartCycleAndRecordRepetition() async throws {
        let created = try await service.createAffirmation(text: "Cycle test", category: .confidence, recordingURL: nil) as! AffirmationDTO
        try await service.startCycle(for: created)
        let afterStart = try await service.fetchAffirmation(id: created.id) as? AffirmationDTO
        XCTAssertTrue(afterStart?.hasActiveCycle ?? false)
        XCTAssertEqual(afterStart?.currentCycleDay, 1)
        try await service.recordRepetition(for: afterStart!)
        let afterRepetition = try await service.fetchAffirmation(id: created.id) as? AffirmationDTO
        XCTAssertEqual(afterRepetition?.currentRepetitions, 1)
        XCTAssertNotNil(afterRepetition?.lastRepetitionDate)
    }

    // MARK: - Edge Cases
    func testEmptyData() async throws {
        let affirmations = try await service.fetchAffirmations()
        XCTAssertTrue(affirmations.isEmpty)
    }

    func testConcurrentOperations() async throws {
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<10 {
                group.addTask { [self] in
                    _ = try? await self.service.createAffirmation(text: "Affirmation \(i)", category: .confidence, recordingURL: nil)
                }
            }
        }
        let affirmations = try await self.service.fetchAffirmations()
        XCTAssertEqual(affirmations.count, 10)
    }

    func testLargeDatasetPerformance() async throws {
        let count = 200
        for i in 0..<count {
            _ = try await service.createAffirmation(text: "Affirmation \(i)", category: .confidence, recordingURL: nil)
        }
        let affirmations = try await service.fetchAffirmations()
        XCTAssertEqual(affirmations.count, count)
    }

    func testErrorHandlingOnUpdateDelete() async throws {
        let fakeId = UUID()
        let fakeDTO = AffirmationDTO(
            id: fakeId,
            text: "Fake",
            category: .confidence,
            recordingURL: nil,
            createdAt: Date(),
            updatedAt: Date(),
            completedCycles: 0,
            currentRepetitions: 0,
            lastRepetitionDate: nil,
            energyLevel: 0.5,
            moodRating: nil,
            notes: nil,
            isFavorite: false,
            playCount: 0,
            hasActiveCycle: false,
            currentCycleDay: 1,
            cycleStartDate: nil,
            cycles: []
        )
        await XCTAssertThrowsErrorAsync(try await service.updateAffirmation(fakeDTO))
        await XCTAssertThrowsErrorAsync(try await service.deleteAffirmation(id: fakeId))
    }
}

// Helper for async error assertion
func XCTAssertThrowsErrorAsync<T>(_ expression: @autoclosure @escaping () async throws -> T, file: StaticString = #file, line: UInt = #line) async {
    do {
        _ = try await expression()
        XCTFail("Expected error to be thrown", file: file, line: line)
    } catch {
        // Success
    }
} 
import XCTest
import SwiftData
@testable import NeuroLoopCore
@testable import NeuroLoopModels
@testable import NeuroLoopInterfaces

@available(iOS 17.0, macOS 14.0, *)
final class SwiftDataAffirmationRepositoryTests: XCTestCase {
    var repository: SwiftDataAffirmationRepository!
    var modelContainer: ModelContainer!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create an in-memory model container for testing
        let schema = Schema([
            Affirmation.self,
            RepetitionCycle.self
        ])
        
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
        
        do {
            self.modelContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
            self.repository = SwiftDataAffirmationRepository(modelContainer: modelContainer)
        } catch {
            XCTFail("Failed to create model container: \(error)")
        }
    }
    
    override func tearDown() async throws {
        repository = nil
        modelContainer = nil
        try await super.tearDown()
    }
    
    func testCreateAffirmation() async throws {
        // Given
        let text = "I am confident and capable"
        let category = AffirmationCategory.confidence
        
        // When
        let affirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )
        
        // Then
        XCTAssertEqual(affirmation.text, text)
        XCTAssertEqual(affirmation.category, category)
        
        // Verify it was added to the repository
        let affirmations = try await repository.fetchAffirmations()
        XCTAssertEqual(affirmations.count, 1)
        XCTAssertEqual(affirmations[0].id, affirmation.id)
    }
    
    func testFetchAffirmation() async throws {
        // Given
        let text = "I am confident and capable"
        let category = AffirmationCategory.confidence
        let affirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )
        
        // When
        let fetchedAffirmation = try await repository.fetchAffirmation(id: affirmation.id)
        
        // Then
        XCTAssertNotNil(fetchedAffirmation)
        XCTAssertEqual(fetchedAffirmation?.id, affirmation.id)
        XCTAssertEqual(fetchedAffirmation?.text, text)
    }
    
    func testUpdateAffirmation() async throws {
        // Given
        let text = "I am confident and capable"
        let newText = "I am strong and resilient"
        let category = AffirmationCategory.confidence
        let affirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        ) as! Affirmation
        
        // When
        affirmation.text = newText
        try await repository.updateAffirmation(affirmation)
        
        // Then
        let fetchedAffirmation = try await repository.fetchAffirmation(id: affirmation.id)
        XCTAssertEqual(fetchedAffirmation?.text, newText)
    }
    
    func testDeleteAffirmation() async throws {
        // Given
        let text = "I am confident and capable"
        let category = AffirmationCategory.confidence
        let affirmation = try await repository.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )
        
        // When
        try await repository.deleteAffirmation(id: affirmation.id)
        
        // Then
        let affirmations = try await repository.fetchAffirmations()
        XCTAssertEqual(affirmations.count, 0)
    }
    
    func testFetchAffirmationsByCategory() async throws {
        // Given
        let confidence = AffirmationCategory.confidence
        let mindfulness = AffirmationCategory.mindfulness
        
        _ = try await repository.createAffirmation(
            text: "I am confident and capable",
            category: confidence,
            recordingURL: nil
        )
        
        _ = try await repository.createAffirmation(
            text: "I embrace the present moment",
            category: mindfulness,
            recordingURL: nil
        )
        
        _ = try await repository.createAffirmation(
            text: "I believe in myself",
            category: confidence,
            recordingURL: nil
        )
        
        // When
        let confidenceAffirmations = try await repository.fetchAffirmations(category: confidence)
        
        // Then
        XCTAssertEqual(confidenceAffirmations.count, 2)
        XCTAssertTrue(confidenceAffirmations.allSatisfy { $0.category == confidence })
    }
    
    func testFetchFavoriteAffirmations() async throws {
        // Given
        let affirmation1 = try await repository.createAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            recordingURL: nil
        ) as! Affirmation
        
        let affirmation2 = try await repository.createAffirmation(
            text: "I embrace the present moment",
            category: .mindfulness,
            recordingURL: nil
        ) as! Affirmation
        
        // When
        affirmation1.isFavorite = true
        try await repository.updateAffirmation(affirmation1)
        
        // Then
        let favorites = try await repository.fetchFavoriteAffirmations()
        XCTAssertEqual(favorites.count, 1)
        XCTAssertEqual(favorites[0].id, affirmation1.id)
    }
    
    func testStartCycle() async throws {
        // Given
        let affirmation = try await repository.createAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            recordingURL: nil
        )
        
        // When
        try await repository.startCycle(for: affirmation)
        
        // Then
        let updatedAffirmation = try await repository.fetchAffirmation(id: affirmation.id)
        XCTAssertNotNil(updatedAffirmation)
        XCTAssertTrue(updatedAffirmation!.hasActiveCycle)
        XCTAssertEqual(updatedAffirmation!.currentCycleDay, 1)
        XCTAssertNotNil(updatedAffirmation!.cycleStartDate)
    }
    
    func testRecordRepetition() async throws {
        // Given
        let affirmation = try await repository.createAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            recordingURL: nil
        )
        
        try await repository.startCycle(for: affirmation)
        
        let beforeRepetition = try await repository.fetchAffirmation(id: affirmation.id)
        let initialRepetitions = beforeRepetition!.currentRepetitions
        
        // When
        try await repository.recordRepetition(for: beforeRepetition!)
        
        // Then
        let afterRepetition = try await repository.fetchAffirmation(id: affirmation.id)
        XCTAssertEqual(afterRepetition!.currentRepetitions, initialRepetitions + 1)
        XCTAssertNotNil(afterRepetition!.lastRepetitionDate)
        
        // Check that the daily progress was updated
        let today = Calendar.current.startOfDay(for: Date())
        XCTAssertEqual(afterRepetition!.dailyProgress[today], 1)
    }
    
    func testCompleteCycle() async throws {
        // Given
        let affirmation = try await repository.createAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            recordingURL: nil
        ) as! Affirmation
        
        try await repository.startCycle(for: affirmation)
        
        // Simulate completing a cycle
        let cycle = affirmation.currentCycle!
        cycle.currentDay = AffirmationConstants.CYCLE_DAYS
        
        // Set today's repetitions to meet the quota
        let today = Calendar.current.startOfDay(for: Date())
        var progress = cycle.dailyProgress
        progress[today] = AffirmationConstants.DAILY_REPETITIONS
        cycle.dailyProgress = progress
        
        try await repository.updateAffirmation(affirmation)
        
        // When
        try await repository.recordRepetition(for: affirmation)
        
        // Then
        let updatedAffirmation = try await repository.fetchAffirmation(id: affirmation.id) as! Affirmation
        XCTAssertFalse(updatedAffirmation.hasActiveCycle)
        XCTAssertEqual(updatedAffirmation.completedCycles, 1)
        XCTAssertTrue(updatedAffirmation.currentCycle?.isComplete ?? false)
    }
}

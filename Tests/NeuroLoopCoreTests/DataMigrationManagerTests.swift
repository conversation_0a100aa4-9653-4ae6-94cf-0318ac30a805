import XCTest
import SwiftData
@testable import NeuroLoopCore
@testable import NeuroLoopModels
@testable import NeuroLoopInterfaces

@available(iOS 17.0, macOS 14.0, *)
final class DataMigrationManagerTests: XCTestCase {
    var legacyRepository: InMemoryAffirmationRepository!
    var swiftDataRepository: SwiftDataAffirmationRepository!
    var migrationManager: DataMigrationManager!
    var modelContainer: ModelContainer!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Set up legacy repository with sample data
        legacyRepository = InMemoryAffirmationRepository()
        
        // Create an in-memory model container for testing
        let schema = Schema([
            Affirmation.self,
            RepetitionCycle.self
        ])
        
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
        
        do {
            self.modelContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
            self.swiftDataRepository = SwiftDataAffirmationRepository(modelContainer: modelContainer)
            self.migrationManager = DataMigrationManager(
                legacyRepository: legacyRepository,
                swiftDataRepository: swiftDataRepository
            )
        } catch {
            XCTFail("Failed to create model container: \(error)")
        }
    }
    
    override func tearDown() async throws {
        legacyRepository = nil
        swiftDataRepository = nil
        migrationManager = nil
        modelContainer = nil
        try await super.tearDown()
    }
    
    func testMigrateData() async throws {
        // Given
        // Legacy repository is initialized with sample data
        
        // When
        let result = try await migrationManager.migrateData()
        
        // Then
        XCTAssertTrue(result.success)
        XCTAssertGreaterThan(result.totalItems, 0)
        XCTAssertEqual(result.successfulItems, result.totalItems)
        
        // Verify that the data was migrated correctly
        let legacyAffirmations = try await legacyRepository.fetchAffirmations()
        let swiftDataAffirmations = try await swiftDataRepository.fetchAffirmations()
        
        XCTAssertEqual(swiftDataAffirmations.count, legacyAffirmations.count)
        
        // Check that all IDs were preserved
        let legacyIds = Set(legacyAffirmations.map { $0.id })
        let swiftDataIds = Set(swiftDataAffirmations.map { $0.id })
        
        XCTAssertEqual(swiftDataIds, legacyIds)
        
        // Check that active cycles were migrated correctly
        let legacyActiveAffirmations = legacyAffirmations.filter { $0.hasActiveCycle }
        let swiftDataActiveAffirmations = swiftDataAffirmations.filter { $0.hasActiveCycle }
        
        XCTAssertEqual(swiftDataActiveAffirmations.count, legacyActiveAffirmations.count)
        
        // Check a specific affirmation to ensure all properties were migrated correctly
        if let legacyAffirmation = legacyAffirmations.first,
           let swiftDataAffirmation = swiftDataAffirmations.first(where: { $0.id == legacyAffirmation.id }) {
            
            XCTAssertEqual(swiftDataAffirmation.text, legacyAffirmation.text)
            XCTAssertEqual(swiftDataAffirmation.category, legacyAffirmation.category)
            XCTAssertEqual(swiftDataAffirmation.isFavorite, legacyAffirmation.isFavorite)
            XCTAssertEqual(swiftDataAffirmation.completedCycles, legacyAffirmation.completedCycles)
            
            // If the affirmation has an active cycle, check that it was migrated correctly
            if legacyAffirmation.hasActiveCycle, let affirmation = swiftDataAffirmation as? Affirmation {
                XCTAssertNotNil(affirmation.currentCycle)
                XCTAssertEqual(affirmation.currentCycleDay, legacyAffirmation.currentCycleDay)
                
                // Check that the daily progress was migrated correctly
                for (date, count) in legacyAffirmation.dailyProgress {
                    XCTAssertEqual(affirmation.dailyProgress[date], count)
                }
            }
        }
    }
}

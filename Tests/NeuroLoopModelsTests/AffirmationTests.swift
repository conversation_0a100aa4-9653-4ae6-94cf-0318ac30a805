import XCTest
@testable import NeuroLoopModels
@testable import NeuroLoopInterfaces

final class AffirmationTests: XCTestCase {
    func testAffirmationInitialization() {
        // Given
        let id = UUID()
        let text = "I am confident and capable"
        let category = AffirmationCategory.confidence
        
        // When
        let affirmation = LegacyAffirmation(
            id: id,
            text: text,
            category: category
        )
        
        // Then
        XCTAssertEqual(affirmation.id, id)
        XCTAssertEqual(affirmation.text, text)
        XCTAssertEqual(affirmation.category, category)
        XCTAssertEqual(affirmation.currentCycleDay, 1)
        XCTAssertEqual(affirmation.completedCycles, 0)
        XCTAssertEqual(affirmation.currentRepetitions, 0)
        XCTAssertFalse(affirmation.hasActiveCycle)
        XCTAssertFalse(affirmation.isFavorite)
    }
    
    func testRecordRepetition() throws {
        // Given
        var affirmation = LegacyAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            hasActiveCycle: true
        )
        
        // When
        try affirmation.recordRepetition()
        
        // Then
        XCTAssertEqual(affirmation.currentRepetitions, 1)
        XCTAssertNotNil(affirmation.lastRepetitionDate)
        
        // Check daily progress
        let today = Calendar.current.startOfDay(for: Date())
        XCTAssertEqual(affirmation.dailyProgress[today], 1)
    }
    
    func testCannotRecordRepetitionWithoutActiveCycle() {
        // Given
        var affirmation = LegacyAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            hasActiveCycle: false
        )
        
        // When/Then
        XCTAssertThrowsError(try affirmation.recordRepetition()) { error in
            XCTAssertEqual(error as? AffirmationError, AffirmationError.cannotPerformRepetition)
        }
    }
    
    func testTodayProgress() {
        // Given
        let today = Calendar.current.startOfDay(for: Date())
        var dailyProgress = [Date: Int]()
        dailyProgress[today] = 50
        
        let affirmation = LegacyAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            dailyProgress: dailyProgress
        )
        
        // When
        let progress = affirmation.todayProgress
        
        // Then
        XCTAssertEqual(progress, 0.5) // 50 / 100 = 0.5
    }
    
    func testCycleProgress() {
        // Given
        let today = Calendar.current.startOfDay(for: Date())
        var dailyProgress = [Date: Int]()
        dailyProgress[today] = 50
        
        let affirmation = LegacyAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            currentCycleDay: 4, // Day 4 of 7
            dailyProgress: dailyProgress
        )
        
        // When
        let progress = affirmation.cycleProgress
        
        // Then
        // 3 completed days (days 1-3) = 3/7 = 0.428...
        // Today's progress = 50/100 * 1/7 = 0.5 * 0.142... = 0.071...
        // Total progress = 0.428... + 0.071... ≈ 0.5
        XCTAssertEqual(round(progress * 100) / 100, 0.5)
    }
    
    func testHasTodayQuotaMet() {
        // Given
        let today = Calendar.current.startOfDay(for: Date())
        
        // Case 1: Not met
        var dailyProgress1 = [Date: Int]()
        dailyProgress1[today] = 99
        
        let affirmation1 = LegacyAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            dailyProgress: dailyProgress1
        )
        
        // Case 2: Met
        var dailyProgress2 = [Date: Int]()
        dailyProgress2[today] = 100
        
        let affirmation2 = LegacyAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            dailyProgress: dailyProgress2
        )
        
        // When/Then
        XCTAssertFalse(affirmation1.hasTodayQuotaMet)
        XCTAssertTrue(affirmation2.hasTodayQuotaMet)
    }
    
    func testIsCurrentCycleComplete() {
        // Given
        let today = Calendar.current.startOfDay(for: Date())
        
        // Case 1: Not complete (day < 7)
        var dailyProgress1 = [Date: Int]()
        dailyProgress1[today] = 100
        
        let affirmation1 = LegacyAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            currentCycleDay: 6,
            dailyProgress: dailyProgress1
        )
        
        // Case 2: Not complete (day = 7, but quota not met)
        var dailyProgress2 = [Date: Int]()
        dailyProgress2[today] = 99
        
        let affirmation2 = LegacyAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            currentCycleDay: 7,
            dailyProgress: dailyProgress2
        )
        
        // Case 3: Complete
        var dailyProgress3 = [Date: Int]()
        dailyProgress3[today] = 100
        
        let affirmation3 = LegacyAffirmation(
            text: "I am confident and capable",
            category: .confidence,
            currentCycleDay: 7,
            dailyProgress: dailyProgress3
        )
        
        // When/Then
        XCTAssertFalse(affirmation1.isCurrentCycleComplete)
        XCTAssertFalse(affirmation2.isCurrentCycleComplete)
        XCTAssertTrue(affirmation3.isCurrentCycleComplete)
    }
}

import XCTest

@testable import Neuro<PERSON>oopCore
@testable import NeuroLoopInterfaces

final class AudioRecordingServiceTests: XCTestCase {
    var serviceFactory: ServiceFactory!

    override func setUp() {
        super.setUp()
        serviceFactory = ServiceFactory()
    }

    override func tearDown() {
        serviceFactory = nil
        super.tearDown()
    }

    func testPlatformSpecificInitialization() async {
        // Get the service instance
        let service = serviceFactory.getAudioRecordingService()

        #if os(iOS)
            // On iOS, verify it was initialized with AudioFileManager
            XCTAssertNotNil(service.audioFileManager)
            XCTAssertTrue(service.audioFileManager is NeuroLoopInterfaces.AudioFileManager)
        #else
            // On macOS, verify it was initialized without AudioFileManager
            XCTAssertNil(service.audioFileManager)
        #endif
    }

    func testServiceReuse() async {
        // Get the service instance twice
        let service1 = serviceFactory.getAudioRecordingService()
        let service2 = serviceFactory.getAudioRecordingService()

        // Verify it's the same instance (cached)
        XCTAssertTrue(service1 === service2)
    }
}

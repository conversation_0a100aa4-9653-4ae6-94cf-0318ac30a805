import XCTest
import Combine
import SwiftUI
import <PERSON>euroLoop<PERSON>ore
@testable import <PERSON>euro<PERSON><PERSON><PERSON>
@testable import NeuroLoopInterfaces
@testable import NeuroLoopModels
import NeuroLoopTestUtilities

class AffirmationSystemTests: XCTestCase {
    var app: XCUIApplication!
    var affirmationService: MockAffirmationService!
    var repetitionService: MockRepetitionService!
    var streakService: MockStreakService!
    var audioService: MockAudioRecordingService!

    override func setUp() {
        super.setUp()
        app = XCUIApplication()
        app.launchArguments = ["--uitesting"]
        app.launch()

        affirmationService = MockAffirmationService()
        repetitionService = MockRepetitionService()
        streakService = MockStreakService()
        audioService = MockAudioRecordingService()
    }

    override func tearDown() {
        affirmationService = nil
        repetitionService = nil
        streakService = nil
        audioService = nil
        super.tearDown()
    }

    // MARK: - Affirmation Creation Tests

    func testCreateAffirmation() async throws {
        // Given
        let text = "I am confident and capable"
        let category = AffirmationCategory.confidence

        // When
        let affirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )

        // Then
        XCTAssertEqual(affirmation.text, text)
        XCTAssertEqual(affirmation.category, category)
        XCTAssertFalse(affirmation.hasActiveCycle)
        XCTAssertEqual(affirmation.currentRepetitions, 0)
        XCTAssertEqual(affirmation.completedCycles, 0)

        // Verify it was added to the service
        let affirmations = try await affirmationService.fetchAffirmations()
        XCTAssertTrue(affirmations.contains { $0.id == affirmation.id })
    }

    func testCreateAffirmationWithRecording() async throws {
        // Given
        let text = "I am confident and capable"
        let category = AffirmationCategory.confidence
        let recordingURL = URL(string: "file:///test-recording.m4a")!

        // When
        let affirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )

        // Then
        XCTAssertEqual(affirmation.text, text)
        XCTAssertEqual(affirmation.category, category)
        XCTAssertEqual(affirmation.recordingURL, recordingURL)
    }

    // MARK: - Affirmation Editing Tests

    func testUpdateAffirmation() async throws {
        // Given
        let originalText = "Original affirmation"
        let updatedText = "Updated affirmation"
        let category = AffirmationCategory.confidence

        let affirmation = try await affirmationService.createAffirmation(
            text: originalText,
            category: category,
            recordingURL: nil
        )

        // When
        affirmation.text = updatedText
        try await affirmationService.updateAffirmation(affirmation)

        // Then
        let updatedAffirmation = try await affirmationService.fetchAffirmation(id: affirmation.id)
        XCTAssertEqual(updatedAffirmation?.text, updatedText)
    }

    func testToggleFavorite() async throws {
        // Given
        let text = "Test affirmation"
        let category = AffirmationCategory.confidence

        let affirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )

        let initialFavoriteStatus = affirmation.isFavorite

        // When
        let updatedAffirmation = try await affirmationService.toggleFavorite(affirmation)

        // Then
        XCTAssertEqual(updatedAffirmation.isFavorite, !initialFavoriteStatus)

        // Toggle back
        let toggledBackAffirmation = try await affirmationService.toggleFavorite(updatedAffirmation)

        // Then
        XCTAssertEqual(toggledBackAffirmation.isFavorite, initialFavoriteStatus)
    }

    // MARK: - Affirmation Deletion Tests

    func testDeleteAffirmation() async throws {
        // Given
        let text = "Affirmation to delete"
        let category = AffirmationCategory.confidence

        let affirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )

        let initialCount = (try await affirmationService.fetchAffirmations()).count

        // When
        try await affirmationService.deleteAffirmation(id: affirmation.id)

        // Then
        let finalCount = (try await affirmationService.fetchAffirmations()).count
        let deletedAffirmation = try await affirmationService.fetchAffirmation(id: affirmation.id)

        XCTAssertNil(deletedAffirmation)
        XCTAssertEqual(finalCount, initialCount - 1)
    }

    // MARK: - Cycle Management Tests

    func testStartCycle() async throws {
        // Given
        let text = "Test affirmation for cycle"
        let category = AffirmationCategory.confidence

        let affirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )

        // When
        try await affirmationService.startCycle(for: affirmation)

        // Then
        let updatedAffirmation = try await affirmationService.fetchAffirmation(id: affirmation.id)
        XCTAssertNotNil(updatedAffirmation)
        XCTAssertTrue(updatedAffirmation!.hasActiveCycle)
        XCTAssertEqual(updatedAffirmation!.currentCycleDay, 1)
        XCTAssertNotNil(updatedAffirmation!.cycleStartDate)
    }

    func testRecordRepetition() async throws {
        // Given
        let text = "Test affirmation for repetition"
        let category = AffirmationCategory.confidence

        let affirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )

        try await affirmationService.startCycle(for: affirmation)

        let beforeRepetition = try await affirmationService.fetchAffirmation(id: affirmation.id)
        let initialRepetitions = beforeRepetition!.currentRepetitions

        // When
        let updatedAffirmation = try await affirmationService.recordRepetition(for: beforeRepetition!)

        // Then
        XCTAssertEqual(updatedAffirmation.currentRepetitions, initialRepetitions + 1)
        XCTAssertNotNil(updatedAffirmation.lastRepetitionDate)
    }

    func testCompleteCycle() async throws {
        // Given
        let text = "Test affirmation for completing cycle"
        let category = AffirmationCategory.confidence

        let affirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        ) as! Affirmation

        try await affirmationService.startCycle(for: affirmation)

        // Simulate completing a cycle (100 repetitions)
        for _ in 1...100 {
            _ = try await affirmationService.recordRepetition(for: affirmation)
        }

        // Then
        let completedAffirmation = try await affirmationService.fetchAffirmation(id: affirmation.id)
        XCTAssertNotNil(completedAffirmation)
        XCTAssertEqual(completedAffirmation!.completedCycles, 1)
        XCTAssertFalse(completedAffirmation!.hasActiveCycle)
    }

    // MARK: - End-to-End Tests

    func testEndToEndAffirmationFlow() async throws {
        // 1. Create an affirmation
        let text = "I am successful in everything I do"
        let category = AffirmationCategory.success

        let affirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: nil
        )

        XCTAssertEqual(affirmation.text, text)
        XCTAssertEqual(affirmation.category, category)

        // 2. Start a cycle
        try await affirmationService.startCycle(for: affirmation)

        var updatedAffirmation = try await affirmationService.fetchAffirmation(id: affirmation.id)
        XCTAssertTrue(updatedAffirmation!.hasActiveCycle)

        // 3. Record repetitions for a day
        for _ in 1...15 { // Assuming daily goal is 15
            updatedAffirmation = try await affirmationService.recordRepetition(for: updatedAffirmation!)
        }

        XCTAssertEqual(updatedAffirmation!.currentRepetitions, 15)

        // 4. Toggle favorite status
        let favoriteAffirmation = try await affirmationService.toggleFavorite(updatedAffirmation!)
        XCTAssertTrue(favoriteAffirmation.isFavorite)

        // 5. Update energy level
        let energyLevel = 0.8
        let energyUpdatedAffirmation = try await affirmationService.updateEnergyLevel(energyLevel, for: favoriteAffirmation)
        XCTAssertEqual(energyUpdatedAffirmation.energyLevel, energyLevel)

        // 6. Record mood
        let moodRating = 5
        let notes = "Feeling great today!"
        let moodUpdatedAffirmation = try await affirmationService.recordMood(moodRating, notes: notes, for: energyUpdatedAffirmation)
        XCTAssertEqual(moodUpdatedAffirmation.moodRating, moodRating)

        // 7. Complete the cycle (simulate remaining repetitions)
        var currentAffirmation = moodUpdatedAffirmation
        for _ in 16...100 {
            currentAffirmation = try await affirmationService.recordRepetition(for: currentAffirmation)
        }

        // 8. Verify cycle completion
        let finalAffirmation = try await affirmationService.fetchAffirmation(id: affirmation.id)
        XCTAssertEqual(finalAffirmation!.completedCycles, 1)
        XCTAssertFalse(finalAffirmation!.hasActiveCycle)
        XCTAssertEqual(finalAffirmation!.currentRepetitions, 100)

        // 9. Start a new cycle
        try await affirmationService.startCycle(for: finalAffirmation!)

        let newCycleAffirmation = try await affirmationService.fetchAffirmation(id: affirmation.id)
        XCTAssertTrue(newCycleAffirmation!.hasActiveCycle)
        XCTAssertEqual(newCycleAffirmation!.currentCycleDay, 1)
        XCTAssertEqual(newCycleAffirmation!.currentRepetitions, 0)

        // 10. Delete the affirmation
        try await affirmationService.deleteAffirmation(id: affirmation.id)

        let deletedAffirmation = try await affirmationService.fetchAffirmation(id: affirmation.id)
        XCTAssertNil(deletedAffirmation)
    }

    func testAffirmationWithAudioRecording() async throws {
        // 1. Create an affirmation with recording
        let text = "I speak with confidence and clarity"
        let category = AffirmationCategory.confidence
        let recordingURL = URL(string: "file:///test-recording.m4a")!

        let affirmation = try await affirmationService.createAffirmation(
            text: text,
            category: category,
            recordingURL: recordingURL
        )

        XCTAssertEqual(affirmation.recordingURL, recordingURL)

        // 2. Start a cycle
        try await affirmationService.startCycle(for: affirmation)

        // 3. Play the recording
        try await audioService.startPlayback(from: recordingURL)
        XCTAssertTrue(audioService.didStartPlayback)

        // 4. Pause the playback
        audioService.pausePlayback()
        XCTAssertTrue(audioService.didPausePlayback)

        // 5. Record repetition after listening
        let updatedAffirmation = try await affirmationService.recordRepetition(for: affirmation)
        XCTAssertEqual(updatedAffirmation.currentRepetitions, 1)
    }
}

// MARK: - Mock Services

class MockAffirmationService: AffirmationServiceProtocol {
    private var affirmations: [AffirmationProtocol] = []
    var errorToThrow: Error?

    func fetchAffirmations() async throws -> [AffirmationProtocol] {
        if let error = errorToThrow {
            throw error
        }
        return affirmations
    }

    func fetchAffirmation(id: UUID) async throws -> AffirmationProtocol? {
        if let error = errorToThrow {
            throw error
        }
        return affirmations.first { $0.id == id }
    }

    func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?) async throws -> AffirmationProtocol {
        if let error = errorToThrow {
            throw error
        }

        let newAffirmation = AffirmationStub(
            id: UUID(),
            text: text,
            category: category,
            recordingURL: recordingURL
        )

        affirmations.append(newAffirmation)
        return newAffirmation
    }

    func updateAffirmation(_ affirmation: AffirmationProtocol) async throws {
        if let error = errorToThrow {
            throw error
        }

        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            affirmations[index] = affirmation
        } else {
            throw AffirmationError.notFound
        }
    }

    func deleteAffirmation(id: UUID) async throws {
        if let error = errorToThrow {
            throw error
        }

        affirmations.removeAll { $0.id == id }
    }

    func fetchAffirmations(category: AffirmationCategory) async throws -> [AffirmationProtocol] {
        if let error = errorToThrow {
            throw error
        }

        return affirmations.filter { $0.category == category }
    }

    func fetchFavoriteAffirmations() async throws -> [AffirmationProtocol] {
        if let error = errorToThrow {
            throw error
        }

        return affirmations.filter { $0.isFavorite }
    }

    func fetchCurrentAffirmation() async throws -> AffirmationProtocol? {
        if let error = errorToThrow {
            throw error
        }

        return affirmations.first { $0.hasActiveCycle }
    }

    func startCycle(for affirmation: AffirmationProtocol) async throws {
        if let error = errorToThrow {
            throw error
        }

        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let mockAffirmation = affirmations[index] as! AffirmationStub
            mockAffirmation.hasActiveCycle = true
            mockAffirmation.currentCycleDay = 1
            mockAffirmation.cycleStartDate = Date()
            mockAffirmation.currentRepetitions = 0
        } else {
            throw AffirmationError.notFound
        }
    }

    func recordRepetition(for affirmation: AffirmationProtocol) async throws -> AffirmationProtocol {
        if let error = errorToThrow {
            throw error
        }

        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let mockAffirmation = affirmations[index] as! AffirmationStub
            try mockAffirmation.recordRepetition()

            // Check if cycle is complete (100 repetitions)
            if mockAffirmation.currentRepetitions >= 100 {
                mockAffirmation.completedCycles += 1
                mockAffirmation.hasActiveCycle = false
            }

            return mockAffirmation
        } else {
            throw AffirmationError.notFound
        }
    }

    func toggleFavorite(_ affirmation: AffirmationProtocol) async throws -> AffirmationProtocol {
        if let error = errorToThrow {
            throw error
        }

        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let mockAffirmation = affirmations[index] as! AffirmationStub
            mockAffirmation.isFavorite.toggle()
            return mockAffirmation
        } else {
            throw AffirmationError.notFound
        }
    }

    func updateEnergyLevel(_ level: Double, for affirmation: AffirmationProtocol) async throws -> AffirmationProtocol {
        if let error = errorToThrow {
            throw error
        }

        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let mockAffirmation = affirmations[index] as! AffirmationStub
            mockAffirmation.energyLevel = level
            return mockAffirmation
        } else {
            throw AffirmationError.notFound
        }
    }

    func recordMood(_ rating: Int, notes: String?, for affirmation: AffirmationProtocol) async throws -> AffirmationProtocol {
        if let error = errorToThrow {
            throw error
        }

        if let index = affirmations.firstIndex(where: { $0.id == affirmation.id }) {
            let mockAffirmation = affirmations[index] as! AffirmationStub
            mockAffirmation.moodRating = rating
            mockAffirmation.notes = notes
            return mockAffirmation
        } else {
            throw AffirmationError.notFound
        }
    }
}

class MockRepetitionService: RepetitionServiceProtocol {
    var didRecordRepetition = false
    var errorToThrow: Error?

    func recordRepetition(for affirmation: AffirmationProtocol) async throws -> RepetitionResult {
        if let error = errorToThrow {
            throw error
        }

        didRecordRepetition = true
        return RepetitionResult(
            affirmation: affirmation,
            isMilestoneReached: affirmation.currentRepetitions + 1 >= affirmation.dailyGoal
        )
    }

    func startCycle(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }

    func startSession(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }

    func getProgress(for affirmation: AffirmationProtocol) -> ProgressInfo {
        return ProgressInfo(
            todayProgress: 0,
            cycleProgress: 0,
            currentDay: 1,
            totalDays: 7,
            currentRepetitions: 0,
            totalRepetitions: 0,
            hasTodayQuotaMet: false,
            isCycleComplete: false,
            hasActiveCycle: false
        )
    }

    func getStreakInfo(for affirmation: AffirmationProtocol) -> StreakInfo {
        return StreakInfo(
            currentStreak: 0,
            longestStreak: 0,
            completedCycles: 0,
            hasActiveCycle: false,
            cycleStartDate: nil,
            lastRepetitionDate: nil
        )
    }

    func canPerformRepetition(for affirmation: AffirmationProtocol) -> Bool { true }

    func timeUntilNextRepetition(for affirmation: AffirmationProtocol) -> TimeInterval? { nil }

    func restartBrokenCycle(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }

    func isCycleBroken(for affirmation: AffirmationProtocol) -> Bool { false }
}

class MockStreakService: StreakServiceProtocol {
    var didUpdateStreak = false
    var errorToThrow: Error?

    func updateStreak(for affirmation: AffirmationProtocol) async throws {
        if let error = errorToThrow {
            throw error
        }

        didUpdateStreak = true
    }
}

class MockAudioRecordingService: AudioRecordingServiceProtocol {
    var didStartPlayback = false
    var didPausePlayback = false
    var errorToThrow: Error?

    // Required properties
    var isRecording: Bool = false
    var recordingTime: TimeInterval = 0
    var recordingPower: Double = 0
    var recordingURL: URL? = nil
    var error: Error? = nil

    var isPlaying: Bool {
        return didStartPlayback && !didPausePlayback
    }

    var playbackProgress: Double {
        return isPlaying ? 0.5 : 0.0
    }

    var playbackTime: TimeInterval {
        return isPlaying ? 15.0 : 0.0
    }

    // Publishers for SwiftUI bindings
    var isRecordingPublisher: Published<Bool>.Publisher { $isRecording }
    var recordingTimePublisher: Published<TimeInterval>.Publisher { $recordingTime }
    var recordingPowerPublisher: Published<Double>.Publisher { $recordingPower }
    var recordingURLPublisher: Published<URL?>.Publisher { $recordingURL }
    var isPlayingPublisher: Published<Bool>.Publisher { $isPlaying }
    var playbackProgressPublisher: Published<Double>.Publisher { $playbackProgress }
    var playbackTimePublisher: Published<TimeInterval>.Publisher { $playbackTime }
    var errorPublisher: Published<Error?>.Publisher { $error }

    // Published properties for SwiftUI/reactive use
    @Published var _isRecording: Bool = false
    @Published var _recordingTime: TimeInterval = 0
    @Published var _recordingPower: Double = 0
    @Published var _recordingURL: URL? = nil
    @Published var _isPlaying: Bool = false
    @Published var _playbackProgress: Double = 0
    @Published var _playbackTime: TimeInterval = 0
    @Published var _error: Error? = nil

    func startRecording() async throws {
        isRecording = true
    }

    func stopRecording() async throws -> URL {
        isRecording = false
        return URL(string: "file:///test.m4a")!
    }

    func deleteRecording() {
        // No-op for tests
    }

    func startPlayback() async throws {
        if let error = errorToThrow {
            throw error
        }
        didStartPlayback = true
    }

    func startPlayback(from url: URL) async throws {
        if let error = errorToThrow {
            throw error
        }
        didStartPlayback = true
    }

    func pausePlayback() {
        didPausePlayback = true
    }

    func stopPlayback() {
        // Implementation not needed for tests
    }

    func deleteRecording(at url: URL) async throws {
        // No-op for tests
    }

    func getRecordingDuration(for url: URL) async throws -> TimeInterval {
        return 5.0
    }

    func getRecordingWaveform(for url: URL) async throws -> [Float] {
        return [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    }

    /// Diagnostic method to test microphone access and audio session setup
    func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double) {
        return (true, "Mock microphone test successful", -30.0)
    }
}

// MARK: - Mock Models

class AffirmationStub: AffirmationProtocol {
    var id: UUID
    var text: String
    var category: AffirmationCategory
    var recordingURL: URL?
    var createdAt: Date
    var updatedAt: Date
    var currentCycleDay: Int
    var cycleStartDate: Date?
    var completedCycles: Int
    var currentRepetitions: Int
    var totalRepetitions: Int
    var dailyProgress: [Date: Int]
    var lastRepetitionDate: Date?
    var energyLevel: Double
    var moodRating: Int?
    var notes: String?
    var isFavorite: Bool
    var playCount: Int
    var hasActiveCycle: Bool
    var dailyGoal: Int
    var todayRepetitions: Int
    var cycleProgress: Double

    var isCurrentCycleComplete: Bool {
        return currentRepetitions >= 100
    }

    var hasTodayQuotaMet: Bool {
        return todayRepetitions >= dailyGoal
    }

    var todayProgress: Double {
        return min(Double(todayRepetitions) / Double(dailyGoal), 1.0)
    }

    init(
        id: UUID = UUID(),
        text: String,
        category: AffirmationCategory = .custom,
        recordingURL: URL? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        currentCycleDay: Int = 1,
        cycleStartDate: Date? = nil,
        completedCycles: Int = 0,
        currentRepetitions: Int = 0,
        totalRepetitions: Int = 0,
        dailyProgress: [Date: Int] = [:],
        lastRepetitionDate: Date? = nil,
        energyLevel: Double = 0.5,
        moodRating: Int? = nil,
        notes: String? = nil,
        isFavorite: Bool = false,
        playCount: Int = 0,
        hasActiveCycle: Bool = false,
        dailyGoal: Int = 15,
        todayRepetitions: Int = 0,
        cycleProgress: Double = 0.0
    ) {
        self.id = id
        self.text = text
        self.category = category
        self.recordingURL = recordingURL
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.currentCycleDay = currentCycleDay
        self.cycleStartDate = cycleStartDate
        self.completedCycles = completedCycles
        self.currentRepetitions = currentRepetitions
        self.totalRepetitions = totalRepetitions
        self.dailyProgress = dailyProgress
        self.lastRepetitionDate = lastRepetitionDate
        self.energyLevel = energyLevel
        self.moodRating = moodRating
        self.notes = notes
        self.isFavorite = isFavorite
        self.playCount = playCount
        self.hasActiveCycle = hasActiveCycle
        self.dailyGoal = dailyGoal
        self.todayRepetitions = todayRepetitions
        self.cycleProgress = cycleProgress
    }

    func recordRepetition() throws {
        currentRepetitions += 1
        totalRepetitions += 1
        todayRepetitions += 1
        lastRepetitionDate = Date()

        // Update daily progress
        let today = Calendar.current.startOfDay(for: Date())
        dailyProgress[today, default: 0] += 1

        // Update cycle progress
        cycleProgress = min(Double(currentRepetitions) / 100.0, 1.0)
    }

    func updateEnergyLevel(_ level: Double) {
        energyLevel = min(max(level, 0.0), 1.0)
        updatedAt = Date()
    }

    func recordMood(_ rating: Int, notes: String?) {
        moodRating = rating
        if let notes = notes {
            self.notes = notes
        }
        updatedAt = Date()
    }
}

// MARK: - Helper Types

struct RepetitionResult {
    let affirmation: AffirmationProtocol
    let isMilestoneReached: Bool
}

protocol RepetitionServiceProtocol {
    func recordRepetition(for affirmation: AffirmationProtocol) async throws -> RepetitionResult
}

protocol StreakServiceProtocol {
    func updateStreak(for affirmation: AffirmationProtocol) async throws
}

protocol AudioRecordingServiceProtocol {
    func startPlayback(from url: URL) async throws
    func pausePlayback()
    func stopPlayback()
    var isPlaying: Bool { get }
    var playbackProgress: Double { get }
    var playbackTime: TimeInterval { get }
}

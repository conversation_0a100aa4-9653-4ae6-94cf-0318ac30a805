import XCTest
import Combine
import SwiftUI
import NeuroLoopCore
@testable import NeuroLoop<PERSON>
@testable import NeuroLoopInterfaces
@testable import NeuroLoopModels
import NeuroLoopTestUtilities

/// Integration tests for the streak calculation and visualization system
class StreakSystemTests: XCTestCase {
    var app: XCUIApplication!
    var affirmationService: MockAffirmationService!
    var repetitionService: MockRepetitionService!
    var streakService: MockStreakService!
    var dateProvider: MockDateProvider!
    var viewModel: StreakTrackingViewModel!

    override func setUp() {
        super.setUp()
        app = XCUIApplication()
        app.launchArguments = ["--uitesting"]
        app.launch()
        affirmationService = MockAffirmationService()
        repetitionService = MockRepetitionService()
        streakService = MockStreakService()
        dateProvider = MockDateProvider()
        viewModel = StreakTrackingViewModel(
            affirmationService: affirmationService,
            repetitionService: repetitionService,
            streakService: streakService,
            dateProvider: dateProvider
        )
    }

    override func tearDown() {
        viewModel = nil
        affirmationService = nil
        repetitionService = nil
        streakService = nil
        dateProvider = nil
        super.tearDown()
    }

    // MARK: - Test: Streak initialization for new affirmations
    func testStreakInitializationForNewAffirmation() async {
        let affirmation = AffirmationStub(id: UUID(), text: "Streak Init", currentCycleDay: 1)
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        XCTAssertEqual(viewModel.currentStreak, 1)
        XCTAssertEqual(viewModel.streakDays.count, 1)
    }

    // MARK: - Test: Streak calculation based on daily repetitions
    func testStreakCalculationBasedOnDailyRepetitions() async {
        let baseDate = Date()
        dateProvider.currentDate = baseDate
        let affirmation = AffirmationStub(id: UUID(), text: "Streak Calc", currentCycleDay: 1)
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Simulate 3 consecutive days of meeting daily goal
        for i in 0..<3 {
            dateProvider.currentDate = Calendar.current.date(byAdding: .day, value: i, to: baseDate)!
            await viewModel.performRepetition(for: affirmation.id, goalMet: true)
        }
        XCTAssertEqual(viewModel.currentStreak, 3)
    }

    // MARK: - Test: Streak continuation when meeting daily goals
    func testStreakContinuation() async {
        let baseDate = Date()
        dateProvider.currentDate = baseDate
        let affirmation = AffirmationStub(id: UUID(), text: "Streak Continue", currentCycleDay: 1)
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Meet goal for 5 days
        for i in 0..<5 {
            dateProvider.currentDate = Calendar.current.date(byAdding: .day, value: i, to: baseDate)!
            await viewModel.performRepetition(for: affirmation.id, goalMet: true)
        }
        XCTAssertEqual(viewModel.currentStreak, 5)
    }

    // MARK: - Test: Streak breaking when missing daily goals
    func testStreakBreaksWhenMissingGoal() async {
        let baseDate = Date()
        dateProvider.currentDate = baseDate
        let affirmation = AffirmationStub(id: UUID(), text: "Streak Break", currentCycleDay: 1)
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Meet goal for 2 days
        for i in 0..<2 {
            dateProvider.currentDate = Calendar.current.date(byAdding: .day, value: i, to: baseDate)!
            await viewModel.performRepetition(for: affirmation.id, goalMet: true)
        }
        // Miss goal on day 3
        dateProvider.currentDate = Calendar.current.date(byAdding: .day, value: 2, to: baseDate)!
        await viewModel.performRepetition(for: affirmation.id, goalMet: false)
        XCTAssertEqual(viewModel.currentStreak, 0)
    }

    // MARK: - Test: Streak visualization in calendar and timeline views
    func testStreakVisualizationCalendarAndTimeline() async {
        let baseDate = Date()
        dateProvider.currentDate = baseDate
        let affirmation = AffirmationStub(id: UUID(), text: "Streak Visual", currentCycleDay: 1)
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Simulate 7 days of streak
        for i in 0..<7 {
            dateProvider.currentDate = Calendar.current.date(byAdding: .day, value: i, to: baseDate)!
            await viewModel.performRepetition(for: affirmation.id, goalMet: true)
        }
        // Check streakDays for visualization
        XCTAssertEqual(viewModel.streakDays.count, 7)
        XCTAssertTrue(viewModel.streakDays.allSatisfy { $0.isComplete })
    }

    // MARK: - Test: Streak statistics and milestone achievements
    func testStreakStatisticsAndMilestones() async {
        let baseDate = Date()
        dateProvider.currentDate = baseDate
        let affirmation = AffirmationStub(id: UUID(), text: "Streak Stats", currentCycleDay: 1)
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Simulate 10 days of streak
        for i in 0..<10 {
            dateProvider.currentDate = Calendar.current.date(byAdding: .day, value: i, to: baseDate)!
            await viewModel.performRepetition(for: affirmation.id, goalMet: true)
        }
        XCTAssertEqual(viewModel.currentStreak, 10)
        XCTAssertTrue(viewModel.hasAchievedMilestone)
    }

    // MARK: - Test: Edge cases (date changes, time zones, app restarts)
    func testStreakEdgeCases() async {
        let baseDate = Date()
        dateProvider.currentDate = baseDate
        let affirmation = AffirmationStub(id: UUID(), text: "Streak Edge", currentCycleDay: 1)
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Simulate time zone change
        let timeZone = TimeZone(secondsFromGMT: 3600 * 5)! // UTC+5
        dateProvider.timeZone = timeZone
        dateProvider.currentDate = Calendar.current.date(byAdding: .day, value: 1, to: baseDate)!
        await viewModel.performRepetition(for: affirmation.id, goalMet: true)
        // Simulate app restart
        tearDown()
        setUp()
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        XCTAssertEqual(viewModel.currentStreak, 2)
    }
}

// MARK: - Test Helpers & Mocks

/// Mock date provider for controlling current date and time zone
class MockDateProvider: DateProviderProtocol {
    var currentDate: Date = Date()
    var timeZone: TimeZone = .current
    func now() -> Date { currentDate }
    func currentTimeZone() -> TimeZone { timeZone }
}

/// Mock affirmation for testing
struct AffirmationStub: AffirmationProtocol {
    var id: UUID = UUID()
    var text: String = ""
    var category: AffirmationCategory = .confidence
    var recordingURL: URL? = nil
    var createdAt: Date = Date()
    var updatedAt: Date = Date()
    var currentCycleDay: Int = 1
    var cycleStartDate: Date? = Date()
    var completedCycles: Int = 0
    var currentRepetitions: Int = 0
    var dailyProgress: [Date: Int] = [:]
    var lastRepetitionDate: Date? = nil
    var energyLevel: Double = 1.0
    var moodRating: Int? = nil
    var notes: String? = nil
    var isFavorite: Bool = false
    var playCount: Int = 0
    var hasActiveCycle: Bool = true
    var dailyGoal: Int = 3
    var todayRepetitions: Int = 0
    var cycleProgress: Double = 0.0
    var canPerformRepetition: Bool { todayRepetitions < dailyGoal }
    var isCurrentCycleComplete: Bool { todayRepetitions >= dailyGoal }
    var todayProgress: Double { Double(todayRepetitions) / Double(dailyGoal) }
    mutating func recordRepetition() throws { todayRepetitions += 1 }
    mutating func updateEnergyLevel(_ level: Double) { energyLevel = level }
    mutating func recordMood(_ rating: Int, notes: String?) { moodRating = rating; notes.map { self.notes = $0 } }
}

/// Mock repetition service
class MockRepetitionService: RepetitionServiceProtocol {
    var didRecordRepetition = false
    var errorToThrow: Error?
    func recordRepetition(for affirmation: AffirmationProtocol) async throws -> RepetitionResult {
        if let error = errorToThrow { throw error }
        didRecordRepetition = true
        var updated = affirmation as? AffirmationStub ?? AffirmationStub()
        updated.todayRepetitions += 1
        return RepetitionResult(affirmation: updated, isMilestoneReached: updated.todayRepetitions >= updated.dailyGoal)
    }
    func startCycle(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }
    func startSession(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }
    func getProgress(for affirmation: AffirmationProtocol) -> ProgressInfo {
        return ProgressInfo(
            todayProgress: 0,
            cycleProgress: 0,
            currentDay: 1,
            totalDays: 7,
            currentRepetitions: 0,
            totalRepetitions: 0,
            hasTodayQuotaMet: false,
            isCycleComplete: false,
            hasActiveCycle: false
        )
    }
    func getStreakInfo(for affirmation: AffirmationProtocol) -> StreakInfo {
        return StreakInfo(
            currentStreak: 0,
            longestStreak: 0,
            completedCycles: 0,
            hasActiveCycle: false,
            cycleStartDate: nil,
            lastRepetitionDate: nil
        )
    }
    func canPerformRepetition(for affirmation: AffirmationProtocol) -> Bool { true }
    func timeUntilNextRepetition(for affirmation: AffirmationProtocol) -> TimeInterval? { nil }
    func restartBrokenCycle(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }
    func isCycleBroken(for affirmation: AffirmationProtocol) -> Bool { false }
}

/// Mock streak service
class MockStreakService: StreakServiceProtocol {
    var didUpdateStreak = false
    var errorToThrow: Error?
    func updateStreak(for affirmation: AffirmationProtocol) async throws {
        if let error = errorToThrow { throw error }
        didUpdateStreak = true
    }
}

// MARK: - Documentation
// Test helpers and mocks are provided for isolation and repeatability. The MockDateProvider allows simulation of date, time zone, and app restart scenarios. Each test creates fresh instances to ensure no state leakage between tests. The streak system is tested end-to-end, including edge cases and visualization logic. 
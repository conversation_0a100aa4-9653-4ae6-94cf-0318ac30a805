import XCTest
import Combine
import <PERSON><PERSON>
@testable import NeuroLoop<PERSON>ore
@testable import NeuroLoopUI
@testable import NeuroLoopInterfaces
@testable import NeuroLoopModels
import NeuroLoopTestUtilities

/// Integration tests for the full repetition tracking system
class RepetitionSystemTests: XCTestCase {
    var app: XCUIApplication!
    var affirmationService: MockAffirmationService!
    var repetitionService: MockRepetitionService!
    var streakService: MockStreakService!
    var audioService: MockAudioRecordingService!
    var viewModel: RepetitionTrackingViewModel!

    override func setUp() {
        super.setUp()
        app = XCUIApplication()
        app.launchArguments = ["--uitesting"]
        app.launch()
        affirmationService = MockAffirmationService()
        repetitionService = MockRepetitionService()
        streakService = MockStreakService()
        audioService = MockAudioRecordingService()
        viewModel = RepetitionTrackingViewModel(
            affirmationService: affirmationService,
            repetitionService: repetitionService,
            streakService: streakService,
            audioService: audioService
        )
    }

    override func tearDown() {
        viewModel = nil
        affirmationService = nil
        repetitionService = nil
        streakService = nil
        audioService = nil
        super.tearDown()
    }

    // MARK: - Test: Creating a new repetition for an affirmation
    func testCreateNewRepetition() async {
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Test repetition",
            category: .confidence,
            todayRepetitions: 0,
            dailyGoal: 3,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        XCTAssertEqual(viewModel.todayRepetitions, 0)
        await viewModel.performRepetition()
        XCTAssertEqual(viewModel.todayRepetitions, 1)
    }

    // MARK: - Test: Tracking repetition progress over time
    func testRepetitionProgressOverTime() async {
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Progress test",
            category: .focus,
            todayRepetitions: 0,
            dailyGoal: 4,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        for i in 1...4 {
            await viewModel.performRepetition()
            XCTAssertEqual(viewModel.todayRepetitions, i)
            XCTAssertEqual(viewModel.todayProgress, Double(i)/4.0, accuracy: 0.01)
        }
    }

    // MARK: - Test: Verifying streak calculation based on repetition patterns
    func testStreakCalculation() async {
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Streak test",
            category: .gratitude,
            todayRepetitions: 0,
            dailyGoal: 2,
            currentDay: 3,
            cycleProgress: 0.3
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Simulate streak update
        try? await streakService.updateStreak(for: affirmation)
        XCTAssertTrue(streakService.didUpdateStreak)
    }

    // MARK: - Test: Testing milestone achievements and celebrations
    func testMilestoneAchievementAndCelebration() async {
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Milestone test",
            category: .success,
            todayRepetitions: 2,
            dailyGoal: 3,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        XCTAssertFalse(viewModel.isMilestoneReached)
        await viewModel.performRepetition()
        XCTAssertTrue(viewModel.isMilestoneReached)
        // Wait for celebration reset
        try? await Task.sleep(nanoseconds: 3_000_000_000)
        XCTAssertFalse(viewModel.isMilestoneReached)
    }

    // MARK: - Test: Verifying repetition limits and cooldowns
    func testRepetitionLimitsAndCooldowns() async {
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Cooldown test",
            category: .focus,
            todayRepetitions: 3,
            dailyGoal: 3,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        XCTAssertFalse(viewModel.canPerformRepetition)
        // Try to perform repetition (should not increment)
        await viewModel.performRepetition()
        XCTAssertEqual(viewModel.todayRepetitions, 3)
    }

    // MARK: - Test: Synchronization between repetition data and UI
    func testRepetitionDataUISynchronization() async {
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Sync test",
            category: .gratitude,
            todayRepetitions: 1,
            dailyGoal: 2,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Simulate UI observing todayProgress
        let progress = viewModel.todayProgress
        await viewModel.performRepetition()
        XCTAssertNotEqual(viewModel.todayProgress, progress)
    }

    // MARK: - Test: Error handling for repetition system
    func testRepetitionErrorHandling() async {
        let error = NSError(domain: "Test", code: 42, userInfo: nil)
        repetitionService.errorToThrow = error
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Error test",
            category: .focus,
            todayRepetitions: 0,
            dailyGoal: 2,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        await viewModel.performRepetition()
        XCTAssertNotNil(viewModel.error)
        XCTAssertEqual((viewModel.error as NSError?)?.code, 42)
    }

    // MARK: - Test: Data persistence across app restarts
    func testPersistenceAcrossAppRestarts() async {
        // Given
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Persistence test",
            category: .confidence,
            todayRepetitions: 2,
            dailyGoal: 3,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        await viewModel.performRepetition() // 3/3
        XCTAssertEqual(viewModel.todayRepetitions, 3)
        XCTAssertTrue(viewModel.isMilestoneReached)

        // Simulate app restart by re-initializing services and view model
        let newAffirmationService = MockAffirmationService()
        newAffirmationService.affirmationToReturn = affirmation
        let newRepetitionService = MockRepetitionService()
        let newStreakService = MockStreakService()
        let newAudioService = MockAudioRecordingService()
        let newViewModel = RepetitionTrackingViewModel(
            affirmationService: newAffirmationService,
            repetitionService: newRepetitionService,
            streakService: newStreakService,
            audioService: newAudioService
        )
        await newViewModel.loadData(for: affirmation.id)
        // Then
        XCTAssertEqual(newViewModel.todayRepetitions, 3)
        XCTAssertTrue(newViewModel.isMilestoneReached)
    }

    // MARK: - Test: Repetition at date boundaries (midnight)
    func testRepetitionAtDateBoundaries() async {
        // Given
        let calendar = Calendar.current
        let now = Date()
        let beforeMidnight = calendar.date(bySettingHour: 23, minute: 59, second: 0, of: now) ?? now
        let afterMidnight = calendar.date(byAdding: .minute, value: 2, to: beforeMidnight) ?? now
        var affirmation = AffirmationStub(
            id: UUID(),
            text: "Date boundary test",
            category: .focus,
            todayRepetitions: 0,
            dailyGoal: 2,
            currentDay: 1,
            cycleProgress: 0.0,
            lastRepetitionDate: beforeMidnight
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Simulate repetition just before midnight
        affirmation.todayRepetitions = 1
        affirmation.lastRepetitionDate = beforeMidnight
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        XCTAssertEqual(viewModel.todayRepetitions, 1)
        // Simulate repetition just after midnight (should reset)
        affirmation.todayRepetitions = 0
        affirmation.currentDay = 2
        affirmation.lastRepetitionDate = afterMidnight
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        XCTAssertEqual(viewModel.todayRepetitions, 0)
        XCTAssertEqual(viewModel.currentDay, 2)
    }

    // MARK: - Test: Timezone change handling
    func testTimezoneChangeHandling() async {
        // Given
        let utc = TimeZone(secondsFromGMT: 0)!
        let pst = TimeZone(identifier: "America/Los_Angeles")!
        let calendar = Calendar(identifier: .gregorian)
        let now = Date()
        let utcDate = now
        let pstDate = now.addingTimeInterval(-8 * 3600) // Simulate 8 hours behind
        var affirmation = AffirmationStub(
            id: UUID(),
            text: "Timezone test",
            category: .gratitude,
            todayRepetitions: 1,
            dailyGoal: 2,
            currentDay: 1,
            cycleProgress: 0.0,
            lastRepetitionDate: utcDate
        )
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        XCTAssertEqual(viewModel.todayRepetitions, 1)
        // Simulate timezone change (user travels to PST)
        affirmation.lastRepetitionDate = pstDate
        affirmationService.affirmationToReturn = affirmation
        await viewModel.loadData(for: affirmation.id)
        // Then: todayRepetitions should not be incorrectly reset or incremented
        XCTAssertEqual(viewModel.todayRepetitions, 1)
    }
}

// MARK: - Test Helpers & Mocks

/// Mock affirmation for testing
struct AffirmationStub: AffirmationProtocol {
    var id: UUID = UUID()
    var text: String = ""
    var category: AffirmationCategory = .confidence
    var recordingURL: URL? = nil
    var createdAt: Date = Date()
    var updatedAt: Date = Date()
    var currentCycleDay: Int = 1
    var cycleStartDate: Date? = Date()
    var completedCycles: Int = 0
    var currentRepetitions: Int = 0
    var dailyProgress: [Date: Int] = [:]
    var lastRepetitionDate: Date? = nil
    var energyLevel: Double = 1.0
    var moodRating: Int? = nil
    var notes: String? = nil
    var isFavorite: Bool = false
    var playCount: Int = 0
    var hasActiveCycle: Bool = true
    var dailyGoal: Int = 3
    var todayRepetitions: Int = 0
    var cycleProgress: Double = 0.0
    var canPerformRepetition: Bool { todayRepetitions < dailyGoal }
    var isCurrentCycleComplete: Bool { todayRepetitions >= dailyGoal }
    var todayProgress: Double { Double(todayRepetitions) / Double(dailyGoal) }
    mutating func recordRepetition() throws { todayRepetitions += 1 }
    mutating func updateEnergyLevel(_ level: Double) { energyLevel = level }
    mutating func recordMood(_ rating: Int, notes: String?) { moodRating = rating; notes.map { self.notes = $0 } }
}

/// Mock repetition service
class MockRepetitionService: RepetitionServiceProtocol {
    var didRecordRepetition = false
    var errorToThrow: Error?

    func recordRepetition(for affirmation: AffirmationProtocol) async throws -> RepetitionResult {
        if let error = errorToThrow {
            throw error
        }
        didRecordRepetition = true
        var updated = affirmation as? AffirmationStub ?? AffirmationStub()
        updated.todayRepetitions += 1
        return RepetitionResult(affirmation: updated, isMilestoneReached: updated.todayRepetitions >= updated.dailyGoal)
    }

    func startCycle(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }

    func startSession(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }

    func getProgress(for affirmation: AffirmationProtocol) -> ProgressInfo {
        return ProgressInfo(
            todayProgress: 0,
            cycleProgress: 0,
            currentDay: 1,
            totalDays: 7,
            currentRepetitions: 0,
            totalRepetitions: 0,
            hasTodayQuotaMet: false,
            isCycleComplete: false,
            hasActiveCycle: false
        )
    }

    func getStreakInfo(for affirmation: AffirmationProtocol) -> StreakInfo {
        return StreakInfo(
            currentStreak: 0,
            longestStreak: 0,
            completedCycles: 0,
            hasActiveCycle: false,
            cycleStartDate: nil,
            lastRepetitionDate: nil
        )
    }

    func canPerformRepetition(for affirmation: AffirmationProtocol) -> Bool { true }

    func timeUntilNextRepetition(for affirmation: AffirmationProtocol) -> TimeInterval? { nil }

    func restartBrokenCycle(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }

    func isCycleBroken(for affirmation: AffirmationProtocol) -> Bool { false }
}

/// Mock streak service
class MockStreakService: StreakServiceProtocol {
    var didUpdateStreak = false
    var errorToThrow: Error?
    func updateStreak(for affirmation: AffirmationProtocol) async throws {
        if let error = errorToThrow { throw error }
        didUpdateStreak = true
    }
}

/// Mock audio recording service
class MockAudioRecordingService: AudioRecordingServiceProtocol {
    var didStartPlayback = false
    var didPausePlayback = false
    var errorToThrow: Error?

    // Required properties
    var isRecording: Bool = false
    var recordingTime: TimeInterval = 0
    var recordingPower: Double = 0
    var recordingURL: URL? = nil
    var error: Error? = nil
    var isPlaying: Bool { didStartPlayback && !didPausePlayback }
    var playbackProgress: Double { isPlaying ? 0.5 : 0.0 }
    var playbackTime: TimeInterval { isPlaying ? 15.0 : 0.0 }

    // Publishers for SwiftUI bindings
    var isRecordingPublisher: Published<Bool>.Publisher { $isRecording }
    var recordingTimePublisher: Published<TimeInterval>.Publisher { $recordingTime }
    var recordingPowerPublisher: Published<Double>.Publisher { $recordingPower }
    var recordingURLPublisher: Published<URL?>.Publisher { $recordingURL }
    var isPlayingPublisher: Published<Bool>.Publisher { $isPlaying }
    var playbackProgressPublisher: Published<Double>.Publisher { $playbackProgress }
    var playbackTimePublisher: Published<TimeInterval>.Publisher { $playbackTime }
    var errorPublisher: Published<Error?>.Publisher { $error }

    // Published properties for SwiftUI/reactive use
    @Published var _isRecording: Bool = false
    @Published var _recordingTime: TimeInterval = 0
    @Published var _recordingPower: Double = 0
    @Published var _recordingURL: URL? = nil
    @Published var _isPlaying: Bool = false
    @Published var _playbackProgress: Double = 0
    @Published var _playbackTime: TimeInterval = 0
    @Published var _error: Error? = nil

    func startRecording() async throws {
        isRecording = true
    }

    func stopRecording() async throws -> URL {
        isRecording = false
        return URL(string: "file:///test.m4a")!
    }

    func deleteRecording() {
        // No-op for tests
    }

    func startPlayback() async throws {
        if let error = errorToThrow { throw error }
        didStartPlayback = true
    }

    func startPlayback(from url: URL) async throws {
        if let error = errorToThrow { throw error }
        didStartPlayback = true
    }

    func pausePlayback() {
        didPausePlayback = true
    }

    func stopPlayback() {}

    func deleteRecording(at url: URL) async throws {
        // No-op for tests
    }

    func getRecordingDuration(for url: URL) async throws -> TimeInterval {
        return 5.0
    }

    func getRecordingWaveform(for url: URL) async throws -> [Float] {
        return [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    }

    /// Diagnostic method to test microphone access and audio session setup
    func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double) {
        return (true, "Mock microphone test successful", -30.0)
    }
}

// MARK: - Documentation
// Test helpers and mocks are provided for isolation and repeatability. Each test creates fresh instances to ensure no state leakage between tests. The repetition system is tested end-to-end, including UI synchronization, milestone logic, cooldowns, and error handling.
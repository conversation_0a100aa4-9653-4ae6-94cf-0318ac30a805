import XCTest
import Combine
import <PERSON><PERSON>
@testable import NeuroLoopCore
@testable import NeuroLoopUI
import NeuroLoopInterfaces
import NeuroLoopTestUtilities

class RepetitionTrackingIntegrationTests: XCTestCase {
    var app: XCUIApplication!
    var viewModel: RepetitionTrackingViewModel!
    var affirmationService: MockAffirmationService!
    var repetitionService: MockRepetitionService!
    var streakService: MockStreakService!
    var audioService: MockAudioRecordingService!

    override func setUp() {
        super.setUp()
        app = XCUIApplication()
        app.launchArguments = ["--uitesting"]
        app.launch()

        affirmationService = MockAffirmationService()
        repetitionService = MockRepetitionService()
        streakService = MockStreakService()
        audioService = MockAudioRecordingService()

        viewModel = RepetitionTrackingViewModel(
            affirmationService: affirmationService,
            repetitionService: repetitionService,
            streakService: streakService,
            audioService: audioService
        )
    }

    override func tearDown() {
        viewModel = nil
        affirmationService = nil
        repetitionService = nil
        streakService = nil
        audioService = nil
        super.tearDown()
    }

    func testCompleteRepetitionFlow() async {
        // Given
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Test affirmation",
            category: .confidence,
            todayRepetitions: 0,
            dailyGoal: 5,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation

        // When
        await viewModel.loadData(for: affirmation.id)

        // Then
        XCTAssertEqual(viewModel.todayRepetitions, 0)
        XCTAssertEqual(viewModel.dailyGoal, 5)
        XCTAssertEqual(viewModel.currentDay, 1)
        XCTAssertEqual(viewModel.cycleProgress, 0.0)

        // When - Perform repetition
        await viewModel.performRepetition()

        // Then
        XCTAssertEqual(viewModel.todayRepetitions, 1)
        XCTAssertEqual(viewModel.todayProgress, 0.2) // 1/5
        XCTAssertFalse(viewModel.isMilestoneReached)

        // When - Complete daily goal
        for _ in 1...4 {
            await viewModel.performRepetition()
        }

        // Then
        XCTAssertEqual(viewModel.todayRepetitions, 5)
        XCTAssertEqual(viewModel.todayProgress, 1.0)
        XCTAssertTrue(viewModel.isMilestoneReached)
    }

    func testAudioPlaybackIntegration() async {
        // Given
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Test affirmation",
            category: .confidence,
            recordingURL: URL(string: "test://recording")
        )
        affirmationService.affirmationToReturn = affirmation

        // When
        await viewModel.loadData(for: affirmation.id)

        // Then
        XCTAssertTrue(viewModel.hasRecording)
        XCTAssertFalse(viewModel.isPlaying)
        XCTAssertEqual(viewModel.playbackProgress, 0.0)

        // When - Start playback
        await viewModel.playRecording()

        // Then
        XCTAssertTrue(viewModel.isPlaying)
        XCTAssertTrue(audioService.didStartPlayback)

        // When - Pause playback
        viewModel.pausePlayback()

        // Then
        XCTAssertFalse(viewModel.isPlaying)
        XCTAssertTrue(audioService.didPausePlayback)
    }

    func testErrorHandling() async {
        // Given
        let error = NSError(domain: "Test", code: 1, userInfo: nil)
        affirmationService.errorToThrow = error

        // When
        await viewModel.loadData(for: UUID())

        // Then
        XCTAssertNotNil(viewModel.error)
        XCTAssertEqual(viewModel.error?.localizedDescription, error.localizedDescription)

        // When - Retry
        affirmationService.errorToThrow = nil
        viewModel.retry()

        // Then
        XCTAssertNil(viewModel.error)
    }

    func testProgressSynchronization() async {
        // Given
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Test affirmation",
            category: .confidence,
            todayRepetitions: 0,
            dailyGoal: 5,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation

        // When
        await viewModel.loadData(for: affirmation.id)

        // Then
        XCTAssertEqual(viewModel.todayRepetitions, 0)
        XCTAssertEqual(viewModel.cycleProgress, 0.0)

        // When - Update affirmation progress
        let updatedAffirmation = AffirmationStub(
            id: affirmation.id,
            text: affirmation.text,
            category: affirmation.category,
            todayRepetitions: 3,
            dailyGoal: 5,
            currentDay: 2,
            cycleProgress: 0.5
        )
        affirmationService.affirmationToReturn = updatedAffirmation

        // When - Reload data
        await viewModel.loadData(for: affirmation.id)

        // Then
        XCTAssertEqual(viewModel.todayRepetitions, 3)
        XCTAssertEqual(viewModel.todayProgress, 0.6) // 3/5
        XCTAssertEqual(viewModel.currentDay, 2)
        XCTAssertEqual(viewModel.cycleProgress, 0.5)
    }

    func testMilestoneCelebration() async {
        // Given
        let affirmation = AffirmationStub(
            id: UUID(),
            text: "Test affirmation",
            category: .confidence,
            todayRepetitions: 4,
            dailyGoal: 5,
            currentDay: 1,
            cycleProgress: 0.0
        )
        affirmationService.affirmationToReturn = affirmation

        // When
        await viewModel.loadData(for: affirmation.id)

        // Then
        XCTAssertFalse(viewModel.isMilestoneReached)

        // When - Complete daily goal
        await viewModel.performRepetition()

        // Then
        XCTAssertTrue(viewModel.isMilestoneReached)

        // Wait for milestone celebration to complete
        try? await Task.sleep(nanoseconds: 3_000_000_000)

        // Then
        XCTAssertFalse(viewModel.isMilestoneReached)
    }
}

// MARK: - Mock Services

class MockAffirmationService: AffirmationServiceProtocol {
    var affirmationToReturn: AffirmationProtocol?
    var errorToThrow: Error?

    func getAffirmation(id: UUID) async throws -> AffirmationProtocol {
        if let error = errorToThrow {
            throw error
        }
        guard let affirmation = affirmationToReturn else {
            throw NSError(domain: "Test", code: 1, userInfo: nil)
        }
        return affirmation
    }

    // Implement other required methods...
}

class MockRepetitionService: RepetitionServiceProtocol {
    var didRecordRepetition = false
    var errorToThrow: Error?

    func recordRepetition(for affirmation: AffirmationProtocol) async throws -> RepetitionResult {
        if let error = errorToThrow {
            throw error
        }
        didRecordRepetition = true
        return RepetitionResult(
            affirmation: affirmation,
            isMilestoneReached: affirmation.todayRepetitions + 1 >= affirmation.dailyGoal
        )
    }

    func startCycle(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }

    func startSession(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }

    func getProgress(for affirmation: AffirmationProtocol) -> ProgressInfo {
        return ProgressInfo(
            todayProgress: 0,
            cycleProgress: 0,
            currentDay: 1,
            totalDays: 7,
            currentRepetitions: 0,
            totalRepetitions: 0,
            hasTodayQuotaMet: false,
            isCycleComplete: false,
            hasActiveCycle: false
        )
    }

    func getStreakInfo(for affirmation: AffirmationProtocol) -> StreakInfo {
        return StreakInfo(
            currentStreak: 0,
            longestStreak: 0,
            completedCycles: 0,
            hasActiveCycle: false,
            cycleStartDate: nil,
            lastRepetitionDate: nil
        )
    }

    func canPerformRepetition(for affirmation: AffirmationProtocol) -> Bool { true }

    func timeUntilNextRepetition(for affirmation: AffirmationProtocol) -> TimeInterval? { nil }

    func restartBrokenCycle(for affirmation: AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }

    func isCycleBroken(for affirmation: AffirmationProtocol) -> Bool { false }
}

class MockStreakService: StreakServiceProtocol {
    var didUpdateStreak = false
    var errorToThrow: Error?

    func updateStreak(for affirmation: AffirmationProtocol) async throws {
        if let error = errorToThrow {
            throw error
        }
        didUpdateStreak = true
    }

    // Implement other required methods...
}

class MockAudioRecordingService: AudioRecordingServiceProtocol {
    var didStartPlayback = false
    var didPausePlayback = false
    var errorToThrow: Error?

    // Required properties
    var isRecording: Bool = false
    var isPlaying: Bool = false
    var recordingTime: TimeInterval = 0
    var recordingPower: Double = 0
    var recordingURL: URL? = nil
    var playbackProgress: Double = 0
    var playbackTime: TimeInterval = 0
    var error: Error? = nil

    // Publishers for SwiftUI bindings
    var isRecordingPublisher: Published<Bool>.Publisher { $isRecording }
    var recordingTimePublisher: Published<TimeInterval>.Publisher { $recordingTime }
    var recordingPowerPublisher: Published<Double>.Publisher { $recordingPower }
    var recordingURLPublisher: Published<URL?>.Publisher { $recordingURL }
    var isPlayingPublisher: Published<Bool>.Publisher { $isPlaying }
    var playbackProgressPublisher: Published<Double>.Publisher { $playbackProgress }
    var playbackTimePublisher: Published<TimeInterval>.Publisher { $playbackTime }
    var errorPublisher: Published<Error?>.Publisher { $error }

    // Published properties for SwiftUI/reactive use
    @Published var _isRecording: Bool = false
    @Published var _recordingTime: TimeInterval = 0
    @Published var _recordingPower: Double = 0
    @Published var _recordingURL: URL? = nil
    @Published var _isPlaying: Bool = false
    @Published var _playbackProgress: Double = 0
    @Published var _playbackTime: TimeInterval = 0
    @Published var _error: Error? = nil

    func startRecording() async throws {
        isRecording = true
    }

    func stopRecording() async throws -> URL {
        isRecording = false
        return URL(string: "file:///test.m4a")!
    }

    func deleteRecording() {
        // No-op for tests
    }

    func startPlayback() async throws {
        if let error = errorToThrow {
            throw error
        }
        didStartPlayback = true
        isPlaying = true
    }

    func pausePlayback() {
        didPausePlayback = true
        isPlaying = false
    }

    func stopPlayback() {
        isPlaying = false
    }

    func deleteRecording(at url: URL) async throws {
        // No-op for tests
    }

    func getRecordingDuration(for url: URL) async throws -> TimeInterval {
        return 5.0
    }

    func getRecordingWaveform(for url: URL) async throws -> [Float] {
        return [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    }

    /// Diagnostic method to test microphone access and audio session setup
    func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double) {
        return (true, "Mock microphone test successful", -30.0)
    }
}
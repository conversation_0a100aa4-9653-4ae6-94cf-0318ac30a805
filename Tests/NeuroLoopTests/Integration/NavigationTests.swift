import XCTest
import SwiftUI
import NeuroLoopCore
import NeuroLoopInterfaces
import Neuro<PERSON><PERSON>Shared
@testable import Neur<PERSON><PERSON><PERSON><PERSON>

/// UI tests for navigation flows across the app
class NavigationTests: XCTestCase {
    var app: XCUIApplication!

    override func setUp() {
        super.setUp()
        continueAfterFailure = false
        app = XCUIApplication()
        app.launchArguments = ["--uitesting", "--navigation-testing"]
        app.launch()
    }

    override func tearDown() {
        app = nil
        super.tearDown()
    }

    // MARK: - Test: Tab bar navigation between Home, Library, Add, and Settings
    func testTabBarNavigation() {
        tabBarTap("Home")
        XCTAssertTrue(app.otherElements["HomeView"].exists)
        tabBarTap("Library")
        XCTAssertTrue(app.otherElements["LibraryView"].exists)
        tabBarTap("Add")
        XCTAssertTrue(app.otherElements["AddAffirmationView"].exists)
        tabBarTap("Settings")
        XCTAssertTrue(app.otherElements["SettingsView"].exists)
    }

    // MARK: - Test: Navigation from Home to Affirmation Detail
    func testHomeToAffirmationDetail() {
        tabBarTap("Home")
        let firstCell = app.tables.cells.element(boundBy: 0)
        XCTAssertTrue(firstCell.exists)
        firstCell.tap()
        XCTAssertTrue(app.otherElements["AffirmationDetailView"].waitForExistence(timeout: 2))
    }

    // MARK: - Test: Navigation from Library to Affirmation Detail
    func testLibraryToAffirmationDetail() {
        tabBarTap("Library")
        let firstCell = app.tables.cells.element(boundBy: 0)
        XCTAssertTrue(firstCell.exists)
        firstCell.tap()
        XCTAssertTrue(app.otherElements["AffirmationDetailView"].waitForExistence(timeout: 2))
    }

    // MARK: - Test: Navigation to and from Add Affirmation screen
    func testAddAffirmationNavigation() {
        tabBarTap("Add")
        XCTAssertTrue(app.otherElements["AddAffirmationView"].exists)
        app.buttons["Cancel"].tap()
        XCTAssertTrue(app.otherElements["HomeView"].exists)
    }

    // MARK: - Test: Deep linking to specific screens
    func testDeepLinkingToAffirmationDetail() {
        app.launchArguments += ["--deeplink", "affirmation-detail?id=123"]
        app.launch()
        XCTAssertTrue(app.otherElements["AffirmationDetailView"].waitForExistence(timeout: 2))
    }

    // MARK: - Test: Back navigation functionality
    func testBackNavigation() {
        tabBarTap("Library")
        let firstCell = app.tables.cells.element(boundBy: 0)
        firstCell.tap()
        let backButton = app.navigationBars.buttons.element(boundBy: 0)
        XCTAssertTrue(backButton.exists)
        backButton.tap()
        XCTAssertTrue(app.otherElements["LibraryView"].exists)
    }

    // MARK: - Test: Modal presentation and dismissal
    func testModalPresentationAndDismissal() {
        tabBarTap("Add")
        XCTAssertTrue(app.otherElements["AddAffirmationView"].exists)
        app.buttons["Cancel"].tap()
        XCTAssertTrue(app.otherElements["HomeView"].exists)
    }

    // MARK: - Test: Navigation to premium subscription screens
    func testNavigationToPremiumSubscription() {
        tabBarTap("Settings")
        app.buttons["GoPremium"].tap()
        XCTAssertTrue(app.otherElements["PremiumSubscriptionView"].waitForExistence(timeout: 2))
    }

    // MARK: - Test: Navigation through onboarding flows
    func testOnboardingFlowNavigation() {
        app.launchArguments += ["--reset-onboarding"]
        app.launch()
        XCTAssertTrue(app.otherElements["OnboardingView"].waitForExistence(timeout: 2))
        app.buttons["Next"].tap()
        XCTAssertTrue(app.otherElements["OnboardingStep2View"].waitForExistence(timeout: 2))
        app.buttons["Finish"].tap()
        XCTAssertTrue(app.otherElements["HomeView"].waitForExistence(timeout: 2))
    }

    // MARK: - Test: Error state navigation handling
    func testErrorStateNavigationHandling() {
        app.launchArguments += ["--simulate-error"]
        app.launch()
        XCTAssertTrue(app.alerts["Error"].waitForExistence(timeout: 2))
        app.alerts["Error"].buttons["OK"].tap()
        XCTAssertTrue(app.otherElements["HomeView"].exists)
    }

    // MARK: - Accessibility navigation (if implemented)
    func testAccessibilityNavigation() {
        tabBarTap("Library")
        let firstCell = app.tables.cells.element(boundBy: 0)
        XCTAssertTrue(firstCell.exists)
        firstCell.swipeRight() // Example accessibility gesture
        XCTAssertTrue(app.otherElements["AffirmationDetailView"].waitForExistence(timeout: 2))
    }

    // MARK: - Helper: Tab bar tap
    func tabBarTap(_ label: String) {
        let tab = app.tabBars.buttons[label]
        XCTAssertTrue(tab.exists, "Tab bar button \(label) does not exist")
        tab.tap()
    }
}

// MARK: - Documentation
// Navigation test helpers are provided for common navigation actions (e.g., tabBarTap). All navigation paths, including deep links, modals, onboarding, and error states, are covered. Each test is isolated and verifies correct view presentation and transitions. 
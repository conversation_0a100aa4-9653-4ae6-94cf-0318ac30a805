import XCTest
import Combine
import StoreKit
@testable import NeuroLoopCore

class SubscriptionViewModelTests: XCTestCase {
    var sut: SubscriptionViewModel!
    var mockPremiumService: MockPremiumService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        mockPremiumService = MockPremiumService()
        sut = SubscriptionViewModel(premiumService: mockPremiumService)
        cancellables = []
    }
    
    override func tearDown() {
        sut = nil
        mockPremiumService = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - Product Loading Tests
    
    func testLoadProducts_Success() async throws {
        // Given
        let expectation = XCTestExpectation(description: "Products loaded")
        
        // When
        try await sut.loadProducts()
        
        // Then
        XCTAssertFalse(sut.products.isEmpty)
        XCTAssertFalse(sut.isLoading)
        XCTAssertNil(sut.error)
        expectation.fulfill()
    }
    
    func testLoadProducts_Failure() async throws {
        // Given
        mockPremiumService.shouldFail = true
        let expectation = XCTestExpectation(description: "Products failed to load")
        
        // When
        do {
            try await sut.loadProducts()
            XCTFail("Expected error to be thrown")
        } catch {
            // Then
            XCTAssertTrue(sut.products.isEmpty)
            XCTAssertFalse(sut.isLoading)
            XCTAssertNotNil(sut.error)
            expectation.fulfill()
        }
    }
    
    // MARK: - Product Selection Tests
    
    func testSelectProduct() {
        // Given
        let product = MockProduct()
        sut.products = [product]
        
        // When
        sut.selectProduct(product)
        
        // Then
        XCTAssertEqual(sut.selectedProductId, product.productIdentifier)
    }
    
    // MARK: - Purchase Tests
    
    func testPurchaseSubscription_Success() async throws {
        // Given
        let product = MockProduct()
        sut.products = [product]
        sut.selectedProductId = product.productIdentifier
        let expectation = XCTestExpectation(description: "Purchase completed")
        
        // When
        try await sut.purchaseSubscription()
        
        // Then
        XCTAssertFalse(sut.isPurchasing)
        XCTAssertNil(sut.error)
        XCTAssertTrue(mockPremiumService.isPremium)
        expectation.fulfill()
    }
    
    func testPurchaseSubscription_Failure() async throws {
        // Given
        let product = MockProduct(shouldFail: true)
        sut.products = [product]
        sut.selectedProductId = product.productIdentifier
        let expectation = XCTestExpectation(description: "Purchase failed")
        
        // When
        do {
            try await sut.purchaseSubscription()
            XCTFail("Expected error to be thrown")
        } catch {
            // Then
            XCTAssertFalse(sut.isPurchasing)
            XCTAssertNotNil(sut.error)
            XCTAssertFalse(mockPremiumService.isPremium)
            expectation.fulfill()
        }
    }
    
    // MARK: - Restore Purchases Tests
    
    func testRestorePurchases_Success() async throws {
        // Given
        let expectation = XCTestExpectation(description: "Purchases restored")
        
        // When
        try await sut.restorePurchases()
        
        // Then
        XCTAssertFalse(sut.isPurchasing)
        XCTAssertNil(sut.error)
        XCTAssertTrue(mockPremiumService.isPremium)
        expectation.fulfill()
    }
    
    func testRestorePurchases_Failure() async throws {
        // Given
        mockPremiumService.shouldFail = true
        let expectation = XCTestExpectation(description: "Restore failed")
        
        // When
        do {
            try await sut.restorePurchases()
            XCTFail("Expected error to be thrown")
        } catch {
            // Then
            XCTAssertFalse(sut.isPurchasing)
            XCTAssertNotNil(sut.error)
            XCTAssertFalse(mockPremiumService.isPremium)
            expectation.fulfill()
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testDismissError() {
        // Given
        sut.error = PremiumError.purchaseFailed
        
        // When
        sut.dismissError()
        
        // Then
        XCTAssertNil(sut.error)
    }
    
    // MARK: - State Management Tests
    
    func testState_Initial() {
        XCTAssertTrue(sut.products.isEmpty)
        XCTAssertNil(sut.selectedProductId)
        XCTAssertFalse(sut.isLoading)
        XCTAssertFalse(sut.isPurchasing)
        XCTAssertNil(sut.error)
    }
    
    func testState_LoadingProducts() {
        // Given
        sut.isLoading = true
        
        // Then
        XCTAssertTrue(sut.isLoading)
        XCTAssertFalse(sut.isPurchasing)
    }
    
    func testState_Purchasing() {
        // Given
        sut.isPurchasing = true
        
        // Then
        XCTAssertFalse(sut.isLoading)
        XCTAssertTrue(sut.isPurchasing)
    }
}

// MARK: - Mock Objects

extension MockPremiumService {
    var shouldFail: Bool = false
    
    override func purchaseSubscription(_ product: SKProduct) async throws {
        if shouldFail {
            throw PremiumError.purchaseFailed
        }
        isPremium = true
    }
    
    override func restorePurchases() async throws {
        if shouldFail {
            throw PremiumError.restoreFailed
        }
        isPremium = true
    }
} 
import XCTest
@testable import NeuroLoopCore

class PremiumErrorTests: XCTestCase {
    
    // MARK: - Error Enum Tests
    
    func testPremiumError_AllCases() {
        // Given
        let expectedCases: [PremiumError] = [
            .purchaseFailed,
            .restoreFailed,
            .verificationFailed,
            .productNotFound,
            .subscriptionExpired
        ]
        
        // When
        let allCases = PremiumError.allCases
        
        // Then
        XCTAssertEqual(allCases.count, expectedCases.count)
        XCTAssertEqual(Set(allCases), Set(expectedCases))
    }
    
    // MARK: - Error Properties Tests
    
    func testErrorProperties_PurchaseFailed() {
        // Given
        let error = PremiumError.purchaseFailed
        
        // Then
        XCTAssertEqual(error.localizedDescription, "Purchase failed. Please try again.")
        XCTAssertEqual(error.errorDescription, "Purchase failed. Please try again.")
        XCTAssertEqual(error.failureReason, "The purchase transaction could not be completed.")
        XCTAssertEqual(error.recoverySuggestion, "Check your internet connection and try again.")
    }
    
    func testErrorProperties_RestoreFailed() {
        // Given
        let error = PremiumError.restoreFailed
        
        // Then
        XCTAssertEqual(error.localizedDescription, "Restore failed. Please try again.")
        XCTAssertEqual(error.errorDescription, "Restore failed. Please try again.")
        XCTAssertEqual(error.failureReason, "Could not restore previous purchases.")
        XCTAssertEqual(error.recoverySuggestion, "Check your internet connection and try again.")
    }
    
    func testErrorProperties_VerificationFailed() {
        // Given
        let error = PremiumError.verificationFailed
        
        // Then
        XCTAssertEqual(error.localizedDescription, "Verification failed. Please try again.")
        XCTAssertEqual(error.errorDescription, "Verification failed. Please try again.")
        XCTAssertEqual(error.failureReason, "Could not verify the purchase with the App Store.")
        XCTAssertEqual(error.recoverySuggestion, "Check your internet connection and try again.")
    }
    
    func testErrorProperties_ProductNotFound() {
        // Given
        let error = PremiumError.productNotFound
        
        // Then
        XCTAssertEqual(error.localizedDescription, "Product not found.")
        XCTAssertEqual(error.errorDescription, "Product not found.")
        XCTAssertEqual(error.failureReason, "The requested product could not be found in the App Store.")
        XCTAssertEqual(error.recoverySuggestion, "Please try again later.")
    }
    
    func testErrorProperties_SubscriptionExpired() {
        // Given
        let error = PremiumError.subscriptionExpired
        
        // Then
        XCTAssertEqual(error.localizedDescription, "Your subscription has expired.")
        XCTAssertEqual(error.errorDescription, "Your subscription has expired.")
        XCTAssertEqual(error.failureReason, "The subscription period has ended.")
        XCTAssertEqual(error.recoverySuggestion, "Please renew your subscription to continue using premium features.")
    }
    
    // MARK: - Error Comparison Tests
    
    func testErrorComparison() {
        // Given
        let error1 = PremiumError.purchaseFailed
        let error2 = PremiumError.restoreFailed
        
        // Then
        XCTAssertNotEqual(error1, error2)
        XCTAssertEqual(error1, error1)
    }
    
    // MARK: - Error Hashable Tests
    
    func testErrorHashable() {
        // Given
        let error = PremiumError.purchaseFailed
        let set: Set<PremiumError> = [error]
        
        // Then
        XCTAssertTrue(set.contains(error))
    }
    
    // MARK: - Error Codable Tests
    
    func testErrorCodable() throws {
        // Given
        let error = PremiumError.purchaseFailed
        let encoder = JSONEncoder()
        let decoder = JSONDecoder()
        
        // When
        let data = try encoder.encode(error)
        let decodedError = try decoder.decode(PremiumError.self, from: data)
        
        // Then
        XCTAssertEqual(error, decodedError)
    }
    
    // MARK: - Error Localization Tests
    
    func testErrorLocalization() {
        // Given
        let error = PremiumError.purchaseFailed
        
        // Then
        XCTAssertFalse(error.localizedDescription.isEmpty)
        XCTAssertFalse(error.errorDescription.isEmpty)
        XCTAssertFalse(error.failureReason.isEmpty)
        XCTAssertFalse(error.recoverySuggestion.isEmpty)
    }
    
    // MARK: - Error Accessibility Tests
    
    func testErrorAccessibility() {
        // Given
        let error = PremiumError.purchaseFailed
        
        // Then
        XCTAssertFalse(error.localizedDescription.isEmpty)
        XCTAssertFalse(error.errorDescription.isEmpty)
        XCTAssertFalse(error.failureReason.isEmpty)
        XCTAssertFalse(error.recoverySuggestion.isEmpty)
    }
} 
import XCTest
import SwiftUI
@testable import NeuroLoopCore

class FeatureGatingTests: XCTestCase {
    var sut: PremiumFeatureUnlocker!
    var mockPremiumService: MockPremiumService!
    
    override func setUp() {
        super.setUp()
        mockPremiumService = MockPremiumService()
        sut = PremiumFeatureUnlocker(premiumService: mockPremiumService)
    }
    
    override func tearDown() {
        sut = nil
        mockPremiumService = nil
        super.tearDown()
    }
    
    // MARK: - Feature Availability Tests
    
    func testIsFeatureAvailable_WhenNotPremium() {
        // Given
        mockPremiumService.isPremium = false
        
        // When
        let isAvailable = sut.isFeatureAvailable(.unlimitedAffirmations)
        
        // Then
        XCTAssertFalse(isAvailable)
    }
    
    func testIsFeatureAvailable_WhenPremium() {
        // Given
        mockPremiumService.isPremium = true
        
        // When
        let isAvailable = sut.isFeatureAvailable(.unlimitedAffirmations)
        
        // Then
        XCTAssertTrue(isAvailable)
    }
    
    // MARK: - Premium Feature View Tests
    
    func testPremiumFeatureView_WhenNotPremium() {
        // Given
        mockPremiumService.isPremium = false
        
        // When
        let view = sut.premiumFeatureView(
            feature: .unlimitedAffirmations,
            content: { Text("Premium Content") }
        )
        
        // Then
        XCTAssertNotNil(view)
        // Note: We can't directly test the view's content in XCTest
        // This would require UI testing
    }
    
    func testPremiumFeatureView_WhenPremium() {
        // Given
        mockPremiumService.isPremium = true
        
        // When
        let view = sut.premiumFeatureView(
            feature: .unlimitedAffirmations,
            content: { Text("Premium Content") }
        )
        
        // Then
        XCTAssertNotNil(view)
        // Note: We can't directly test the view's content in XCTest
        // This would require UI testing
    }
    
    // MARK: - Premium Feature Button Tests
    
    func testPremiumFeatureButton_WhenNotPremium() {
        // Given
        mockPremiumService.isPremium = false
        
        // When
        let button = sut.premiumFeatureButton(
            feature: .unlimitedAffirmations,
            action: {},
            label: { Text("Premium Button") }
        )
        
        // Then
        XCTAssertNotNil(button)
        // Note: We can't directly test the button's content in XCTest
        // This would require UI testing
    }
    
    func testPremiumFeatureButton_WhenPremium() {
        // Given
        mockPremiumService.isPremium = true
        
        // When
        let button = sut.premiumFeatureButton(
            feature: .unlimitedAffirmations,
            action: {},
            label: { Text("Premium Button") }
        )
        
        // Then
        XCTAssertNotNil(button)
        // Note: We can't directly test the button's content in XCTest
        // This would require UI testing
    }
    
    // MARK: - Feature Locked View Tests
    
    func testFeatureLockedView_DisplaysCorrectFeature() {
        // Given
        let feature = PremiumFeature.unlimitedAffirmations
        
        // When
        let view = sut.premiumFeatureLockedView(feature: feature)
        
        // Then
        XCTAssertNotNil(view)
        // Note: We can't directly test the view's content in XCTest
        // This would require UI testing
    }
}

// MARK: - Mock Objects

class MockPremiumService: PremiumServiceProtocol {
    var isPremium: Bool = false
    
    func isFeatureAvailable(_ feature: PremiumFeature) -> Bool {
        return isPremium
    }
    
    func purchaseSubscription(_ product: SKProduct) async throws {
        isPremium = true
    }
    
    func restorePurchases() async throws {
        isPremium = true
    }
    
    func verifySubscription() async throws -> Bool {
        return isPremium
    }
} 
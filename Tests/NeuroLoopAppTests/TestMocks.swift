import Foundation
import XCTest
@testable import <PERSON>eur<PERSON><PERSON><PERSON><PERSON>ore
@testable import Neuro<PERSON>oopUI
import Combine
import NeuroLoopInterfaces
import NeuroLoopTestUtilities // Import shared mocks from here

// Shared test mocks for NeuroLoopAppTests

// Mocks have been migrated to NeuroLoopTestUtilities. Use imports from that module for shared mocks.

public final class MockAffirmationService {
    private(set) var affirmations: [UUID: AffirmationProtocol] = [:]
    public init() {}
    public func createMockAffirmation() -> AffirmationProtocol {
        let affirmation = AffirmationStub(
            text: "Test affirmation",
            category: .confidence
        )
        affirmations[affirmation.id] = affirmation
        return affirmation
    }
    public func hasAffirmation(_ id: UUID) -> Bool { affirmations[id] != nil }
}

public final class MockRepetitionService: RepetitionServiceProtocol {
    private var data: [UUID: [Date]] = [:]
    private var milestones: [UUID: Set<Int>] = [:]
    public init() {}
    public func addRepetition(for id: UUID, date: Date) throws -> Bool {
        guard date > Date.distantPast else { throw NSError(domain: "Invalid date", code: 1) }
        data[id, default: []].append(date)
        let count = data[id]!.count
        if [25, 50, 75, 100].contains(count) {
            milestones[id, default: []].insert(count)
        }
        return true
    }
    public func repetitions(for id: UUID) -> [Date] { data[id] ?? [] }
    public func dailyCount(for id: UUID, date: Date) -> Int {
        let cal = Calendar.current
        return (data[id] ?? []).filter { cal.isDate($0, inSameDayAs: date) }.count
    }
    public func hasCompletedTarget(for id: UUID) -> Bool { (data[id]?.count ?? 0) >= 100 }
    public func isMilestone(for id: UUID, count: Int) -> Bool { milestones[id]?.contains(count) ?? false }
    public func hasMilestone(for id: UUID, count: Int) -> Bool { milestones[id]?.contains(count) ?? false }
    public func resetMilestones(for id: UUID) { milestones[id] = [] }
    public func simulateCrashAndRecovery() { /* No-op for mock */ }
    // Protocol stubs
    public func recordRepetition(for affirmation: any AffirmationProtocol) async throws -> RepetitionResult {
        return RepetitionResult(success: true, updatedAffirmation: affirmation, isQuotaMet: false, isCycleComplete: false)
    }
    public func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }
    public func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }
    public func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
        return ProgressInfo(
            todayProgress: 0,
            cycleProgress: 0,
            currentDay: 1,
            totalDays: 7,
            currentRepetitions: 0,
            totalRepetitions: 0,
            hasTodayQuotaMet: false,
            isCycleComplete: false,
            hasActiveCycle: false
        )
    }
    public func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
        return StreakInfo(
            currentStreak: 0,
            longestStreak: 0,
            completedCycles: 0,
            hasActiveCycle: false,
            cycleStartDate: nil,
            lastRepetitionDate: nil
        )
    }
    public func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool { true }
    public func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval? { nil }
    public func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
        return CycleResult(success: true, updatedAffirmation: affirmation)
    }
    public func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool { false }
}

public final class MockStreakService: StreakServiceProtocol {
    private var streaks: [UUID: Int] = [:]
    private var maxStreaks: [UUID: Int] = [:]
    public init() {}
    public func startStreak(for id: UUID) -> Bool { streaks[id] = 1; return true }
    public func addRepetition(for id: UUID, date: Date) -> Bool {
        streaks[id, default: 0] += 1
        maxStreaks[id] = max(maxStreaks[id] ?? 0, streaks[id] ?? 0)
        return true
    }
    public func currentStreak(for id: UUID) -> Int { streaks[id] ?? 0 }
    public func maximumStreak(for id: UUID) -> Int { maxStreaks[id] ?? 0 }
    public func validateStreaks() async throws -> StreakValidationResult {
        return StreakValidationResult(success: true, error: nil, validatedAffirmations: 0, brokenStreaks: 0)
    }
    public func getStreakStatistics() async throws -> StreakStatistics {
        return StreakStatistics()
    }
    public func getStreakCalendarData(for affirmation: AffirmationProtocol, numberOfDays: Int) -> [StreakCalendarDay] {
        return []
    }
    public func isStreakAtRisk(for affirmation: AffirmationProtocol) -> Bool {
        return false
    }
    public func updateStreak(for affirmation: AffirmationProtocol) async throws {}
    public func initializeStreak(for id: UUID) { streaks[id] = 0 }
    public func calendarData(for id: UUID, month: Date) -> [StreakCalendarDay] { return Array(repeating: StreakCalendarDay(date: month, repetitions: 1, isComplete: true, progress: 1.0), count: 7) }
    public func timelineData(for id: UUID) -> [StreakCalendarDay] { return Array(repeating: StreakCalendarDay(date: Date(), repetitions: 1, isComplete: true, progress: 1.0), count: 10) }
    public func longestStreak(for id: UUID) -> Int { return maxStreaks[id] ?? 0 }
    public func totalActiveDays(for id: UUID) -> Int { return streaks[id] ?? 0 }
    public func simulateCrashAndRecovery() { /* No-op for mock */ }
}

public final class MockNotificationService {
    private var scheduled: Set<UUID> = []
    public init() {}
    public func scheduleReminder(for id: UUID) { scheduled.insert(id) }
    public func hasScheduled(for id: UUID) -> Bool { scheduled.contains(id) }
}

public final class MockAudioService {
    private var playing: Set<UUID> = []
    public init() {}
    public func play(for id: UUID) { playing.insert(id) }
    public func isPlaying(for id: UUID) -> Bool { playing.contains(id) }
}

public final class MockAudioRecordingService: AudioRecordingServiceProtocol {
    @Published public var isRecording: Bool = false
    @Published public var recordingTime: TimeInterval = 0
    @Published public var recordingPower: Double = 0
    @Published public var recordingURL: URL? = nil
    @Published public var isPlaying: Bool = false
    @Published public var playbackProgress: Double = 0
    @Published public var playbackTime: TimeInterval = 0
    @Published public var error: Error? = nil

    public var isRecordingPublisher: Published<Bool>.Publisher { $isRecording }
    public var recordingTimePublisher: Published<TimeInterval>.Publisher { $recordingTime }
    public var recordingPowerPublisher: Published<Double>.Publisher { $recordingPower }
    public var recordingURLPublisher: Published<URL?>.Publisher { $recordingURL }
    public var isPlayingPublisher: Published<Bool>.Publisher { $isPlaying }
    public var playbackProgressPublisher: Published<Double>.Publisher { $playbackProgress }
    public var playbackTimePublisher: Published<TimeInterval>.Publisher { $playbackTime }
    public var errorPublisher: Published<Error?>.Publisher { $error }

    public init() {}
    public func startRecording() async throws { isRecording = true }
    public func stopRecording() async throws -> URL { isRecording = false; return URL(fileURLWithPath: "/dev/null") }
    public func deleteRecording() { recordingURL = nil }
    public func startPlayback() async throws { isPlaying = true }
    public func pausePlayback() { isPlaying = false }
    public func stopPlayback() { isPlaying = false }
    public func deleteRecording(at url: URL) async throws {}
    public func getRecordingDuration(for url: URL) async throws -> TimeInterval { return 5.0 }
    public func getRecordingWaveform(for url: URL) async throws -> [Float] { return [0.1, 0.2, 0.3, 0.4, 0.5] }

    /// Diagnostic method to test microphone access and audio session setup
    public func testMicrophoneAccess() async -> (success: Bool, message: String, audioLevel: Double) {
        return (true, "Mock microphone test successful", -30.0)
    }
}

public class MockAffirmationRepository: AffirmationRepositoryProtocol {
    public init() {}
    public func fetchAffirmations() async throws -> [AffirmationProtocol] { [] }
    public func fetchAffirmation(id: UUID) async throws -> AffirmationProtocol? { nil }
    public func createAffirmation(text: String, category: AffirmationCategory, recordingURL: URL?) async throws -> AffirmationProtocol { MockAffirmation() }
    public func updateAffirmation(_ affirmation: AffirmationProtocol) async throws {}
    public func deleteAffirmation(id: UUID) async throws {}
    public func fetchAffirmations(category: AffirmationCategory) async throws -> [AffirmationProtocol] { [] }
    public func fetchFavoriteAffirmations() async throws -> [AffirmationProtocol] { [] }
    public func fetchCurrentAffirmation() async throws -> AffirmationProtocol? { nil }
    public func recordRepetition(for affirmation: AffirmationProtocol) async throws {}
    public func startCycle(for affirmation: AffirmationProtocol) async throws {}
}

public final class MockAffirmation: AffirmationProtocol {
    public init() {}
    public var id = UUID()
    public var text = "I am confident and capable of achieving my goals"
    public var category: AffirmationCategory = .confidence
    public var recordingURL: URL? = nil
    public var createdAt = Date()
    public var updatedAt = Date()
    public var currentCycleDay = 3
    public var cycleStartDate: Date? = Date()
    public var completedCycles = 2
    public var currentRepetitions = 42
    public var dailyProgress: [Date: Int] = [:]
    public var lastRepetitionDate: Date? = Date()
    public var energyLevel = 0.7
    public var moodRating: Int? = 4
    public var notes: String? = nil
    public var isFavorite = true
    public var playCount = 5
    public var hasActiveCycle = true
    public var isCurrentCycleComplete = false
    public var todayProgress = 0.42
    public var cycleProgress = 0.38
    public var hasTodayQuotaMet = false
    public var canPerformRepetition = true
    public func recordRepetition() throws {}
    public func updateEnergyLevel(_ level: Double) {}
    public func recordMood(_ rating: Int, notes: String?) {}
}
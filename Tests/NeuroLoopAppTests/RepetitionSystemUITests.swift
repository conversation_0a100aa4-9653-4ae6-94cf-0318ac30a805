import XCTest
@testable import NeuroLoopCore
@testable import NeuroLoopUI
import NeuroLoopTestUtilities

/// Integration tests for the repetition tracking system, including streaks, milestones, and data persistence.
final class RepetitionSystemTests: XCTestCase {
    // MARK: - Mocks & Test Data
    var affirmationService: MockAffirmationService!
    var repetitionService: MockRepetitionService!
    var streakService: MockStreakService!
    var notificationService: MockNotificationService!
    var audioService: MockAudioService!
    
    override func setUp() {
        super.setUp()
        affirmationService = MockAffirmationService()
        repetitionService = MockRepetitionService()
        streakService = MockStreakService()
        notificationService = MockNotificationService()
        audioService = MockAudioService()
    }
    
    override func tearDown() {
        affirmationService = nil
        repetitionService = nil
        streakService = nil
        notificationService = nil
        audioService = nil
        super.tearDown()
    }
    
    // MARK: - Core Repetition Functionality
    @MainActor func testCreateNewRepetition() throws {
        let affirmation = affirmationService.createMockAffirmation()
        let result = try repetitionService.addRepetition(for: affirmation.id, date: Date())
        XCTAssertTrue(result)
        XCTAssertEqual(repetitionService.repetitions(for: affirmation.id).count, 1)
    }
    
    @MainActor func testRetrieveRepetitionHistory() {
        let affirmation = affirmationService.createMockAffirmation()
        _ = try? repetitionService.addRepetition(for: affirmation.id, date: Date())
        let history = repetitionService.repetitions(for: affirmation.id)
        XCTAssertFalse(history.isEmpty)
    }
    
    @MainActor func testDailyRepetitionCounts() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: today)!
        _ = try? repetitionService.addRepetition(for: affirmation.id, date: today)
        _ = try? repetitionService.addRepetition(for: affirmation.id, date: yesterday)
        let todayCount = repetitionService.dailyCount(for: affirmation.id, date: today)
        let yesterdayCount = repetitionService.dailyCount(for: affirmation.id, date: yesterday)
        XCTAssertEqual(todayCount, 1)
        XCTAssertEqual(yesterdayCount, 1)
    }
    
    @MainActor func testRepetitionTargetTracking() {
        let affirmation = affirmationService.createMockAffirmation()
        for _ in 0..<100 {
            _ = try? repetitionService.addRepetition(for: affirmation.id, date: Date())
        }
        XCTAssertTrue(repetitionService.hasCompletedTarget(for: affirmation.id))
    }
    
    // MARK: - Streak Calculation
    @MainActor func testStartNewStreak() {
        let affirmation = affirmationService.createMockAffirmation()
        let started = streakService.startStreak(for: affirmation.id)
        XCTAssertTrue(started)
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 1)
    }
    
    @MainActor func testMaintainStreakWithDailyRepetitions() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: today)!
        _ = streakService.startStreak(for: affirmation.id)
        _ = streakService.addRepetition(for: affirmation.id, date: yesterday)
        _ = streakService.addRepetition(for: affirmation.id, date: today)
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 2)
    }
    
    @MainActor func testBreakStreakWhenDayMissed() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        let twoDaysAgo = Calendar.current.date(byAdding: .day, value: -2, to: today)!
        _ = streakService.startStreak(for: affirmation.id)
        _ = streakService.addRepetition(for: affirmation.id, date: twoDaysAgo)
        _ = streakService.addRepetition(for: affirmation.id, date: today)
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 1)
    }
    
    @MainActor func testStreakRecoveryAfterBreak() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        let twoDaysAgo = Calendar.current.date(byAdding: .day, value: -2, to: today)!
        _ = streakService.startStreak(for: affirmation.id)
        _ = streakService.addRepetition(for: affirmation.id, date: twoDaysAgo)
        _ = streakService.addRepetition(for: affirmation.id, date: today)
        _ = streakService.addRepetition(for: affirmation.id, date: today)
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 1)
    }
    
    @MainActor func testMaximumStreakCalculation() {
        let affirmation = affirmationService.createMockAffirmation()
        for i in 0..<5 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: Date())!
            _ = streakService.addRepetition(for: affirmation.id, date: date)
        }
        XCTAssertEqual(streakService.maximumStreak(for: affirmation.id), 5)
    }
    
    // MARK: - Milestone Tracking
    @MainActor func testMilestoneDetectionForRepetitionCounts() {
        let affirmation = affirmationService.createMockAffirmation()
        for i in 1...100 {
            _ = try? repetitionService.addRepetition(for: affirmation.id, date: Date())
            if [25, 50, 75, 100].contains(i) {
                XCTAssertTrue(repetitionService.isMilestone(for: affirmation.id, count: i))
            }
        }
    }
    
    @MainActor func testMilestonePersistenceAndRetrieval() {
        let affirmation = affirmationService.createMockAffirmation()
        for i in 1...50 {
            _ = try? repetitionService.addRepetition(for: affirmation.id, date: Date())
        }
        XCTAssertTrue(repetitionService.hasMilestone(for: affirmation.id, count: 50))
    }
    
    @MainActor func testMilestoneResetWhenAppropriate() {
        let affirmation = affirmationService.createMockAffirmation()
        for i in 1...100 {
            _ = try? repetitionService.addRepetition(for: affirmation.id, date: Date())
        }
        repetitionService.resetMilestones(for: affirmation.id)
        XCTAssertFalse(repetitionService.hasMilestone(for: affirmation.id, count: 100))
    }
    
    // MARK: - Integration with Other Systems
    @MainActor func testIntegrationWithAffirmationSystem() {
        let affirmation = affirmationService.createMockAffirmation()
        _ = try? repetitionService.addRepetition(for: affirmation.id, date: Date())
        XCTAssertTrue(affirmationService.hasAffirmation(affirmation.id))
    }
    
    @MainActor func testIntegrationWithAudioPlayback() {
        let affirmation = affirmationService.createMockAffirmation()
        audioService.play(for: affirmation.id)
        XCTAssertTrue(audioService.isPlaying(for: affirmation.id))
    }
    
    @MainActor func testIntegrationWithNotificationSystem() {
        let affirmation = affirmationService.createMockAffirmation()
        notificationService.scheduleReminder(for: affirmation.id)
        XCTAssertTrue(notificationService.hasScheduled(for: affirmation.id))
    }
    
    // MARK: - Edge Cases and Error Handling
    @MainActor func testInvalidInputData() {
        let affirmation = affirmationService.createMockAffirmation()
        XCTAssertThrowsError(try repetitionService.addRepetition(for: affirmation.id, date: Date.distantPast))
    }
    
    @MainActor func testSystemRecoveryAfterCrash() {
        let affirmation = affirmationService.createMockAffirmation()
        _ = try? repetitionService.addRepetition(for: affirmation.id, date: Date())
        repetitionService.simulateCrashAndRecovery()
        XCTAssertTrue(repetitionService.repetitions(for: affirmation.id).count >= 1)
    }
    
    @MainActor func testConcurrentRepetitionTracking() {
        let affirmation1 = affirmationService.createMockAffirmation()
        let affirmation2 = affirmationService.createMockAffirmation()
        _ = try? repetitionService.addRepetition(for: affirmation1.id, date: Date())
        _ = try? repetitionService.addRepetition(for: affirmation2.id, date: Date())
        XCTAssertEqual(repetitionService.repetitions(for: affirmation1.id).count, 1)
        XCTAssertEqual(repetitionService.repetitions(for: affirmation2.id).count, 1)
    }
    
    @MainActor func testPerformanceWithLargeDataSets() {
        let affirmation = affirmationService.createMockAffirmation()
        measure {
            for _ in 0..<10000 {
                _ = try? repetitionService.addRepetition(for: affirmation.id, date: Date())
            }
            XCTAssertTrue(repetitionService.repetitions(for: affirmation.id).count == 10000)
        }
    }
} 
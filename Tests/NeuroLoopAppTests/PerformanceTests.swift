import XCTest
@testable import <PERSON>eur<PERSON><PERSON><PERSON>A<PERSON>
@testable import Neuro<PERSON>oopCore
@testable import NeuroLoopShared

final class MockAppLauncher: AppLaunchTestable {
    func simulateAppLaunch() {
        // Simulate app launch by creating and accessing the app
        let app = NeuroLoopApp()
        _ = app.body
    }
}

final class PerformanceTests: XCTestCase {
    private var performanceMonitor: PerformanceMonitor!
    private var finalTestingService: FinalTestingService!
    private var affirmationService: AffirmationService!
    private var appLauncher: MockAppLauncher!
    
    override func setUp() {
        super.setUp()
        performanceMonitor = PerformanceMonitor.shared
        finalTestingService = FinalTestingService.shared
        // Initialize affirmation service with test repository
        let repository = try! SwiftDataAffirmationRepository()
        affirmationService = AffirmationService(repository: repository)
        appLauncher = MockAppLauncher()
    }
    
    override func tearDown() {
        performanceMonitor = nil
        finalTestingService = nil
        affirmationService = nil
        appLauncher = nil
        super.tearDown()
    }
    
    // MARK: - App Launch Performance
    
    func testAppLaunchTime() async throws {
        let launchTime = try await finalTestingService.measureAppLaunchTime(appLauncher: appLauncher)
        XCTAssertLessThan(launchTime, 2.0, "App launch time should be under 2 seconds")
    }
    
    // MARK: - Memory Usage Tests
    
    func testMemoryUsageWithLargeDataset() async throws {
        // Create 20+ affirmations
        for i in 0..<20 {
            _ = try await affirmationService.createAffirmation(
                text: "Test affirmation \(i) with a longer text to simulate real usage",
                category: .confidence,
                recordingURL: nil
            )
        }
        
        let memoryUsage = try await finalTestingService.measureMemoryUsage()
        XCTAssertLessThan(memoryUsage, 200 * 1024 * 1024, "Memory usage should be under 200MB")
    }
    
    func testMemoryUsageDuringExtendedUse() async throws {
        // Simulate extended use by performing multiple operations
        for _ in 0..<100 {
            _ = try await affirmationService.fetchAffirmations()
            _ = try await affirmationService.fetchFavoriteAffirmations()
            _ = try await affirmationService.getStatistics()
        }
        
        let memoryUsage = try await finalTestingService.measureMemoryUsage()
        XCTAssertLessThan(memoryUsage, 200 * 1024 * 1024, "Memory usage should be under 200MB after extended use")
    }
    
    // MARK: - UI Performance Tests
    
    func testUIResponsiveness() async throws {
        let frameRate = try await finalTestingService.measureFrameRate()
        XCTAssertGreaterThanOrEqual(frameRate, 55.0, "Frame rate should be at least 55 FPS for smooth UI")
        
        let cpuUsage = try await finalTestingService.measureCPUUsage()
        XCTAssertLessThan(cpuUsage, 50.0, "CPU usage should be under 50% during UI operations")
    }
    
    // MARK: - Animation Performance
    
    func testAnimationSmoothness() async throws {
        let frameRate = try await finalTestingService.measureFrameRate()
        XCTAssertGreaterThanOrEqual(frameRate, 55.0, "Frame rate should be at least 55 FPS for smooth animations")
        
        let cpuUsage = try await finalTestingService.measureCPUUsage()
        XCTAssertLessThan(cpuUsage, 50.0, "CPU usage should be under 50% during animations")
    }
    
    // MARK: - Battery Impact
    
    func testBatteryImpact() async throws {
        let batteryImpact = try await finalTestingService.measureBatteryImpact()
        XCTAssertLessThan(batteryImpact, 5.0, "Battery impact should be under 5% per minute of use")
    }
    
    // MARK: - Edge Case Performance
    
    func testPerformanceWithExtremeContent() async throws {
        // Create affirmation with very long text
        let longText = String(repeating: "Test affirmation with extreme content ", count: 50)
        _ = try await affirmationService.createAffirmation(
            text: longText,
            category: .confidence,
            recordingURL: nil
        )
        
        let memoryUsage = try await finalTestingService.measureMemoryUsage()
        XCTAssertLessThan(memoryUsage, 200 * 1024 * 1024, "Memory usage should be under 200MB with extreme content")
        
        let cpuUsage = try await finalTestingService.measureCPUUsage()
        XCTAssertLessThan(cpuUsage, 50.0, "CPU usage should be under 50% with extreme content")
    }
    
    func testPerformanceWithRapidOperations() async throws {
        // Simulate rapid creation and deletion
        for i in 0..<10 {
            let affirmation = try await affirmationService.createAffirmation(
                text: "Test affirmation \(i)",
                category: .confidence,
                recordingURL: nil
            )
            try await affirmationService.deleteAffirmation(id: affirmation.id)
        }
        
        let memoryUsage = try await finalTestingService.measureMemoryUsage()
        XCTAssertLessThan(memoryUsage, 200 * 1024 * 1024, "Memory usage should be under 200MB after rapid operations")
        
        let cpuUsage = try await finalTestingService.measureCPUUsage()
        XCTAssertLessThan(cpuUsage, 50.0, "CPU usage should be under 50% during rapid operations")
    }
    
    // MARK: - Comprehensive Performance Test
    
    func testComprehensivePerformance() async throws {
        let results = try await finalTestingService.runComprehensiveTests(appLauncher: appLauncher)
        XCTAssertTrue(results.success, "Comprehensive tests should pass")
        
        // Verify performance metrics
        XCTAssertLessThan(results.performanceMetrics.launchTime, 2.0, "App launch time should be under 2 seconds")
        XCTAssertLessThan(results.performanceMetrics.memoryUsage, 200 * 1024 * 1024, "Memory usage should be under 200MB")
        XCTAssertLessThan(results.performanceMetrics.cpuUsage, 50.0, "CPU usage should be under 50%")
        XCTAssertLessThan(results.performanceMetrics.batteryImpact, 5.0, "Battery impact should be under 5%")
        XCTAssertGreaterThanOrEqual(results.performanceMetrics.frameRate, 55.0, "Frame rate should be at least 55 FPS")
    }
} 
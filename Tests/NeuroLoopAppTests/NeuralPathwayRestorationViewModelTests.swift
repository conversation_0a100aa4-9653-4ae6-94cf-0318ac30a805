import XCTest
@testable import NeuroLoopUI
import NeuroLoopCore
import NeuroLoopInterfaces

final class NeuralPathwayRestorationViewModelTests: XCTestCase {
    // MARK: - Mocks
    class MockAffirmation: AffirmationProtocol {
        var id: UUID = UUID()
        var text: String = "Test Affirmation"
        var category: AffirmationCategory = .confidence
        var recordingURL: URL? = nil
        var currentCycleDay: Int = 1
        var cycleProgress: Double = 0.0
        var todayProgress: Double = 0.0
        var currentRepetitions: Int = 0
        var hasTodayQuotaMet: Bool = false
        var isCurrentCycleComplete: Bool = false
        var hasActiveCycle: Bool = false
        var completedCycles: Int = 0
        var cycleStartDate: Date? = Date()
        var lastRepetitionDate: Date? = Date()
        var dailyProgress: [Date: Int] = [:]
        var createdAt: Date = Date()
        var updatedAt: Date = Date()
        var energyLevel: Double = 0.0
        var moodRating: Int? = nil
        var notes: String? = nil
        var isFavorite: Bool = false
        var playCount: Int = 0
        var canPerformRepetition: Bool { true }
        func recordRepetition() throws {}
        func updateEnergyLevel(_ level: Double) {}
        func recordMood(_ rating: Int, notes: String?) {}
    }
    class MockRepetitionService: RepetitionServiceProtocol {
        var startCycleCalled = false
        var shouldThrow = false
        func recordRepetition(for affirmation: any AffirmationProtocol) async throws -> RepetitionResult {
            RepetitionResult(success: true, updatedAffirmation: affirmation, isQuotaMet: false, isCycleComplete: false)
        }
        func startCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
            startCycleCalled = true
            if shouldThrow { throw NSError(domain: "Test", code: 1) }
            return CycleResult(success: true, updatedAffirmation: affirmation)
        }
        func startSession(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
            if shouldThrow { throw NSError(domain: "Test", code: 1) }
            return CycleResult(success: true, updatedAffirmation: affirmation)
        }
        func getProgress(for affirmation: any AffirmationProtocol) -> ProgressInfo {
            ProgressInfo(
                todayProgress: 0,
                cycleProgress: 0,
                currentDay: 1,
                totalDays: 7,
                currentRepetitions: 0,
                totalRepetitions: 0,
                hasTodayQuotaMet: false,
                isCycleComplete: false,
                hasActiveCycle: false
            )
        }
        func getStreakInfo(for affirmation: any AffirmationProtocol) -> StreakInfo {
            StreakInfo(
                currentStreak: 0,
                longestStreak: 0,
                completedCycles: 0,
                hasActiveCycle: false,
                cycleStartDate: nil,
                lastRepetitionDate: nil
            )
        }
        func canPerformRepetition(for affirmation: any AffirmationProtocol) -> Bool { true }
        func timeUntilNextRepetition(for affirmation: any AffirmationProtocol) -> TimeInterval? { nil }
        func restartBrokenCycle(for affirmation: any AffirmationProtocol) async throws -> CycleResult {
            return CycleResult(success: true, updatedAffirmation: affirmation)
        }
        func isCycleBroken(for affirmation: any AffirmationProtocol) -> Bool { false }
    }

    // MARK: - Tests
    func testInitializationAndContentTiers() {
        let affirmation = MockAffirmation()
        let service = MockRepetitionService()
        let vm = NeuralPathwayRestorationViewModel(affirmation: affirmation, repetitionService: service)
        // Default tier is basic
        XCTAssertEqual(vm.selectedTier, .basic)
        XCTAssertFalse(vm.mainContent().isEmpty)
        XCTAssertFalse(vm.learnMoreContent().isEmpty)
        // Switch to intermediate
        vm.selectedTier = .intermediate
        XCTAssertFalse(vm.mainContent().isEmpty)
        XCTAssertFalse(vm.learnMoreContent().isEmpty)
        // Switch to advanced
        vm.selectedTier = .advanced
        XCTAssertFalse(vm.mainContent().isEmpty)
        XCTAssertFalse(vm.learnMoreContent().isEmpty)
    }
    func testNeuralStrengthCalculation() {
        let affirmation = MockAffirmation()
        affirmation.cycleProgress = 0.0
        let vm0 = NeuralPathwayRestorationViewModel(affirmation: affirmation, repetitionService: MockRepetitionService())
        XCTAssertEqual(vm0.neuralStrength, 0.0)
        affirmation.cycleProgress = 0.5
        let vm50 = NeuralPathwayRestorationViewModel(affirmation: affirmation, repetitionService: MockRepetitionService())
        XCTAssertEqual(vm50.neuralStrength, 0.5)
        affirmation.cycleProgress = 0.99
        let vm99 = NeuralPathwayRestorationViewModel(affirmation: affirmation, repetitionService: MockRepetitionService())
        XCTAssertEqual(vm99.neuralStrength, 0.99)
    }
    func testRestartCycleSuccessAndCallback() async {
        let affirmation = MockAffirmation()
        let service = MockRepetitionService()
        var callbackCalled = false
        let vm = NeuralPathwayRestorationViewModel(affirmation: affirmation, repetitionService: service, onCycleRestarted: { callbackCalled = true })
        await vm.restartCycle()
        XCTAssertFalse(vm.isLoading)
        XCTAssertNil(vm.error)
        XCTAssertTrue(service.startCycleCalled)
        XCTAssertTrue(callbackCalled)
    }
    func testRestartCycleErrorHandling() async {
        let affirmation = MockAffirmation()
        let service = MockRepetitionService()
        service.shouldThrow = true
        let vm = NeuralPathwayRestorationViewModel(affirmation: affirmation, repetitionService: service)
        await vm.restartCycle()
        XCTAssertFalse(vm.isLoading)
        XCTAssertNotNil(vm.error)
    }
    func testScienceReferencesPresent() {
        let vm = NeuralPathwayRestorationViewModel(affirmation: MockAffirmation(), repetitionService: MockRepetitionService())
        XCTAssertFalse(vm.scienceReferences.isEmpty)
    }
} 
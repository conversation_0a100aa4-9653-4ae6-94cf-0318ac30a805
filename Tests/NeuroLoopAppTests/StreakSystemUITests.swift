import XCTest
@testable import NeuroLoopCore
@testable import NeuroLoopUI
import NeuroLoopTestUtilities

/// Integration tests for the streak calculation and visualization system.
final class StreakSystemTests: XCTestCase {
    // MARK: - Mocks & Test Data
    var streakService: MockStreakService!
    var repetitionService: MockRepetitionService!
    var affirmationService: MockAffirmationService!
    var notificationService: MockNotificationService!
    
    override func setUp() {
        super.setUp()
        Task { @MainActor in
            self.streakService = MockStreakService()
            self.repetitionService = MockRepetitionService()
            self.affirmationService = MockAffirmationService()
            self.notificationService = MockNotificationService()
        }
    }
    
    override func tearDown() {
        Task { @MainActor in
            self.streakService = nil
            self.repetitionService = nil
            self.affirmationService = nil
            self.notificationService = nil
        }
        super.tearDown()
    }
    
    // MARK: - Streak Calculation Core Functionality
    @MainActor func testStreakInitializationForNewAffirmation() {
        let affirmation = affirmationService.createMockAffirmation()
        streakService.initializeStreak(for: affirmation.id)
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 0)
    }
    
    @MainActor func testStreakIncrementWithDailyRepetitions() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        for i in 0..<3 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: today)!
            streakService.addRepetition(for: affirmation.id, date: date)
        }
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 3)
    }
    
    @MainActor func testStreakResetWhenDaysMissed() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        let twoDaysAgo = Calendar.current.date(byAdding: .day, value: -2, to: today)!
        streakService.addRepetition(for: affirmation.id, date: twoDaysAgo)
        streakService.addRepetition(for: affirmation.id, date: today)
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 1)
    }
    
    @MainActor func testStreakRecoveryAfterBreak() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        let twoDaysAgo = Calendar.current.date(byAdding: .day, value: -2, to: today)!
        streakService.addRepetition(for: affirmation.id, date: twoDaysAgo)
        streakService.addRepetition(for: affirmation.id, date: today)
        streakService.addRepetition(for: affirmation.id, date: today)
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 1)
    }
    
    // MARK: - Streak Calendar Visualization
    @MainActor func testCalendarDateMapping() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        for i in 0..<7 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: today)!
            streakService.addRepetition(for: affirmation.id, date: date)
        }
        let calendarData = streakService.calendarData(for: affirmation.id, month: today)
        XCTAssertEqual(calendarData.count, 7)
    }
    
    @MainActor func testStreakDayHighlighting() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        streakService.addRepetition(for: affirmation.id, date: today)
        let calendarData = streakService.calendarData(for: affirmation.id, month: today)
        XCTAssertTrue(calendarData.contains { $0.isComplete })
    }
    
    @MainActor func testCurrentDayIndication() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        let calendarData = streakService.calendarData(for: affirmation.id, month: today)
        XCTAssertTrue(calendarData.contains { $0.date == today })
    }
    
    @MainActor func testMonthNavigationAndDateRange() {
        let affirmation = affirmationService.createMockAffirmation()
        let date = Calendar.current.date(byAdding: .month, value: -1, to: Date())!
        let calendarData = streakService.calendarData(for: affirmation.id, month: date)
        XCTAssertNotNil(calendarData)
    }
    
    // MARK: - Streak Timeline Visualization
    @MainActor func testTimelineDataGeneration() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        for i in 0..<10 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: today)!
            streakService.addRepetition(for: affirmation.id, date: date)
        }
        let timeline = streakService.timelineData(for: affirmation.id)
        XCTAssertEqual(timeline.count, 10)
    }
    
    @MainActor func testTimelineGapVisualization() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        let gapDay = Calendar.current.date(byAdding: .day, value: -2, to: today)!
        streakService.addRepetition(for: affirmation.id, date: gapDay)
        streakService.addRepetition(for: affirmation.id, date: today)
        let timeline = streakService.timelineData(for: affirmation.id)
        XCTAssertTrue(timeline.contains { $0.repetitions == 0 })
    }
    
    // MARK: - Streak Statistics and Analytics
    @MainActor func testLongestStreakCalculation() {
        let affirmation = affirmationService.createMockAffirmation()
        for i in 0..<5 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: Date())!
            streakService.addRepetition(for: affirmation.id, date: date)
        }
        XCTAssertEqual(streakService.longestStreak(for: affirmation.id), 5)
    }
    
    @MainActor func testCurrentStreakCalculation() {
        let affirmation = affirmationService.createMockAffirmation()
        for i in 0..<3 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: Date())!
            streakService.addRepetition(for: affirmation.id, date: date)
        }
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 3)
    }
    
    @MainActor func testTotalActiveDaysCalculation() {
        let affirmation = affirmationService.createMockAffirmation()
        for i in 0..<10 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: Date())!
            streakService.addRepetition(for: affirmation.id, date: date)
        }
        XCTAssertEqual(streakService.totalActiveDays(for: affirmation.id), 10)
    }
    
    // MARK: - Integration with Other Systems
    @MainActor func testIntegrationWithRepetitionSystem() {
        let affirmation = affirmationService.createMockAffirmation()
        let today = Date()
        _ = try? repetitionService.addRepetition(for: affirmation.id, date: today)
        streakService.addRepetition(for: affirmation.id, date: today)
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 1)
    }
    
    @MainActor func testIntegrationWithNotificationSystem() {
        let affirmation = affirmationService.createMockAffirmation()
        notificationService.scheduleReminder(for: affirmation.id)
        XCTAssertTrue(notificationService.hasScheduled(for: affirmation.id))
    }
    
    // MARK: - Edge Cases and Error Handling
    @MainActor func testTimezoneChangeBehavior() {
        let affirmation = affirmationService.createMockAffirmation()
        let now = Date()
        let timezoneAdjusted = Calendar.current.date(byAdding: .hour, value: 5, to: now)!
        streakService.addRepetition(for: affirmation.id, date: now)
        streakService.addRepetition(for: affirmation.id, date: timezoneAdjusted)
        XCTAssertEqual(streakService.currentStreak(for: affirmation.id), 1)
    }
    
    @MainActor func testSystemRecoveryAfterCrash() {
        let affirmation = affirmationService.createMockAffirmation()
        streakService.addRepetition(for: affirmation.id, date: Date())
        streakService.simulateCrashAndRecovery()
        XCTAssertTrue(streakService.currentStreak(for: affirmation.id) >= 1)
    }
    
    @MainActor func testConcurrentStreakTracking() {
        let affirmation1 = affirmationService.createMockAffirmation()
        let affirmation2 = affirmationService.createMockAffirmation()
        streakService.addRepetition(for: affirmation1.id, date: Date())
        streakService.addRepetition(for: affirmation2.id, date: Date())
        XCTAssertEqual(streakService.currentStreak(for: affirmation1.id), 1)
        XCTAssertEqual(streakService.currentStreak(for: affirmation2.id), 1)
    }
    
    @MainActor func testPerformanceWithExtendedStreakHistories() {
        let affirmation = affirmationService.createMockAffirmation()
        measure {
            for i in 0..<365 {
                let date = Calendar.current.date(byAdding: .day, value: -i, to: Date())!
                streakService.addRepetition(for: affirmation.id, date: date)
            }
            XCTAssertEqual(streakService.totalActiveDays(for: affirmation.id), 365)
        }
    }
}

// MARK: - Mock Service Implementations (stubs for demonstration)

struct MockCalendarDay {
    let date: Date
    let isStreakDay: Bool
    let isCurrentDay: Bool
}

struct MockTimelineEntry {
    let date: Date
    let isGap: Bool
}






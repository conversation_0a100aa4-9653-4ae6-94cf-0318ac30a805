import XCTest

@available(iOS 17.0, macOS 14.0, *)
final class SettingsUITests: XCTestCase {
    var app: XCUIApplication!

    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launchArguments += ["-UITestMode", "-BypassOnboarding", "-BypassLogin"]
        app.launch()
    }

    override func tearDownWithError() throws {
        app = nil
    }

    // MARK: - General Settings
    func testThemeSwitching() {
        app.tabBars.buttons["Settings"].tap()
        let darkModeToggle = app.switches["Dark Mode"]
        if darkModeToggle.exists {
            let initialValue = darkModeToggle.value as? String
            darkModeToggle.tap()
            XCTAssertNotEqual(darkModeToggle.value as? String, initialValue)
        }
    }

    func testLanguageSelection() {
        app.tabBars.buttons["Settings"].tap()
        let languageCell = app.tables.staticTexts["Language"]
        if languageCell.exists {
            languageCell.tap()
            let englishOption = app.tables.staticTexts["English"]
            if englishOption.exists {
                englishOption.tap()
                XCTAssertTrue(languageCell.label.contains("English"))
            }
        }
    }

    func testDefaultViewConfiguration() {
        app.tabBars.buttons["Settings"].tap()
        let defaultViewCell = app.tables.staticTexts["Default View"]
        if defaultViewCell.exists {
            defaultViewCell.tap()
            let homeOption = app.tables.staticTexts["Home"]
            if homeOption.exists {
                homeOption.tap()
                XCTAssertTrue(defaultViewCell.label.contains("Home"))
            }
        }
    }

    // MARK: - Notification Settings
    func testNotificationToggle() {
        app.tabBars.buttons["Settings"].tap()
        let notificationToggle = app.switches["Notifications"]
        if notificationToggle.exists {
            let initialValue = notificationToggle.value as? String
            notificationToggle.tap()
            XCTAssertNotEqual(notificationToggle.value as? String, initialValue)
        }
    }

    func testNotificationTimeSetting() {
        app.tabBars.buttons["Settings"].tap()
        let notificationTimeCell = app.tables.staticTexts["Notification Time"]
        if notificationTimeCell.exists {
            notificationTimeCell.tap()
            let timePicker = app.datePickers.firstMatch
            if timePicker.exists {
                // Simulate changing the time
                timePicker.adjust(toPickerWheelValue: "9:00 AM")
                app.buttons["Done"].tap()
                XCTAssertTrue(notificationTimeCell.label.contains("9:00 AM"))
            }
        }
    }

    func testDoNotDisturbIntegration() {
        app.tabBars.buttons["Settings"].tap()
        let dndToggle = app.switches["Do Not Disturb"]
        if dndToggle.exists {
            let initialValue = dndToggle.value as? String
            dndToggle.tap()
            XCTAssertNotEqual(dndToggle.value as? String, initialValue)
        }
    }

    // MARK: - Account Settings
    func testProfileEditing() {
        app.tabBars.buttons["Settings"].tap()
        let profileCell = app.tables.staticTexts["Profile"]
        if profileCell.exists {
            profileCell.tap()
            let editButton = app.buttons["Edit"]
            if editButton.exists {
                editButton.tap()
                let nameField = app.textFields["Name"]
                if nameField.exists {
                    nameField.tap()
                    nameField.typeText("Test User")
                    app.buttons["Save"].tap()
                    XCTAssertTrue(profileCell.label.contains("Test User"))
                }
            }
        }
    }

    func testDataExportAndRestore() {
        app.tabBars.buttons["Settings"].tap()
        let exportCell = app.tables.staticTexts["Export Data"]
        if exportCell.exists {
            exportCell.tap()
            let exportSuccessAlert = app.alerts["Export Successful"]
            XCTAssertTrue(exportSuccessAlert.waitForExistence(timeout: 2))
            exportSuccessAlert.buttons["OK"].tap()
        }
        let restoreCell = app.tables.staticTexts["Restore Data"]
        if restoreCell.exists {
            restoreCell.tap()
            let restoreSuccessAlert = app.alerts["Restore Successful"]
            XCTAssertTrue(restoreSuccessAlert.waitForExistence(timeout: 2))
            restoreSuccessAlert.buttons["OK"].tap()
        }
    }

    // MARK: - Premium Settings
    func testPremiumStatusAndRestorePurchases() {
        app.tabBars.buttons["Settings"].tap()
        let premiumCell = app.tables.staticTexts["Premium"]
        if premiumCell.exists {
            premiumCell.tap()
            let restoreButton = app.buttons["Restore Purchases"]
            if restoreButton.exists {
                restoreButton.tap()
                let restoreAlert = app.alerts["Restore Successful"]
                XCTAssertTrue(restoreAlert.waitForExistence(timeout: 2))
                restoreAlert.buttons["OK"].tap()
            }
        }
    }

    // MARK: - Advanced Settings
    func testResetDataOption() {
        app.tabBars.buttons["Settings"].tap()
        let resetCell = app.tables.staticTexts["Reset Data"]
        if resetCell.exists {
            resetCell.tap()
            let deleteAlert = app.alerts["Delete All Data"]
            XCTAssertTrue(deleteAlert.waitForExistence(timeout: 2))
            deleteAlert.buttons["Delete"].tap()
        }
    }

    // MARK: - Accessibility
    func testAccessibilityIdentifiers() {
        app.tabBars.buttons["Settings"].tap()
        let settingsTable = app.tables.firstMatch
        XCTAssertTrue(settingsTable.exists)
        // Check for some known accessibility identifiers
        XCTAssertTrue(app.switches["Dark Mode"].exists)
        XCTAssertTrue(app.switches["Notifications"].exists)
    }

    // MARK: - Persistence
    func testSettingsPersistenceAcrossAppRestarts() {
        app.tabBars.buttons["Settings"].tap()
        let darkModeToggle = app.switches["Dark Mode"]
        if darkModeToggle.exists {
            let initialValue = darkModeToggle.value as? String
            darkModeToggle.tap()
            app.terminate()
            app.launch()
            app.tabBars.buttons["Settings"].tap()
            let newValue = app.switches["Dark Mode"].value as? String
            XCTAssertEqual(newValue, initialValue == "1" ? "0" : "1")
        }
    }

    func testCustomThemeCreationButtonPremiumGating() {
        let app = XCUIApplication()
        app.launchArguments.append("-isPremiumUser")
        app.launch()
        app.tabBars.buttons["Settings"].tap()
        let createButton = app.buttons["Create Custom Theme"]
        if app.launchArguments.contains("-isPremiumUser") {
            XCTAssertTrue(createButton.isEnabled)
        } else {
            XCTAssertFalse(createButton.isEnabled)
        }
    }

    func testThemePreviewUpdatesWithColorPickers() {
        let app = XCUIApplication()
        app.launchArguments.append("-isPremiumUser")
        app.launch()
        app.tabBars.buttons["Settings"].tap()
        let createButton = app.buttons["Create Custom Theme"]
        if createButton.isEnabled {
            createButton.tap()
            let nameField = app.textFields["Theme Name"]
            nameField.tap()
            nameField.typeText("Preview Test")
            let colorPicker = app.otherElements["ColorPicker"].firstMatch
            XCTAssertTrue(colorPicker.exists)
            let preview = app.otherElements["ThemePreview"]
            XCTAssertTrue(preview.exists)
        }
    }
} 
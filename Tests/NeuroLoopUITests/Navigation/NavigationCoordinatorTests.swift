import XCTest
import Swift<PERSON>
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoop<PERSON>

@available(iOS 17.0, macOS 14.0, *)
final class NavigationCoordinatorTests: XCTestCase {
    private var coordinator: NavigationCoordinator!
    
    override func setUp() {
        super.setUp()
        coordinator = NavigationCoordinator()
    }
    
    override func tearDown() {
        coordinator = nil
        super.tearDown()
    }
    
    // MARK: - Tab Navigation Tests
    
    func testNavigateToTab() {
        // Given
        let initialTab = coordinator.selectedTab
        
        // When
        coordinator.navigateToTab(.library)
        
        // Then
        XCTAssertNotEqual(coordinator.selectedTab, initialTab)
        XCTAssertEqual(coordinator.selectedTab, .library)
    }
    
    func testPopToRoot() {
        // Given
        coordinator.navigateToRoute(.addAffirmation)
        XCTAssertFalse(coordinator.homePath.isEmpty)
        
        // When
        coordinator.popToRoot(.home)
        
        // Then
        XCTAssertTrue(coordinator.homePath.isEmpty)
    }
    
    // MARK: - Route Navigation Tests
    
    func testNavigateToRoute() {
        // Given
        let route = NavigationRoute.addAffirmation
        
        // When
        coordinator.navigateToRoute(route)
        
        // Then
        XCTAssertFalse(coordinator.homePath.isEmpty)
        XCTAssertEqual(coordinator.homePath.last, route)
    }
    
    func testNavigateToRouteInDifferentTab() {
        // Given
        let route = NavigationRoute.affirmationDetails(id: UUID())
        
        // When
        coordinator.navigateToRoute(route)
        
        // Then
        XCTAssertFalse(coordinator.homePath.isEmpty)
        XCTAssertEqual(coordinator.homePath.last, route)
    }
    
    func testNavigateToRouteWithParameters() {
        // Given
        let id = UUID()
        let route = NavigationRoute.affirmationDetails(id: id)
        
        // When
        coordinator.navigateToRoute(route)
        
        // Then
        XCTAssertFalse(coordinator.homePath.isEmpty)
        XCTAssertEqual(coordinator.homePath.last, route)
    }
    
    // MARK: - Path Management Tests
    
    func testClearPath() {
        // Given
        coordinator.navigateToRoute(.addAffirmation)
        XCTAssertFalse(coordinator.homePath.isEmpty)
        
        // When
        coordinator.clearPath(.home)
        
        // Then
        XCTAssertTrue(coordinator.homePath.isEmpty)
    }
    
    func testClearAllPaths() {
        // Given
        coordinator.navigateToRoute(.addAffirmation)
        coordinator.navigateToTab(.library)
        coordinator.navigateToRoute(.affirmationDetails(id: UUID()))
        
        // When
        coordinator.clearAllPaths()
        
        // Then
        XCTAssertTrue(coordinator.homePath.isEmpty)
        XCTAssertTrue(coordinator.libraryPath.isEmpty)
        XCTAssertTrue(coordinator.settingsPath.isEmpty)
    }
    
    // MARK: - Path Persistence Tests
    
    func testPathPersistence() {
        // Given
        let route = NavigationRoute.addAffirmation
        
        // When
        coordinator.navigateToRoute(route)
        let path = coordinator.homePath
        
        // Then
        XCTAssertFalse(path.isEmpty)
        XCTAssertEqual(path.last, route)
    }
    
    // MARK: - Error Handling Tests
    
    func testInvalidRouteNavigation() {
        // Given
        let invalidRoute = NavigationRoute.affirmationDetails(id: UUID())
        
        // When
        coordinator.navigateToRoute(invalidRoute)
        
        // Then
        XCTAssertFalse(coordinator.homePath.isEmpty)
        XCTAssertEqual(coordinator.homePath.last, invalidRoute)
    }
    
    func testPopFromEmptyPath() {
        // Given
        XCTAssertTrue(coordinator.homePath.isEmpty)
        
        // When
        coordinator.popToRoot(.home)
        
        // Then
        XCTAssertTrue(coordinator.homePath.isEmpty)
    }
} 
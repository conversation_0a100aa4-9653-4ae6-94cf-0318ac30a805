import Combine
import NeuroLoopTypes
import XCTest

@testable import NeuroLoopUI

// import NeuroLoopInterfaces
// import NeuroLoopShared

@available(iOS 17.0, macOS 14.0, *)
final class SettingsViewModelTests: XCTestCase {
    private var viewModel: SettingsViewModel!
    private var mockPurchaseManager: MockPurchaseManager!
    private var mockDataExportService: MockDataExportService!
    private var userDefaults: UserDefaults!
    private var mockThemeManager: MockThemeManager!
    private var mockHapticManager: MockHapticManager!

    @MainActor override func setUp() {
        super.setUp()
        userDefaults = UserDefaults(suiteName: #function)
        mockPurchaseManager = MockPurchaseManager()
        mockDataExportService = MockDataExportService()
        mockThemeManager = MockThemeManager()
        mockHapticManager = MockHapticManager()

        viewModel = SettingsViewModel(
            userDefaults: userDefaults,
            purchaseManager: mockPurchaseManager,
            dataExportService: mockDataExportService,
            themeManager: mockThemeManager,
            hapticManager: mockHapticManager
        )
    }

    override func tearDown() {
        viewModel = nil
        mockPurchaseManager = nil
        mockDataExportService = nil
        userDefaults.removePersistentDomain(forName: #function)
        userDefaults = nil
        super.tearDown()
    }

    // MARK: - Settings Tests

    func testDarkModeSetting() {
        // Given
        let initialValue = viewModel.isDarkModeEnabled

        // When
        viewModel.isDarkModeEnabled.toggle()

        // Then
        XCTAssertNotEqual(viewModel.isDarkModeEnabled, initialValue)
        XCTAssertEqual(userDefaults.bool(forKey: "isDarkModeEnabled"), viewModel.isDarkModeEnabled)
    }

    func testNotificationsSetting() {
        // Given
        let initialValue = viewModel.notificationsEnabled

        // When
        viewModel.notificationsEnabled.toggle()

        // Then
        XCTAssertNotEqual(viewModel.notificationsEnabled, initialValue)
        XCTAssertEqual(
            userDefaults.bool(forKey: "notificationsEnabled"), viewModel.notificationsEnabled)
    }

    func testReminderTimeSetting() {
        // Given
        let newTime = Date().addingTimeInterval(3600)

        // When
        viewModel.reminderTime = newTime

        // Then
        XCTAssertEqual(viewModel.reminderTime, newTime)
        XCTAssertEqual(userDefaults.object(forKey: "reminderTime") as? Date, newTime)
    }

    func testHapticFeedbackSetting() {
        // Given
        let initialValue = viewModel.hapticFeedbackEnabled

        // When
        viewModel.hapticFeedbackEnabled.toggle()

        // Then
        XCTAssertNotEqual(viewModel.hapticFeedbackEnabled, initialValue)
        XCTAssertEqual(
            userDefaults.bool(forKey: "hapticFeedbackEnabled"), viewModel.hapticFeedbackEnabled)
    }

    func testSoundEffectsSetting() {
        // Given
        let initialValue = viewModel.soundEffectsEnabled

        // When
        viewModel.soundEffectsEnabled.toggle()

        // Then
        XCTAssertNotEqual(viewModel.soundEffectsEnabled, initialValue)
        XCTAssertEqual(
            userDefaults.bool(forKey: "soundEffectsEnabled"), viewModel.soundEffectsEnabled)
    }

    func testDailyGoalSetting() {
        // Given
        let newGoal = 10

        // When
        viewModel.dailyGoal = newGoal

        // Then
        XCTAssertEqual(viewModel.dailyGoal, newGoal)
        XCTAssertEqual(userDefaults.integer(forKey: "dailyGoal"), newGoal)
    }

    // MARK: - Premium Tests

    func testPurchasePremium() async throws {
        // Given
        mockPurchaseManager.shouldSucceed = true

        // When
        try await viewModel.purchasePremium()

        // Then
        XCTAssertTrue(viewModel.isPremiumUser)
        XCTAssertTrue(userDefaults.bool(forKey: "isPremiumUser"))
    }

    func testPurchasePremiumWithError() async {
        // Given
        mockPurchaseManager.shouldSucceed = false

        // When/Then
        do {
            try await viewModel.purchasePremium()
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertFalse(viewModel.isPremiumUser)
        }
    }

    func testRestorePurchases() async throws {
        // Given
        mockPurchaseManager.shouldSucceed = true
        userDefaults.set(true, forKey: "isPremiumUser")

        // When
        try await viewModel.restorePurchases()

        // Then
        XCTAssertTrue(viewModel.isPremiumUser)
    }

    func testRestorePurchasesWithError() async {
        // Given
        mockPurchaseManager.shouldSucceed = false

        // When/Then
        do {
            try await viewModel.restorePurchases()
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertFalse(viewModel.isPremiumUser)
        }
    }

    // MARK: - Data Management Tests

    func testExportData() async throws {
        // Given
        mockDataExportService.shouldSucceed = true

        // When
        let url = try await viewModel.exportData()

        // Then
        XCTAssertNotNil(url)
    }

    func testExportDataWithError() async {
        // Given
        mockDataExportService.shouldSucceed = false

        // When/Then
        do {
            _ = try await viewModel.exportData()
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }

    func testDeleteAllData() async throws {
        // Given
        mockDataExportService.shouldSucceed = true

        // When
        try await viewModel.deleteAllData()

        // Then
        XCTAssertFalse(viewModel.isDarkModeEnabled)
        XCTAssertTrue(viewModel.notificationsEnabled)
        XCTAssertTrue(viewModel.hapticFeedbackEnabled)
        XCTAssertTrue(viewModel.soundEffectsEnabled)
        XCTAssertEqual(viewModel.dailyGoal, 5)
    }

    func testDeleteAllDataWithError() async {
        // Given
        mockDataExportService.shouldSucceed = false

        // When/Then
        do {
            try await viewModel.deleteAllData()
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
}

// MARK: - Mock Services

private class MockPurchaseManager: PurchaseManagerProtocol {
    var shouldSucceed = true

    func purchasePremium() async throws {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
    }

    func restorePurchases() async throws {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
    }
}

private class MockDataExportService: DataExportServiceProtocol {
    var shouldSucceed = true

    func exportData() async throws -> URL {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
        return URL(fileURLWithPath: NSTemporaryDirectory())
    }

    func deleteAllData() async throws {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
    }
}

private class MockThemeManager: ThemeManaging {
    var currentTheme: Theme = .defaultLight
    func setTheme(_ theme: Theme) { currentTheme = theme }
}

private class MockHapticManager: HapticGenerating {
    @MainActor
    func lightImpact() async {}
    @MainActor
    func mediumImpact() async {}
    @MainActor
    func heavyImpact() async {}
    @MainActor
    func playSuccess() async {}
    @MainActor
    func playError() async {}
    @MainActor
    func playWarning() async {}
    @MainActor
    func playSelection() async {}
    @MainActor
    func playAffirmationCompletionPattern() async {}
    @MainActor
    func playCelebrationPattern() async {}
    @MainActor
    func playStreakCompletionPattern() async {}
    @MainActor
    func playImpact(style: ImpactStyle) async {}
}

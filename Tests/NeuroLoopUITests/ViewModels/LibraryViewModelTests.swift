import XCTest
import NeuroLoopCore
import NeuroLoopInterfaces
import NeuroLoopUI
import SwiftData

@available(iOS 17.0, macOS 14.0, *)
final class LibraryViewModelTests: XCTestCase {
    private var viewModel: LibraryViewModel!
    private var mockService: MockAffirmationService!
    private var modelContainer: ModelContainer!
    
    override func setUp() {
        super.setUp()
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        modelContainer = try! ModelContainer(for: Affirmation.self, configurations: config)
        mockService = MockAffirmationService()
        viewModel = LibraryViewModel(affirmationService: mockService)
    }
    
    override func tearDown() {
        viewModel = nil
        mockService = nil
        modelContainer = nil
        super.tearDown()
    }
    
    // MARK: - Loading Tests
    
    func testLoadAffirmations() async {
        // Given
        let testAffirmations = [
            Affirmation(text: "Test 1", category: .personalGrowth),
            Affirmation(text: "Test 2", category: .motivation)
        ]
        mockService.mockAffirmations = testAffirmations
        
        // When
        await viewModel.loadAffirmations()
        
        // Then
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNil(viewModel.errorMessage)
        XCTAssertEqual(viewModel.affirmations.count, 2)
    }
    
    func testLoadAffirmationsWithError() async {
        // Given
        mockService.shouldThrowError = true
        
        // When
        await viewModel.loadAffirmations()
        
        // Then
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNotNil(viewModel.errorMessage)
        XCTAssertTrue(viewModel.affirmations.isEmpty)
    }
    
    // MARK: - Filtering Tests
    
    func testFilterBySearchText() async {
        // Given
        let testAffirmations = [
            Affirmation(text: "Test 1", category: .personalGrowth),
            Affirmation(text: "Test 2", category: .motivation)
        ]
        mockService.mockAffirmations = testAffirmations
        await viewModel.loadAffirmations()
        
        // When
        viewModel.searchText = "Test 1"
        
        // Then
        XCTAssertEqual(viewModel.filteredAffirmations.count, 1)
        XCTAssertEqual(viewModel.filteredAffirmations.first?.text, "Test 1")
    }
    
    func testFilterByCategory() async {
        // Given
        let testAffirmations = [
            Affirmation(text: "Test 1", category: .personalGrowth),
            Affirmation(text: "Test 2", category: .motivation)
        ]
        mockService.mockAffirmations = testAffirmations
        await viewModel.loadAffirmations()
        
        // When
        viewModel.selectedCategory = .personalGrowth
        
        // Then
        XCTAssertEqual(viewModel.filteredAffirmations.count, 1)
        XCTAssertEqual(viewModel.filteredAffirmations.first?.category, .personalGrowth)
    }
    
    // MARK: - Sorting Tests
    
    func testSortByNewest() async {
        // Given
        let testAffirmations = [
            Affirmation(text: "Test 1", category: .personalGrowth),
            Affirmation(text: "Test 2", category: .motivation)
        ]
        mockService.mockAffirmations = testAffirmations
        await viewModel.loadAffirmations()
        
        // When
        viewModel.sortOption = .newest
        
        // Then
        XCTAssertEqual(viewModel.filteredAffirmations.first?.text, "Test 2")
    }
    
    func testSortByOldest() async {
        // Given
        let testAffirmations = [
            Affirmation(text: "Test 1", category: .personalGrowth),
            Affirmation(text: "Test 2", category: .motivation)
        ]
        mockService.mockAffirmations = testAffirmations
        await viewModel.loadAffirmations()
        
        // When
        viewModel.sortOption = .oldest
        
        // Then
        XCTAssertEqual(viewModel.filteredAffirmations.first?.text, "Test 1")
    }
    
    // MARK: - Favorite Tests
    
    func testToggleFavorite() async throws {
        // Given
        let testAffirmation = Affirmation(text: "Test", category: .personalGrowth)
        mockService.mockAffirmations = [testAffirmation]
        await viewModel.loadAffirmations()
        
        // When
        try await viewModel.toggleFavorite(testAffirmation)
        
        // Then
        XCTAssertTrue(viewModel.affirmations.first?.isFavorite ?? false)
    }
    
    func testToggleFavoriteWithError() async {
        // Given
        let testAffirmation = Affirmation(text: "Test", category: .personalGrowth)
        mockService.mockAffirmations = [testAffirmation]
        mockService.shouldThrowError = true
        await viewModel.loadAffirmations()
        
        // When/Then
        do {
            try await viewModel.toggleFavorite(testAffirmation)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(viewModel.errorMessage)
        }
    }
    
    // MARK: - Delete Tests
    
    func testDeleteAffirmation() async throws {
        // Given
        let testAffirmation = Affirmation(text: "Test", category: .personalGrowth)
        mockService.mockAffirmations = [testAffirmation]
        await viewModel.loadAffirmations()
        
        // When
        try await viewModel.deleteAffirmation(testAffirmation)
        
        // Then
        XCTAssertTrue(viewModel.affirmations.isEmpty)
    }
    
    func testDeleteAffirmationWithError() async {
        // Given
        let testAffirmation = Affirmation(text: "Test", category: .personalGrowth)
        mockService.mockAffirmations = [testAffirmation]
        mockService.shouldThrowError = true
        await viewModel.loadAffirmations()
        
        // When/Then
        do {
            try await viewModel.deleteAffirmation(testAffirmation)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(viewModel.errorMessage)
        }
    }
}

// MARK: - Mock Service

private class MockAffirmationService: AffirmationServiceProtocol {
    var mockAffirmations: [AffirmationProtocol] = []
    var shouldThrowError = false
    
    func fetchAffirmations() async throws -> [AffirmationProtocol] {
        if shouldThrowError {
            throw NSError(domain: "Test", code: 1)
        }
        return mockAffirmations
    }
    
    func createAffirmation(_ affirmation: AffirmationProtocol) async throws -> AffirmationProtocol {
        if shouldThrowError {
            throw NSError(domain: "Test", code: 1)
        }
        return affirmation
    }
    
    func updateAffirmation(_ affirmation: AffirmationProtocol) async throws -> AffirmationProtocol {
        if shouldThrowError {
            throw NSError(domain: "Test", code: 1)
        }
        return affirmation
    }
    
    func deleteAffirmation(id: UUID) async throws {
        if shouldThrowError {
            throw NSError(domain: "Test", code: 1)
        }
    }
    
    func toggleFavorite(_ affirmation: AffirmationProtocol) async throws -> AffirmationProtocol {
        if shouldThrowError {
            throw NSError(domain: "Test", code: 1)
        }
        var updated = affirmation
        updated.isFavorite.toggle()
        return updated
    }
} 
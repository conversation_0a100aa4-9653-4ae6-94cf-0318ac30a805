import XCTest
import NeuroLoop<PERSON>ore
import NeuroLoopInterfaces
import Neuro<PERSON><PERSON><PERSON>

@available(iOS 17.0, macOS 14.0, *)
final class HomeViewModelTests: XCTestCase {
    private var viewModel: HomeViewModel!
    private var mockAffirmationService: MockAffirmationService!
    private var mockProgressTracker: MockProgressTracker!
    
    override func setUp() {
        super.setUp()
        mockAffirmationService = MockAffirmationService()
        mockProgressTracker = MockProgressTracker()
        
        viewModel = HomeViewModel(
            affirmationService: mockAffirmationService,
            progressTracker: mockProgressTracker
        )
    }
    
    override func tearDown() {
        viewModel = nil
        mockAffirmationService = nil
        mockProgressTracker = nil
        super.tearDown()
    }
    
    // MARK: - Data Loading Tests
    
    func testLoadData() async throws {
        // Given
        mockAffirmationService.shouldSucceed = true
        mockProgressTracker.shouldSucceed = true
        
        // When
        try await viewModel.loadData()
        
        // Then
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNil(viewModel.error)
        XCTAssertFalse(viewModel.activeAffirmations.isEmpty)
        XCTAssertNotNil(viewModel.todaysProgress)
    }
    
    func testLoadDataWithError() async {
        // Given
        mockAffirmationService.shouldSucceed = false
        
        // When/Then
        do {
            try await viewModel.loadData()
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertTrue(viewModel.isLoading)
            XCTAssertNotNil(viewModel.error)
        }
    }
    
    // MARK: - Affirmation Management Tests
    
    func testAddAffirmation() async throws {
        // Given
        mockAffirmationService.shouldSucceed = true
        let affirmation = Affirmation(
            id: UUID(),
            text: "Test affirmation",
            category: .personal,
            isFavorite: false,
            createdAt: Date()
        )
        
        // When
        try await viewModel.addAffirmation(affirmation)
        
        // Then
        XCTAssertTrue(viewModel.activeAffirmations.contains(affirmation))
    }
    
    func testAddAffirmationWithError() async {
        // Given
        mockAffirmationService.shouldSucceed = false
        let affirmation = Affirmation(
            id: UUID(),
            text: "Test affirmation",
            category: .personal,
            isFavorite: false,
            createdAt: Date()
        )
        
        // When/Then
        do {
            try await viewModel.addAffirmation(affirmation)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertFalse(viewModel.activeAffirmations.contains(affirmation))
        }
    }
    
    func testDeleteAffirmation() async throws {
        // Given
        mockAffirmationService.shouldSucceed = true
        let affirmation = Affirmation(
            id: UUID(),
            text: "Test affirmation",
            category: .personal,
            isFavorite: false,
            createdAt: Date()
        )
        try await viewModel.addAffirmation(affirmation)
        
        // When
        try await viewModel.deleteAffirmation(affirmation)
        
        // Then
        XCTAssertFalse(viewModel.activeAffirmations.contains(affirmation))
    }
    
    func testDeleteAffirmationWithError() async {
        // Given
        mockAffirmationService.shouldSucceed = false
        let affirmation = Affirmation(
            id: UUID(),
            text: "Test affirmation",
            category: .personal,
            isFavorite: false,
            createdAt: Date()
        )
        
        // When/Then
        do {
            try await viewModel.deleteAffirmation(affirmation)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
    
    func testToggleFavorite() async throws {
        // Given
        mockAffirmationService.shouldSucceed = true
        let affirmation = Affirmation(
            id: UUID(),
            text: "Test affirmation",
            category: .personal,
            isFavorite: false,
            createdAt: Date()
        )
        try await viewModel.addAffirmation(affirmation)
        
        // When
        try await viewModel.toggleFavorite(affirmation)
        
        // Then
        let updatedAffirmation = viewModel.activeAffirmations.first { $0.id == affirmation.id }
        XCTAssertNotNil(updatedAffirmation)
        XCTAssertTrue(updatedAffirmation?.isFavorite ?? false)
    }
    
    // MARK: - Progress Tracking Tests
    
    func testRecordProgress() async throws {
        // Given
        mockProgressTracker.shouldSucceed = true
        let affirmation = Affirmation(
            id: UUID(),
            text: "Test affirmation",
            category: .personal,
            isFavorite: false,
            createdAt: Date()
        )
        
        // When
        try await viewModel.recordProgress(for: affirmation)
        
        // Then
        XCTAssertNotNil(viewModel.todaysProgress)
    }
    
    func testRecordProgressWithError() async {
        // Given
        mockProgressTracker.shouldSucceed = false
        let affirmation = Affirmation(
            id: UUID(),
            text: "Test affirmation",
            category: .personal,
            isFavorite: false,
            createdAt: Date()
        )
        
        // When/Then
        do {
            try await viewModel.recordProgress(for: affirmation)
            XCTFail("Expected error to be thrown")
        } catch {
            XCTAssertNotNil(error)
        }
    }
}

// MARK: - Mock Services

private class MockAffirmationService: AffirmationServiceProtocol {
    var shouldSucceed = true
    
    func loadAffirmations() async throws -> [Affirmation] {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
        return [
            Affirmation(
                id: UUID(),
                text: "Test affirmation",
                category: .personal,
                isFavorite: false,
                createdAt: Date()
            )
        ]
    }
    
    func addAffirmation(_ affirmation: Affirmation) async throws {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
    }
    
    func deleteAffirmation(_ affirmation: Affirmation) async throws {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
    }
    
    func toggleFavorite(_ affirmation: Affirmation) async throws {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
    }
}

private class MockProgressTracker: ProgressTrackerProtocol {
    var shouldSucceed = true
    
    func loadProgress() async throws -> Progress {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
        return Progress(
            completedRepetitions: 5,
            dailyGoal: 10,
            lastUpdated: Date()
        )
    }
    
    func recordProgress(for affirmation: Affirmation) async throws {
        if !shouldSucceed {
            throw NSError(domain: "Test", code: 1)
        }
    }
} 
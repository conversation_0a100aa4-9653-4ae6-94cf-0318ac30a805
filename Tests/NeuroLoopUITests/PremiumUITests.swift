import XCTest

@available(iOS 17.0, macOS 14.0, *)
final class PremiumUITests: XCTestCase {
    var app: XCUIApplication!

    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launchArguments += ["-UITestMode", "-BypassOnboarding", "-BypassLogin"]
        app.launch()
    }

    override func tearDownWithError() throws {
        app = nil
    }

    // MARK: - Premium Feature Access
    func testPremiumFeatureVisibilityForPremiumUser() {
        app.launchArguments += ["-PremiumUser"]
        app.launch()
        app.tabBars.buttons["Premium"].tap()
        XCTAssertTrue(app.staticTexts["Premium Content"].exists)
        XCTAssertFalse(app.staticTexts["Upgrade to Premium"].exists)
    }

    func testPremiumFeatureVisibilityForNonPremiumUser() {
        app.launchArguments += ["-NonPremiumUser"]
        app.launch()
        app.tabBars.buttons["Premium"].tap()
        XCTAssertTrue(app.staticTexts["Upgrade to Premium"].exists)
        XCTAssertFalse(app.staticTexts["Premium Content"].exists)
    }

    func testPremiumFeatureFunctionality() {
        app.launchArguments += ["-PremiumUser"]
        app.launch()
        app.tabBars.buttons["Premium"].tap()
        let featureButton = app.buttons["Exclusive Feature"]
        if featureButton.exists {
            featureButton.tap()
            XCTAssertTrue(app.staticTexts["Feature Activated"].exists)
        }
    }

    // MARK: - Subscription Management
    func testSubscriptionPurchaseFlow() {
        app.tabBars.buttons["Premium"].tap()
        let subscribeButton = app.buttons["Subscribe"]
        if subscribeButton.exists {
            subscribeButton.tap()
            let purchaseAlert = app.alerts["Purchase Successful"]
            XCTAssertTrue(purchaseAlert.waitForExistence(timeout: 3))
            purchaseAlert.buttons["OK"].tap()
        }
    }

    func testSubscriptionTierSelection() {
        app.tabBars.buttons["Premium"].tap()
        let tierPicker = app.pickers["Subscription Tier"]
        if tierPicker.exists {
            tierPicker.adjust(toPickerWheelValue: "Annual")
            let subscribeButton = app.buttons["Subscribe"]
            if subscribeButton.exists {
                subscribeButton.tap()
                let purchaseAlert = app.alerts["Purchase Successful"]
                XCTAssertTrue(purchaseAlert.waitForExistence(timeout: 3))
                purchaseAlert.buttons["OK"].tap()
            }
        }
    }

    func testSubscriptionCancellationFlow() {
        app.tabBars.buttons["Premium"].tap()
        let cancelButton = app.buttons["Cancel Subscription"]
        if cancelButton.exists {
            cancelButton.tap()
            let cancelAlert = app.alerts["Subscription Cancelled"]
            XCTAssertTrue(cancelAlert.waitForExistence(timeout: 3))
            cancelAlert.buttons["OK"].tap()
        }
    }

    // MARK: - Purchase Restoration
    func testRestorePurchases() {
        app.tabBars.buttons["Premium"].tap()
        let restoreButton = app.buttons["Restore Purchases"]
        if restoreButton.exists {
            restoreButton.tap()
            let restoreAlert = app.alerts["Restore Successful"]
            XCTAssertTrue(restoreAlert.waitForExistence(timeout: 3))
            restoreAlert.buttons["OK"].tap()
        }
    }

    func testReceiptValidationFailure() {
        app.launchArguments += ["-SimulateReceiptValidationFailure"]
        app.launch()
        app.tabBars.buttons["Premium"].tap()
        let restoreButton = app.buttons["Restore Purchases"]
        if restoreButton.exists {
            restoreButton.tap()
            let errorAlert = app.alerts["Receipt Invalid"]
            XCTAssertTrue(errorAlert.waitForExistence(timeout: 3))
            errorAlert.buttons["OK"].tap()
        }
    }

    // MARK: - Premium UI Elements
    func testPremiumBadgesAndIndicators() {
        app.tabBars.buttons["Library"].tap()
        XCTAssertTrue(app.images["PremiumBadge"].exists)
    }

    func testPremiumOnboardingFlow() {
        app.launchArguments += ["-FirstLaunchWithPremium"]
        app.launch()
        let onboardingScreen = app.staticTexts["Welcome to Premium"]
        XCTAssertTrue(onboardingScreen.exists)
        app.buttons["Continue"].tap()
    }

    func testPremiumSettingsOptions() {
        app.tabBars.buttons["Settings"].tap()
        let premiumSettingsCell = app.tables.staticTexts["Premium Settings"]
        XCTAssertTrue(premiumSettingsCell.exists)
    }

    // MARK: - Error Handling
    func testNetworkErrorDuringPurchase() {
        app.launchArguments += ["-SimulateNetworkError"]
        app.launch()
        app.tabBars.buttons["Premium"].tap()
        let subscribeButton = app.buttons["Subscribe"]
        if subscribeButton.exists {
            subscribeButton.tap()
            let errorAlert = app.alerts["Network Error"]
            XCTAssertTrue(errorAlert.waitForExistence(timeout: 3))
            errorAlert.buttons["OK"].tap()
        }
    }

    func testInterruptedPurchaseFlow() {
        app.launchArguments += ["-SimulateInterruptedPurchase"]
        app.launch()
        app.tabBars.buttons["Premium"].tap()
        let subscribeButton = app.buttons["Subscribe"]
        if subscribeButton.exists {
            subscribeButton.tap()
            let errorAlert = app.alerts["Purchase Interrupted"]
            XCTAssertTrue(errorAlert.waitForExistence(timeout: 3))
            errorAlert.buttons["OK"].tap()
        }
    }

    // MARK: - Persistence
    func testPremiumStatusPersistenceAcrossAppRestarts() {
        app.launchArguments += ["-PremiumUser"]
        app.launch()
        app.tabBars.buttons["Premium"].tap()
        XCTAssertTrue(app.staticTexts["Premium Content"].exists)
        app.terminate()
        app.launch()
        app.tabBars.buttons["Premium"].tap()
        XCTAssertTrue(app.staticTexts["Premium Content"].exists)
    }

    // MARK: - Accessibility
    func testPremiumAccessibilityIdentifiers() {
        app.tabBars.buttons["Premium"].tap()
        XCTAssertTrue(app.staticTexts["Premium Content"].exists)
        XCTAssertTrue(app.buttons["Subscribe"].exists)
        XCTAssertTrue(app.buttons["Restore Purchases"].exists)
    }
} 
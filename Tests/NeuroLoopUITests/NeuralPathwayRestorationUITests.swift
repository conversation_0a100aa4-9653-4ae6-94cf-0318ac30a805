import XCTest

final class NeuralPathwayRestorationUITests: XCTestCase {
    let app = XCUIApplication()
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app.launchArguments.append("-UITest_RestorationFlow")
        app.launch()
    }
    
    func testRestorationFlowPresentationAndUIElements() {
        let restorationTitle = app.staticTexts[NSLocalizedString("Neural Pathway Restoration", comment: "Restoration title")]
        XCTAssertTrue(restorationTitle.waitForExistence(timeout: 3))
        XCTAssertTrue(app.staticTexts[NSLocalizedString("Neural Pathway Visualization", comment: "Visualization label")].exists)
        XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS[c] %@", NSLocalizedString("You've strengthened this pathway", comment: "Neural strength progress"))).element.exists)
        XCTAssertTrue(app.segmentedControls.firstMatch.exists)
        XCTAssertTrue(app.staticTexts[app.segmentedControls.buttons.element(boundBy: 0).label].exists)
        let learnMore = app.buttons[NSLocalizedString("Learn More", comment: "Learn more section label")]
        XCTAssertTrue(learnMore.exists)
        XCTAssertTrue(app.staticTexts[NSLocalizedString("Science Behind This", comment: "Science section title")].exists)
        XCTAssertTrue(app.buttons[NSLocalizedString("Restart Cycle", comment: "Restart cycle button")].exists)
    }
    
    func testTierSelectionAndContentChange() {
        let picker = app.segmentedControls.firstMatch
        let basic = picker.buttons.element(boundBy: 0)
        let intermediate = picker.buttons.element(boundBy: 1)
        let advanced = picker.buttons.element(boundBy: 2)
        XCTAssertTrue(basic.exists && intermediate.exists && advanced.exists)
        intermediate.tap()
        XCTAssertTrue(app.staticTexts[intermediate.label].exists)
        advanced.tap()
        XCTAssertTrue(app.staticTexts[advanced.label].exists)
        basic.tap()
        XCTAssertTrue(app.staticTexts[basic.label].exists)
    }
    
    func testLearnMoreExpansion() {
        let learnMore = app.buttons[NSLocalizedString("Learn More", comment: "Learn more section label")]
        XCTAssertTrue(learnMore.exists)
        learnMore.tap()
        // Check for at least one fact containing 'brain'
        let fact = app.staticTexts.containing(NSPredicate(format: "label CONTAINS[c] %@", "brain")).firstMatch
        XCTAssertTrue(fact.waitForExistence(timeout: 2))
    }
    
    func testRestartButtonAndDismissal() {
        let restart = app.buttons[NSLocalizedString("Restart Cycle", comment: "Restart cycle button")]
        XCTAssertTrue(restart.exists)
        restart.tap()
        let restorationTitle = app.staticTexts[NSLocalizedString("Neural Pathway Restoration", comment: "Restoration title")]
        XCTAssertFalse(restorationTitle.waitForExistence(timeout: 2))
    }
    
    func testAccessibilityLabelsAndDynamicType() {
        let picker = app.segmentedControls.firstMatch
        XCTAssertTrue(picker.exists)
        // Compare .label property for accessibility
        XCTAssertEqual(picker.label, NSLocalizedString("Choose content depth", comment: "Accessibility: content tier picker"))
        // Dynamic type: placeholder for actual implementation
    }
    
    func testEdgeCases_MultipleBrokenCyclesAndNeuralStrengths() {
        // Check for neuralStrength = 0%
        let zero = app.staticTexts.containing(NSPredicate(format: "label CONTAINS[c] %@", "0%"))
        XCTAssertTrue(zero.firstMatch.exists)
        // Check for neuralStrength = 99%
        let ninetyNine = app.staticTexts.containing(NSPredicate(format: "label CONTAINS[c] %@", "99%"))
        XCTAssertTrue(ninetyNine.firstMatch.exists)
        // Check for neuralStrength = 50%
        let fifty = app.staticTexts.containing(NSPredicate(format: "label CONTAINS[c] %@", "50%"))
        XCTAssertTrue(fifty.firstMatch.exists)
    }
} 